local desc = {
  ABTestInfo = {
    disable = {
    },
    GroupId = {
    },
    guideCount = {
    },
    testType = {
    },
  },
  ABTestSetting = {
    abtestId = {
    },
    settingVal = {
    },
  },
  Accessories = {
    accessoriesId = {
    },
    accessoriesUUId = {
    },
    vehicleItemId = {
    },
  },
  AccumulateBlessingsInfo = {
    assistCnt = {
    },
    assistFriends = {
      isAry = true,
    },
    assistFriends_is_cleared = {
      isAryClear = true,
      targetFieldName = "assistFriends",
    },
    blessingValue = {
    },
    invitationId = {
    },
    lastLoginDay = {
    },
    loginCumulative = {
    },
    loginRewardStatus = {
      isAry = true,
    },
    loginRewardStatus_is_cleared = {
      isAryClear = true,
      targetFieldName = "loginRewardStatus",
    },
    onlineRewardStatus = {
    },
    onlineTimeMs = {
    },
  },
  AchievementCompleteInfo = {
    id = {
    },
    stageInfo = {
      isMap = true,
      keyName = "stageId",
    },
    stageInfo_deleted = {
      isMapDel = true,
      targetFieldName = "stageInfo",
    },
  },
  AchievementInfo = {
    achievementComplete = {
      isMap = true,
      keyName = "id",
    },
    achievementComplete_deleted = {
      isMapDel = true,
      targetFieldName = "achievementComplete",
    },
    achievements = {
      isMap = true,
      keyName = "id",
    },
    achievements_deleted = {
      isMapDel = true,
      targetFieldName = "achievements",
    },
  },
  AchievementStageCompleteInfo = {
    rewarded = {
    },
    stageId = {
    },
    time = {
    },
  },
  ActiveSuitTypeBook = {
    activeSuitBooks = {
      isAry = true,
    },
    activeSuitBooks_is_cleared = {
      isAryClear = true,
      targetFieldName = "activeSuitBooks",
    },
    type = {
    },
  },
  Activity = {
    activityUnits = {
      isMap = true,
      keyName = "id",
    },
    activityUnits_deleted = {
      isMapDel = true,
      targetFieldName = "activityUnits",
    },
  },
  ActivityAddNewAidInfo = {
    configVersion = {
    },
    toAddNewIndex = {
    },
  },
  ActivityCenter = {
    activity = {
      isMsg = true,
    },
  },
  ActivityDetail = {
    accumulateBlessingsData = {
      isMsg = true,
    },
    amusementParkData = {
      isMsg = true,
    },
    animalHandbookData = {
      isMsg = true,
    },
    anniversaryMoba = {
      isMsg = true,
    },
    bookOfFriends = {
      isMsg = true,
    },
    captureShadowData = {
      isMsg = true,
    },
    checkInPlanData = {
      isMsg = true,
    },
    clubChallengeActivityData = {
      isMsg = true,
    },
    commonArrayList = {
      isMsg = true,
    },
    commonIntValue = {
    },
    competitionWarmUpAttr = {
      isMsg = true,
    },
    conanIpActiveData = {
      isMsg = true,
    },
    conanWarmupData = {
      isMsg = true,
    },
    danceOutfitData = {
      isMsg = true,
    },
    depositData = {
      isMsg = true,
    },
    doubleDiamondActivityData = {
      isMsg = true,
    },
    farmAnwserData = {
      isMsg = true,
    },
    farmBuffWish = {
      isMsg = true,
    },
    farmDailyAwardActivity = {
      isMsg = true,
    },
    farmDragonData = {
      isMsg = true,
    },
    farmReturningTask = {
      isMsg = true,
    },
    fashionFundData = {
      isMsg = true,
    },
    featureIntegrationData = {
      isMsg = true,
    },
    findPartnerData = {
      isMsg = true,
    },
    fishingFameData = {
      isMsg = true,
    },
    fishingHallOfFameData = {
      isMsg = true,
    },
    flashRaceCheeringDate = {
      isMsg = true,
    },
    flyingChessActivityInfo = {
      isMsg = true,
    },
    foodFestivalData = {
      isMsg = true,
    },
    groupingReturnData = {
      isMsg = true,
    },
    hynActivityData = {
      isMsg = true,
    },
    hywarmupActiviyData = {
      isMsg = true,
    },
    inflateRedPacket = {
      isMsg = true,
    },
    intelligenceStationData = {
      isMsg = true,
    },
    interServerGiftData = {
      isMsg = true,
    },
    kungFuPandaData = {
      isMsg = true,
    },
    levelUpChallenge = {
      isMsg = true,
    },
    lotteryDrawData = {
      isMsg = true,
    },
    luckyBalloonData = {
      isMsg = true,
    },
    luckyMoneyData = {
      isMsg = true,
    },
    luckyRebateData = {
      isMsg = true,
    },
    luckyStarData = {
      isMsg = true,
    },
    luckyTurntableData = {
      isMsg = true,
    },
    minesweeperActivityData = {
      isMsg = true,
    },
    mobaChallengeData = {
      isMsg = true,
    },
    mobaRandomVote = {
      isMsg = true,
    },
    mobaSquadDrawRedPacketData = {
      isMsg = true,
    },
    monopolyActivityData = {
      isMsg = true,
    },
    musicOrderData = {
      isMsg = true,
    },
    newYearSign = {
      isMsg = true,
    },
    oneDollarRaffleActivity = {
      isMsg = true,
    },
    paidUnlockActivityData = {
      isMsg = true,
    },
    pickFactionActivityData = {
      isMsg = true,
    },
    prayerCardData = {
      isMsg = true,
    },
    puzzleData = {
      isMsg = true,
    },
    qingShuangTrialData = {
      isMsg = true,
    },
    recruitData = {
      isMsg = true,
    },
    redPacketData = {
      isMsg = true,
    },
    restaurantThemed = {
      isMsg = true,
    },
    scoreGuideData = {
      isMsg = true,
    },
    scratchOffTicketsData = {
      isMsg = true,
    },
    shareActiveData = {
      isMsg = true,
    },
    springBlessingCollection = {
      isMsg = true,
    },
    springPrayActivityData = {
      isMsg = true,
    },
    springSlipData = {
      isMsg = true,
    },
    stickerData = {
      isMsg = true,
    },
    summerFlashMobData = {
      isMsg = true,
    },
    summerNavigationBarData = {
      isMsg = true,
    },
    summerVacationBPData = {
      isMsg = true,
    },
    superCoreRankData = {
      isMsg = true,
    },
    superLinearRedeem = {
      isMsg = true,
    },
    takeawayData = {
      isMsg = true,
    },
    teamRankData = {
      isMsg = true,
    },
    testActTeamData = {
      isMsg = true,
    },
    themeAdventureData = {
      isMsg = true,
    },
    timeLimitedCheckInData = {
      isMsg = true,
    },
    travelingDogData = {
      isMsg = true,
    },
    treasureHunt = {
      isMsg = true,
    },
    treasureLevelUpData = {
      isMsg = true,
    },
    twoPeopleSquad = {
      isMsg = true,
    },
    ultramanThemeData = {
      isMsg = true,
    },
    ultramanThemeTeam = {
      isMsg = true,
    },
    upgradeCheckInManualData = {
      isMsg = true,
    },
    useItemShareActivityData = {
      isMsg = true,
    },
    wealthBankData = {
      isMsg = true,
    },
    weekenIceBrokenData = {
      isMsg = true,
    },
    werewolfFullReducedConfData = {
      isMsg = true,
    },
    wishActivityData = {
      isMsg = true,
    },
    wishingTreeActivityData = {
      isMsg = true,
    },
    wolfReturnData = {
      isMsg = true,
    },
    wolfTeamChestData = {
      isMsg = true,
    },
  },
  ActivityFarmSquadData = {
    buffActivated = {
      isSet = true,
    },
    buffActivated_deleted = {
      isSetDel = true,
      targetFieldName = "buffActivated",
    },
    luckyFlag = {
    },
    rewardHistory = {
      isMap = true,
      keyName = "uid",
    },
    rewardHistory_deleted = {
      isMapDel = true,
      targetFieldName = "rewardHistory",
    },
    rewardTreeLevel = {
    },
    statisticsData = {
      isMap = true,
      keyName = "k",
    },
    statisticsData_deleted = {
      isMapDel = true,
      targetFieldName = "statisticsData",
    },
  },
  ActivityFindPartnerData = {
    trophyData = {
      isMap = true,
      keyName = "uid",
    },
    trophyData_deleted = {
      isMapDel = true,
      targetFieldName = "trophyData",
    },
  },
  ActivityFinished = {
    finishTimeSec = {
    },
    id = {
    },
  },
  ActivityFishGuaranteedRecord = {
    activityId = {
    },
    cnt = {
    },
    guaranteed = {
    },
  },
  ActivityGroupPhotoData = {
    groupPhotoUrl = {
    },
    name = {
    },
  },
  ActivityHistoryData = {
    squadData = {
      isMap = true,
      keyName = "activityId",
    },
    squadData_deleted = {
      isMapDel = true,
      targetFieldName = "squadData",
    },
    updateForesightSubscribed = {
      isSet = true,
    },
    updateForesightSubscribed_deleted = {
      isSetDel = true,
      targetFieldName = "updateForesightSubscribed",
    },
  },
  ActivityHistoryPlusData = {
    squadData = {
      isMap = true,
      keyName = "groupType",
    },
    squadData_deleted = {
      isMapDel = true,
      targetFieldName = "squadData",
    },
    updateForesightSubscribed = {
      isSet = true,
    },
    updateForesightSubscribed_deleted = {
      isSetDel = true,
      targetFieldName = "updateForesightSubscribed",
    },
  },
  ActivityInflateRedPacketData = {
    memberData = {
      isMap = true,
      keyName = "uid",
    },
    memberData_deleted = {
      isMapDel = true,
      targetFieldName = "memberData",
    },
    unlockedUidList = {
      isAry = true,
    },
    unlockedUidList_is_cleared = {
      isAryClear = true,
      targetFieldName = "unlockedUidList",
    },
  },
  ActivityLabel = {
    content = {
    },
    id = {
    },
    labelLevel = {
    },
  },
  ActivityModule = {
    moduleData = {
      isMsg = true,
    },
    moduleType = {
    },
  },
  ActivityModuleData = {
    lotteryData = {
      isMsg = true,
    },
    quizData = {
      isMsg = true,
    },
  },
  ActivityPlayerRewardHistory = {
    rewardHistory = {
      isMap = true,
      keyName = "rewardId",
    },
    rewardHistory_deleted = {
      isMapDel = true,
      targetFieldName = "rewardHistory",
    },
    uid = {
    },
  },
  ActivityPuzzleData = {
    puzzles = {
      isMap = true,
      keyName = "puzzleId",
    },
    puzzles_deleted = {
      isMapDel = true,
      targetFieldName = "puzzles",
    },
  },
  ActivityPuzzleInfo = {
    completed = {
    },
    puzzleId = {
    },
    stageRewardIds = {
      isSet = true,
    },
    stageRewardIds_deleted = {
      isSetDel = true,
      targetFieldName = "stageRewardIds",
    },
    uncoverIndices = {
      isSet = true,
    },
    uncoverIndices_deleted = {
      isSetDel = true,
      targetFieldName = "uncoverIndices",
    },
  },
  ActivityRaffle = {
    runningRaffles = {
      isMap = true,
      keyName = "raffleId",
    },
    runningRaffles_deleted = {
      isMapDel = true,
      targetFieldName = "runningRaffles",
    },
  },
  ActivityRaffleInfo = {
    costs = {
      isMap = true,
      keyName = "coinType",
    },
    costs_deleted = {
      isMapDel = true,
      targetFieldName = "costs",
    },
    data = {
      isMsg = true,
    },
    raffleId = {
    },
  },
  ActivityRedDot = {
    redDotIds = {
      isSet = true,
    },
    redDotIds_deleted = {
      isSetDel = true,
      targetFieldName = "redDotIds",
    },
    redDotType = {
    },
  },
  ActivityRewardDetail = {
    rewardId = {
    },
    rewardTimeMs = {
    },
    rewardType = {
    },
  },
  ActivitySquadData = {
    farmSquadData = {
      isMsg = true,
    },
    findPartnerData = {
      isMsg = true,
    },
    groupingReturnData = {
      isMsg = true,
    },
    groupPhotoData = {
      isMsg = true,
    },
    groupTrophyTaskData = {
      isMsg = true,
    },
    groupWolfKillTrophyData = {
      isMsg = true,
    },
    inflateRedPacket = {
      isMsg = true,
    },
    mobaSquadDrawRedPacketData = {
      isMsg = true,
    },
    twoPeopleSquad = {
      isMsg = true,
    },
  },
  ActivitySquadDetail = {
    activityId = {
    },
    activityType = {
    },
    extraData = {
      isMsg = true,
    },
    lastUpdateTimeMs = {
    },
    leaderUid = {
    },
    members = {
      isMap = true,
      keyName = "uid",
    },
    members_deleted = {
      isMapDel = true,
      targetFieldName = "members",
    },
    phrase = {
    },
    squadCreateTimeMs = {
    },
    squadId = {
    },
    sumData = {
      isMap = true,
      keyName = "k",
    },
    sumData_deleted = {
      isMapDel = true,
      targetFieldName = "sumData",
    },
  },
  ActivitySquadInfo = {
    accumulatePoint = {
    },
    activityNo = {
    },
    dailyPoint = {
    },
    squadId = {
    },
  },
  ActivityStatus = {
    beginTimeSec = {
    },
    endTimeSec = {
    },
    fsmStatus = {
    },
  },
  ActivityTaskCompleteInfo = {
    taskList = {
      isMap = true,
      keyName = "taskId",
    },
    taskList_deleted = {
      isMapDel = true,
      targetFieldName = "taskList",
    },
  },
  ActivityTrophyData = {
    trophyCnt = {
    },
    uid = {
    },
  },
  ActivityTrophyTaskCompleteData = {
    taskData = {
      isMap = true,
      keyName = "taskId",
    },
    taskData_deleted = {
      isMapDel = true,
      targetFieldName = "taskData",
    },
    trophyCnt = {
    },
  },
  ActivityTwoPeopleSquadData = {
    memberData = {
      isMap = true,
      keyName = "uid",
    },
    memberData_deleted = {
      isMapDel = true,
      targetFieldName = "memberData",
    },
    memberTaskData = {
      isMap = true,
      keyName = "taskId",
    },
    memberTaskData_deleted = {
      isMapDel = true,
      targetFieldName = "memberTaskData",
    },
  },
  ActivityUnit = {
    clearRedDotInfo = {
      isMsg = true,
    },
    clickRedDotInfo = {
      isMap = true,
      keyName = "redDotType",
    },
    clickRedDotInfo_deleted = {
      isMapDel = true,
      targetFieldName = "clickRedDotInfo",
    },
    createTimeMs = {
    },
    detailData = {
      isMsg = true,
    },
    id = {
    },
    isHiddenInCli = {
    },
    isRead = {
    },
    modules = {
      isMap = true,
      keyName = "moduleType",
    },
    modules_deleted = {
      isMapDel = true,
      targetFieldName = "modules",
    },
    redDotShow = {
    },
    taskCompleteInfo = {
      isMsg = true,
    },
  },
  ActivityWolfKillSquadTrophyData = {
    taskGainTrophyCnt = {
    },
    trophyCnt = {
    },
  },
  AiChatInfo = {
    enableAigcNpc = {
    },
    enableToneChat = {
    },
    finishGuide = {
    },
    license = {
    },
    PalLicense = {
    },
    sessions = {
      isMap = true,
      keyName = "roleId",
    },
    sessions_deleted = {
      isMapDel = true,
      targetFieldName = "sessions",
    },
  },
  AiChatSession = {
    actionState = {
    },
    dailyAction = {
    },
    dailyMoodPunishTotal = {
    },
    dailyTouch = {
    },
    dressInfo = {
      isMap = true,
      keyName = "dressUpType",
    },
    dressInfo_deleted = {
      isMapDel = true,
      targetFieldName = "dressInfo",
    },
    labRoleId = {
    },
    lastActionTime = {
    },
    lastGenTime = {
    },
    lastTouchPart = {
    },
    mood = {
    },
    npcId = {
    },
    playerProfile = {
      isMsg = true,
    },
    profile = {
      isMsg = true,
    },
    puzzleInfo = {
      isMap = true,
      keyName = "puzzleId",
    },
    puzzleInfo_deleted = {
      isMapDel = true,
      targetFieldName = "puzzleInfo",
    },
    renderId = {
    },
    roleId = {
    },
    round = {
    },
    touchSamePart = {
    },
    validChatTimes = {
    },
  },
  AigcCountLimit = {
    lastRefreshDailyCountTime = {
    },
    todayGenAnicapCount = {
    },
    todayGenImageCount = {
    },
    todayGenVoiceCount = {
    },
  },
  AigcNpcModifyTime = {
    lastTimeSec = {
    },
    type = {
    },
  },
  AigcNpcPlayerProfile = {
    birthday = {
      isMsg = true,
    },
    city = {
    },
    gender = {
    },
    lastModifyTimeSec = {
      isMap = true,
      keyName = "type",
    },
    lastModifyTimeSec_deleted = {
      isMapDel = true,
      targetFieldName = "lastModifyTimeSec",
    },
    matchTypes = {
      isSet = true,
    },
    matchTypes_deleted = {
      isSetDel = true,
      targetFieldName = "matchTypes",
    },
    mbti = {
    },
    name = {
    },
  },
  AigcNpcProfile = {
    birthday = {
      isMsg = true,
    },
    career = {
    },
    claim = {
    },
    gender = {
    },
    labNpcId = {
    },
    name = {
    },
    personality = {
      isSet = true,
    },
    personality_deleted = {
      isSetDel = true,
      targetFieldName = "personality",
    },
    playerNick = {
    },
    timbre = {
    },
    title = {
    },
    wishContent = {
    },
  },
  AigcNpcPuzzleInfo = {
    completed = {
    },
    puzzleId = {
    },
  },
  AiNpcAttr = {
    lastOnlineTime = {
    },
    name = {
    },
    npcData = {
      isMap = true,
      keyName = "roleId",
    },
    npcData_deleted = {
      isMapDel = true,
      targetFieldName = "npcData",
    },
    openId = {
    },
    uid = {
    },
  },
  AiNpcData = {
    actionState = {
    },
    dressInfo = {
      isSet = true,
    },
    dressInfo_deleted = {
      isSetDel = true,
      targetFieldName = "dressInfo",
    },
    friendChatData = {
      isMsg = true,
    },
    labNpcId = {
    },
    labRoleId = {
    },
    mood = {
    },
    playerProfile = {
      isMsg = true,
    },
    profile = {
      isMsg = true,
    },
    pushRate = {
      isMap = true,
      keyName = "rateType",
    },
    pushRate_deleted = {
      isMapDel = true,
      targetFieldName = "pushRate",
    },
    roleId = {
    },
  },
  AiNpcFriendChatData = {
    hasSayGoodMoodTriggered = {
    },
    hasSayHiTriggered = {
    },
    lastNpcBirthdayMs = {
    },
    lastPlayerBirthdayMs = {
    },
    lastPlayerReqMs = {
    },
    procTickTimeSnapMs = {
    },
    randomChatData = {
      isMsg = true,
    },
  },
  AiNpcFriendRandomChatData = {
    lastTriggeredMs = {
    },
    tryTriggerMs = {
    },
    tryTriggerProbability = {
    },
  },
  AiNpcPushRateData = {
    curValue = {
    },
    dailyValue = {
    },
    rateType = {
    },
  },
  AlbumExtInfo = {
    albumLimitInfo = {
      isMsg = true,
    },
    albumPicExtInfo = {
      isMap = true,
      keyName = "picKey",
    },
    albumPicExtInfo_deleted = {
      isMapDel = true,
      targetFieldName = "albumPicExtInfo",
    },
    picTargetAtInfo = {
      isMap = true,
      keyName = "picKey",
    },
    picTargetAtInfo_deleted = {
      isMapDel = true,
      targetFieldName = "picTargetAtInfo",
    },
  },
  AlbumInfo = {
    albumPicMap = {
      isMap = true,
      keyName = "picKey",
    },
    albumPicMap_deleted = {
      isMapDel = true,
      targetFieldName = "albumPicMap",
    },
    albumPicWallInfo = {
      isMsg = true,
    },
    dataMigrateTimeSec = {
    },
    dataVersion = {
    },
    isDataMigrate = {
    },
  },
  AlbumLikeHisInfo = {
    AlbumPicLikeHis = {
      isMap = true,
      keyName = "picKey",
    },
    AlbumPicLikeHis_deleted = {
      isMapDel = true,
      targetFieldName = "AlbumPicLikeHis",
    },
    dataVersion = {
    },
  },
  AlbumLimitConditionInfo = {
    curValue = {
    },
    id = {
    },
  },
  AlbumLimitInfo = {
    albumLimitConditionMap = {
      isMap = true,
      keyName = "id",
    },
    albumLimitConditionMap_deleted = {
      isMapDel = true,
      targetFieldName = "albumLimitConditionMap",
    },
    totalUpperLimit = {
    },
  },
  AlbumPicAtTargetInfo = {
    atTime = {
    },
    uid = {
    },
  },
  AlbumPicExtInfo = {
    atTargetUidSet = {
      isMap = true,
      keyName = "uid",
    },
    atTargetUidSet_deleted = {
      isMapDel = true,
      targetFieldName = "atTargetUidSet",
    },
    picKey = {
    },
  },
  AlbumPicInfo = {
    bucket = {
    },
    cosPath = {
    },
    createTimeSec = {
    },
    isChecking = {
    },
    isHide = {
    },
    isLike = {
    },
    labelType = {
    },
    likeCount = {
    },
    picKey = {
    },
    picLike = {
      isMap = true,
      keyName = "uid",
    },
    picLike_deleted = {
      isMapDel = true,
      targetFieldName = "picLike",
    },
    sceneTag = {
      isSet = true,
    },
    sceneTag_deleted = {
      isSetDel = true,
      targetFieldName = "sceneTag",
    },
    sceneTagStr = {
    },
    sourceScene = {
    },
    thumbnailCosPath = {
    },
    topTime = {
    },
  },
  AlbumPicLikeHisInfo = {
    likeCount = {
    },
    picKey = {
    },
    picLikeHis = {
      isMap = true,
      keyName = "uid",
    },
    picLikeHis_deleted = {
      isMapDel = true,
      targetFieldName = "picLikeHis",
    },
  },
  AlbumPicNewLikeHis = {
    picKey = {
    },
    time = {
    },
    uid = {
    },
  },
  AlbumPicTargetAtInfo = {
    atTime = {
    },
    picKey = {
    },
    uid = {
    },
  },
  AlbumPicWallInfo = {
    myPicList = {
      isMap = true,
      keyName = "picKey",
    },
    myPicList_deleted = {
      isMapDel = true,
      targetFieldName = "myPicList",
    },
  },
  AlgoInfo = {
    expTag = {
    },
    recId = {
    },
  },
  AlgoRecommendMatchInfo = {
    expTag = {
    },
    matchTypes = {
      isAry = true,
    },
    matchTypes_is_cleared = {
      isAryClear = true,
      targetFieldName = "matchTypes",
    },
    recId = {
    },
  },
  AllFriendInteractAttr = {
    dailyInteractAttr = {
      isMap = true,
      keyName = "uid",
    },
    dailyInteractAttr_deleted = {
      isMapDel = true,
      targetFieldName = "dailyInteractAttr",
    },
    dailyInteractRefreshTimeMs = {
    },
    dailyInteractStatistic = {
      isMap = true,
      keyName = "uid",
    },
    dailyInteractStatistic_deleted = {
      isMapDel = true,
      targetFieldName = "dailyInteractStatistic",
    },
  },
  AllIntimateRelationAttr = {
    blockRecommendUid = {
      isSet = true,
    },
    blockRecommendUid_deleted = {
      isSetDel = true,
      targetFieldName = "blockRecommendUid",
    },
    intimateRelationExtraCnt = {
    },
    motionAttr = {
      isMap = true,
      keyName = "uid",
    },
    motionAttr_deleted = {
      isMapDel = true,
      targetFieldName = "motionAttr",
    },
  },
  AmusementParkData = {
    limits = {
      isMap = true,
      keyName = "limitId",
    },
    limits_deleted = {
      isMapDel = true,
      targetFieldName = "limits",
    },
  },
  AmusementParkLimit = {
    lastRefreshTime = {
    },
    limitId = {
    },
    value = {
    },
  },
  AnimalHandbookActivityAttr = {
    animalGetInfo = {
      isMap = true,
      keyName = "animalId",
    },
    animalGetInfo_deleted = {
      isMapDel = true,
      targetFieldName = "animalGetInfo",
    },
    animalInfo = {
      isMap = true,
      keyName = "uniqueId",
    },
    animalInfo_deleted = {
      isMapDel = true,
      targetFieldName = "animalInfo",
    },
    colonyInfo = {
      isMap = true,
      keyName = "colonyId",
    },
    colonyInfo_deleted = {
      isMapDel = true,
      targetFieldName = "colonyInfo",
    },
    dayGiveTime = {
    },
    giveHistory = {
      isMap = true,
      keyName = "uniqueId",
    },
    giveHistory_deleted = {
      isMapDel = true,
      targetFieldName = "giveHistory",
    },
    isFinalPrizeRewarded = {
    },
    lastGiveTimeMs = {
    },
    receiveHistory = {
      isMap = true,
      keyName = "uniqueId",
    },
    receiveHistory_deleted = {
      isMapDel = true,
      targetFieldName = "receiveHistory",
    },
    rewardedAnimals = {
      isSet = true,
    },
    rewardedAnimals_deleted = {
      isSetDel = true,
      targetFieldName = "rewardedAnimals",
    },
    rewardedSpecies = {
      isSet = true,
    },
    rewardedSpecies_deleted = {
      isSetDel = true,
      targetFieldName = "rewardedSpecies",
    },
    weekGiveTime = {
    },
  },
  AnimalHandbookAnimalGetInfo = {
    animalId = {
    },
    noGetCount = {
    },
  },
  AnimalHandbookAnimalInfo = {
    animalId = {
    },
    canGive = {
    },
    getTimeMs = {
    },
    giverUid = {
    },
    uniqueId = {
    },
  },
  AnimalHandbookColonyInfo = {
    animalId = {
    },
    animalRepeatCount = {
    },
    colonyId = {
    },
    completeTimeMs = {
    },
    freeClaimCount = {
    },
    isFreeGet = {
    },
    speciesId = {
    },
    state = {
    },
  },
  AnimalHandbookGiveHistory = {
    animalInfo = {
      isMsg = true,
    },
    receiver = {
      isMsg = true,
    },
    uniqueId = {
    },
  },
  AnimalHandbookItemInfo = {
    itemId = {
    },
    itemNum = {
    },
  },
  AnimalHandbookPlayerInfo = {
    gender = {
    },
    headFrameId = {
    },
    headFrameType = {
    },
    headFrameUuid = {
    },
    name = {
    },
    openId = {
    },
    profile = {
    },
    uid = {
    },
  },
  AnimalHandbookReceiveHistory = {
    animalInfo = {
      isMsg = true,
    },
    giver = {
      isMsg = true,
    },
    uniqueId = {
    },
  },
  AnniversaryMoba = {
    bigRewardKey = {
    },
    drawRewardIndex = {
      isSet = true,
    },
    drawRewardIndex_deleted = {
      isSetDel = true,
      targetFieldName = "drawRewardIndex",
    },
  },
  AppearanceRoad = {
    activeSuitBookByType = {
      isMap = true,
      keyName = "type",
    },
    activeSuitBookByType_deleted = {
      isMapDel = true,
      targetFieldName = "activeSuitBookByType",
    },
    appearanceInfo = {
      isMap = true,
      keyName = "k",
    },
    appearanceInfo_deleted = {
      isMapDel = true,
      targetFieldName = "appearanceInfo",
    },
    gather = {
      isMap = true,
      keyName = "type",
    },
    gather_deleted = {
      isMapDel = true,
      targetFieldName = "gather",
    },
    lv = {
      isMap = true,
      keyName = "lv",
    },
    lv_deleted = {
      isMapDel = true,
      targetFieldName = "lv",
    },
    updateTimeMs = {
    },
  },
  AppearanceRoadGather = {
    awarded = {
      isSet = true,
    },
    awarded_deleted = {
      isSetDel = true,
      targetFieldName = "awarded",
    },
    collected = {
      isSet = true,
    },
    collected_deleted = {
      isSetDel = true,
      targetFieldName = "collected",
    },
    type = {
    },
  },
  AppearanceRoadLv = {
    awarded = {
      isSet = true,
    },
    awarded_deleted = {
      isSetDel = true,
      targetFieldName = "awarded",
    },
    lv = {
    },
  },
  AppearanceRoadShowInfo = {
    id = {
    },
    suitId = {
    },
    type = {
    },
  },
  ApplyData = {
    applyTime = {
    },
    cid = {
    },
  },
  ArenaCardGroupInfo = {
    cards = {
      isAry = true,
    },
    cards_is_cleared = {
      isAryClear = true,
      targetFieldName = "cards",
    },
    id = {
    },
    name = {
    },
  },
  ArenaCardPackBoxUpgradeHistory = {
    qualityRecord = {
      isMap = true,
      keyName = "quality",
    },
    qualityRecord_deleted = {
      isMapDel = true,
      targetFieldName = "qualityRecord",
    },
    seasonId = {
    },
  },
  ArenaCardPackBoxUpgradeRecord = {
    num = {
    },
    quality = {
    },
  },
  ArenaCardPackContentHistory = {
    onceIds = {
      isSet = true,
    },
    onceIds_deleted = {
      isSetDel = true,
      targetFieldName = "onceIds",
    },
    packId = {
    },
  },
  ArenaCardPackDrawHistory = {
    boxUpgradeHistory = {
      isMap = true,
      keyName = "seasonId",
    },
    boxUpgradeHistory_deleted = {
      isMapDel = true,
      targetFieldName = "boxUpgradeHistory",
    },
    packHistory = {
      isMap = true,
      keyName = "packId",
    },
    packHistory_deleted = {
      isMapDel = true,
      targetFieldName = "packHistory",
    },
    ruleHistory = {
      isMap = true,
      keyName = "ruleId",
    },
    ruleHistory_deleted = {
      isMapDel = true,
      targetFieldName = "ruleHistory",
    },
  },
  ArenaCardPackRuleHistory = {
    ruleId = {
    },
    seqRecord = {
      isMap = true,
      keyName = "seq",
    },
    seqRecord_deleted = {
      isMapDel = true,
      targetFieldName = "seqRecord",
    },
  },
  ArenaCardPackSeqRecord = {
    current = {
    },
    seq = {
    },
    total = {
    },
  },
  ArenaCardRegulationRecords = {
    matchTypeId = {
    },
    records = {
      isAry = true,
    },
    records_is_cleared = {
      isAryClear = true,
      targetFieldName = "records",
    },
  },
  ArenaDailyVictoryRecord = {
    dateKey = {
    },
    num = {
    },
  },
  ArenaGameData = {
    ceData = {
      isMap = true,
      keyName = "heroId",
    },
    ceData_deleted = {
      isMapDel = true,
      targetFieldName = "ceData",
    },
    combatEffectiveness = {
      isMsg = true,
    },
    isBigPackage = {
    },
    sevenDaysLoginActivity = {
      isMsg = true,
    },
    unlockData = {
      isMap = true,
      keyName = "heroId",
    },
    unlockData_deleted = {
      isMapDel = true,
      targetFieldName = "unlockData",
    },
  },
  ArenaGameInfo = {
    cardRegulationRecordsMap = {
      isMap = true,
      keyName = "matchTypeId",
    },
    cardRegulationRecordsMap_deleted = {
      isMapDel = true,
      targetFieldName = "cardRegulationRecordsMap",
    },
    cards = {
      isSet = true,
    },
    cards_deleted = {
      isSetDel = true,
      targetFieldName = "cards",
    },
    clientVersion64 = {
    },
    dailyVictoryRecord = {
      isMap = true,
      keyName = "dateKey",
    },
    dailyVictoryRecord_deleted = {
      isMapDel = true,
      targetFieldName = "dailyVictoryRecord",
    },
    drawHistory = {
      isMsg = true,
    },
    equips = {
      isSet = true,
    },
    equips_deleted = {
      isSetDel = true,
      targetFieldName = "equips",
    },
    heatPower = {
    },
    heatPowerActivityId = {
    },
    heatPowerRank = {
    },
    heroMap = {
      isMap = true,
      keyName = "heroId",
    },
    heroMap_deleted = {
      isMapDel = true,
      targetFieldName = "heroMap",
    },
    heroRoadComplete = {
    },
    heroStarDisable = {
    },
    heroStarGeneralInfo = {
      isMsg = true,
    },
    isAutoSetSprayPaint = {
    },
    items = {
      isAry = true,
    },
    items_is_cleared = {
      isAryClear = true,
      targetFieldName = "items",
    },
    limitedTimeFreeHeroMap = {
      isMap = true,
      keyName = "heroId",
    },
    limitedTimeFreeHeroMap_deleted = {
      isMapDel = true,
      targetFieldName = "limitedTimeFreeHeroMap",
    },
    moodSettings = {
      isMap = true,
      keyName = "posId",
    },
    moodSettings_deleted = {
      isMapDel = true,
      targetFieldName = "moodSettings",
    },
    openId = {
    },
    peekInfo = {
      isMsg = true,
    },
    profile = {
      isMsg = true,
    },
    pushRandomEventState = {
    },
    riftPower = {
    },
    selectedFrame = {
    },
    selectedVoiceStyle = {
    },
    sendHokExperienceCard = {
    },
    sevenDaysLoginActivity = {
      isMsg = true,
    },
    skins = {
      isSet = true,
    },
    skins_deleted = {
      isSetDel = true,
      targetFieldName = "skins",
    },
    sprayPaintSetting = {
      isMap = true,
      keyName = "posId",
    },
    sprayPaintSetting_deleted = {
      isMapDel = true,
      targetFieldName = "sprayPaintSetting",
    },
    stats = {
      isMsg = true,
    },
  },
  ArenaHeroCombatEffectiveness = {
    heroData = {
      isMap = true,
      keyName = "heroId",
    },
    heroData_deleted = {
      isMapDel = true,
      targetFieldName = "heroData",
    },
    seasonSettlement = {
      isMap = true,
      keyName = "qualifyType",
    },
    seasonSettlement_deleted = {
      isMapDel = true,
      targetFieldName = "seasonSettlement",
    },
    settlementTime = {
    },
    settlementTimeHOK = {
    },
  },
  ArenaHeroCombatEffectivenessData = {
    ceItems = {
      isMap = true,
      keyName = "group",
    },
    ceItems_deleted = {
      isMapDel = true,
      targetFieldName = "ceItems",
    },
    heroId = {
    },
  },
  ArenaHeroCombatEffectivenessDataItem = {
    activePoint = {
    },
    activePointUpdateTime = {
    },
    badgeId = {
    },
    battleScore = {
    },
    group = {
    },
    historyRank = {
      isMap = true,
      keyName = "geoLevel",
    },
    historyRank_deleted = {
      isMapDel = true,
      targetFieldName = "historyRank",
    },
    lastQualifyTime = {
    },
    lastWeekScore = {
    },
    latestRank = {
      isMap = true,
      keyName = "geoLevel",
    },
    latestRank_deleted = {
      isMapDel = true,
      targetFieldName = "latestRank",
    },
    perfScore = {
    },
  },
  ArenaHeroDynamicStat = {
    seasonStats = {
      isMap = true,
      keyName = "season",
    },
    seasonStats_deleted = {
      isMapDel = true,
      targetFieldName = "seasonStats",
    },
  },
  ArenaHeroInfo = {
    cardGroupID = {
    },
    cardGroups = {
      isMap = true,
      keyName = "id",
    },
    cardGroups_deleted = {
      isMapDel = true,
      targetFieldName = "cardGroups",
    },
    ceItems = {
      isMap = true,
      keyName = "group",
    },
    ceItems_deleted = {
      isMapDel = true,
      targetFieldName = "ceItems",
    },
    changeReason = {
    },
    duration = {
    },
    dynamicStat = {
      isMsg = true,
    },
    equips = {
      isAry = true,
    },
    equips_is_cleared = {
      isAryClear = true,
      targetFieldName = "equips",
    },
    getTime = {
    },
    heroId = {
    },
    heroSkinId = {
    },
    heroStarInfo = {
      isMsg = true,
    },
    presets = {
      isMsg = true,
    },
    selectedEquip = {
    },
    stats = {
      isMsg = true,
    },
    summonerSkillId = {
    },
  },
  ArenaHeroPresets = {
    summonerSkill = {
      isMap = true,
      keyName = "matchGroup",
    },
    summonerSkill_deleted = {
      isMapDel = true,
      targetFieldName = "summonerSkill",
    },
  },
  ArenaHeroPresetsSummonerSkill = {
    matchGroup = {
    },
    skillId = {
    },
  },
  ArenaHeroRankItem = {
    badgeId = {
    },
    geoCode = {
    },
    geoLevel = {
    },
    position = {
    },
    rankLevel = {
    },
    score = {
    },
    seasonId = {
    },
  },
  ArenaHeroSeasonStat = {
    season = {
    },
    stats = {
      isMap = true,
      keyName = "matchType",
    },
    stats_deleted = {
      isMapDel = true,
      targetFieldName = "stats",
    },
  },
  ArenaHeroStarGeneralInfo = {
    level = {
    },
    rewardedLevel = {
    },
    rewardedSet = {
      isSet = true,
    },
    rewardedSet_deleted = {
      isSetDel = true,
      targetFieldName = "rewardedSet",
    },
  },
  ArenaHeroStarInfo = {
    level = {
    },
    levelQuestCompleted = {
    },
    levelQuestTaskId = {
    },
    rewardedLevel = {
    },
    rewardedSet = {
      isSet = true,
    },
    rewardedSet_deleted = {
      isSetDel = true,
      targetFieldName = "rewardedSet",
    },
    star = {
    },
  },
  ArenaHeroStat = {
    dynamicStats = {
      isMap = true,
      keyName = "type",
    },
    dynamicStats_deleted = {
      isMapDel = true,
      targetFieldName = "dynamicStats",
    },
    matchType = {
    },
  },
  ArenaHeroStatInfo = {
    heroId = {
    },
    mvpCount = {
    },
    usedCount = {
    },
    winCount = {
    },
    zhanLi = {
    },
  },
  ArenaHeroStatKV = {
    type = {
    },
    value = {
    },
  },
  ArenaHeroUnlockData = {
    gameTimesStatistics = {
      isMsg = true,
    },
    heroId = {
    },
  },
  ArenaMatchStat = {
    dynamicResultStats = {
      isMap = true,
      keyName = "type",
    },
    dynamicResultStats_deleted = {
      isMapDel = true,
      targetFieldName = "dynamicResultStats",
    },
    heroStats = {
      isMap = true,
      keyName = "heroId",
    },
    heroStats_deleted = {
      isMapDel = true,
      targetFieldName = "heroStats",
    },
    matchType = {
    },
    stats = {
      isMap = true,
      keyName = "type",
    },
    stats_deleted = {
      isMapDel = true,
      targetFieldName = "stats",
    },
    teamStats = {
      isMap = true,
      keyName = "uid",
    },
    teamStats_deleted = {
      isMapDel = true,
      targetFieldName = "teamStats",
    },
  },
  ArenaMoodSetting = {
    itemId = {
    },
    posId = {
    },
  },
  ArenaPeakHistory = {
    comboWin = {
    },
    peakValue = {
    },
  },
  ArenaPeakInfo = {
    peakType = {
    },
    seasonInfo = {
      isMap = true,
      keyName = "season",
    },
    seasonInfo_deleted = {
      isMapDel = true,
      targetFieldName = "seasonInfo",
    },
  },
  ArenaPeakSeasonInfo = {
    comboWin = {
    },
    historyMax = {
      isMsg = true,
    },
    lastMatchTime = {
    },
    lastWinTime = {
    },
    peakPower = {
    },
    peakValue = {
    },
    season = {
    },
  },
  ArenaPlayerProfile = {
    headPic = {
    },
    headPicFrame = {
    },
    showSkin = {
    },
  },
  ArenaSeasonSettlementData = {
    oldQualifyInfo = {
      isMsg = true,
    },
    qualifyInfo = {
      isMsg = true,
    },
    qualifyType = {
    },
  },
  ArenaSeasonStat = {
    season = {
    },
    stats = {
      isMap = true,
      keyName = "matchType",
    },
    stats_deleted = {
      isMapDel = true,
      targetFieldName = "stats",
    },
  },
  ArenaSettings = {
    cameraControlType = {
    },
    cameraControlTypeBS = {
    },
    cameraControlTypeHZ = {
    },
    expandData = {
      isMap = true,
      keyName = "id",
    },
    expandData_deleted = {
      isMapDel = true,
      targetFieldName = "expandData",
    },
    indicatorAbsorbType = {
    },
    isAutoNormalAttack = {
    },
    isAutoNormalAttackBS = {
    },
    isAutoNormalAttackHZ = {
    },
    isLockTargetByIcon3V3 = {
    },
    isLockTargetByIcon5V5 = {
    },
    isLockTargetByIconBS = {
    },
    isLockTargetByIconHZ = {
    },
    isShowSkillTips = {
    },
    maxAngle = {
    },
    maxAngleBS = {
    },
    maxAngleHZ = {
    },
    minAngle = {
    },
    minAngleBS = {
    },
    minAngleHZ = {
    },
    showHeroPower = {
    },
    targetSelectionStrategy = {
    },
    targetSelectionStrategyBS = {
    },
    targetSelectionStrategyHZ = {
    },
  },
  ArenaSevenDaysLoginActivity = {
    activityId = {
    },
    beginMillis = {
    },
    EndedAndCleared = {
    },
    endMillis = {
    },
  },
  ArenaSprayPaintSetting = {
    itemId = {
    },
    itemNum = {
    },
    posId = {
    },
  },
  ArenaStat = {
    activeTime = {
    },
    continuouslyActive = {
    },
    historyStats = {
      isMap = true,
      keyName = "type",
    },
    historyStats_deleted = {
      isMapDel = true,
      targetFieldName = "historyStats",
    },
    hokWinningStreak = {
    },
    matchStats = {
      isMap = true,
      keyName = "matchType",
    },
    matchStats_deleted = {
      isMapDel = true,
      targetFieldName = "matchStats",
    },
    qualifyStats = {
      isMap = true,
      keyName = "season",
    },
    qualifyStats_deleted = {
      isMapDel = true,
      targetFieldName = "qualifyStats",
    },
    seasonStats = {
      isMap = true,
      keyName = "season",
    },
    seasonStats_deleted = {
      isMapDel = true,
      targetFieldName = "seasonStats",
    },
  },
  ArenaStatKV = {
    type = {
    },
    value = {
    },
  },
  ArenaTeamStatKV = {
    battleCount = {
    },
    uid = {
    },
  },
  ArenaTipInfo = {
    generousValue = {
    },
    popularValue = {
    },
  },
  AttrBirthdayBasicData = {
    lastBirthdayMonthDay = {
    },
    lastSettingEpochSecs = {
    },
    twoTimesAgoBirthdayMonthDay = {
    },
    twoTimesAgoSettingEpochSecs = {
    },
    unhandledBirthdayCardReceiveEpochSecs = {
    },
    unhandledBirthdayCardSendEpochSecs = {
    },
  },
  AttrBirthdayBlessingRecord = {
    birthdayCardData = {
      isMsg = true,
    },
    birthdayGift = {
      isMsg = true,
    },
    birthdayMonthDay = {
    },
    blessingConfId = {
    },
    blessingEpochMillis = {
    },
    blessingRecordId = {
    },
    blessingType = {
    },
    giveCommidityList = {
      isMap = true,
      keyName = "id",
    },
    giveCommidityList_deleted = {
      isMapDel = true,
      targetFieldName = "giveCommidityList",
    },
    isDeleted = {
    },
    mailId = {
    },
    senderUid = {
    },
  },
  AttrBirthdayCardData = {
    birthdayCardConfigId = {
    },
    blessingConfigId = {
    },
    cardEpochMillis = {
    },
    customContent = {
    },
  },
  AttrBirthdayCardItemData = {
    birthdayCardData = {
      isMsg = true,
    },
    birthdayCardId = {
    },
    itemId = {
    },
    itemUUID = {
    },
    mailId = {
    },
  },
  AttrBirthdayData = {
    birthdayBasic = {
      isMsg = true,
    },
    birthdayBlessingRecord = {
      isMap = true,
      keyName = "blessingRecordId",
    },
    birthdayBlessingRecord_deleted = {
      isMapDel = true,
      targetFieldName = "birthdayBlessingRecord",
    },
    birthdayCardItemData = {
      isMap = true,
      keyName = "birthdayCardId",
    },
    birthdayCardItemData_deleted = {
      isMapDel = true,
      targetFieldName = "birthdayCardItemData",
    },
    birthdayFriend = {
      isMsg = true,
    },
    blessingRecordIsRedDot = {
    },
    officialWelfare = {
      isMsg = true,
    },
    onDueDateCardReceived = {
      isMap = true,
      keyName = "blessingRecordId",
    },
    onDueDateCardReceived_deleted = {
      isMapDel = true,
      targetFieldName = "onDueDateCardReceived",
    },
    onDueDateCardSent = {
      isMap = true,
      keyName = "blessingRecordId",
    },
    onDueDateCardSent_deleted = {
      isMapDel = true,
      targetFieldName = "onDueDateCardSent",
    },
    remindData = {
      isMsg = true,
    },
  },
  AttrBirthdayOfficialWelfare = {
    officialWelfareAwardEpochSecs = {
    },
    officialWelfareReissueAwardEpochSecs = {
    },
    officialWelfareReissueTriggerEpochSecs = {
    },
  },
  AttrBirthdayRemindData = {
    friendUidList = {
      isAry = true,
    },
    friendUidList_is_cleared = {
      isAryClear = true,
      targetFieldName = "friendUidList",
    },
  },
  AttrBirthdayToFriend = {
    birthdayFriendData = {
      isMap = true,
      keyName = "friendUid",
    },
    birthdayFriendData_deleted = {
      isMapDel = true,
      targetFieldName = "birthdayFriendData",
    },
  },
  AttrBirthdayToFriendData = {
    birthdayMonthDay = {
    },
    friendUid = {
    },
    lastRemindEpochSecs = {
    },
    lastSendEpochMillis = {
    },
    sentEpochMillis = {
      isAry = true,
    },
    sentEpochMillis_is_cleared = {
      isAryClear = true,
      targetFieldName = "sentEpochMillis",
    },
    twoTimesAgoRemindEpochSecs = {
    },
  },
  AttrClubInfo = {
    applyClub = {
      isMap = true,
      keyName = "cid",
    },
    applyClub_deleted = {
      isMapDel = true,
      targetFieldName = "applyClub",
    },
    banCreate = {
    },
    chatGroupKey = {
      isMsg = true,
    },
    cid = {
    },
    clubs = {
      isMap = true,
      keyName = "cid",
    },
    clubs_deleted = {
      isMapDel = true,
      targetFieldName = "clubs",
    },
    created = {
      isAry = true,
    },
    created_is_cleared = {
      isAryClear = true,
      targetFieldName = "created",
    },
    heatDay = {
    },
    heatDetailToday = {
      isMap = true,
      keyName = "id",
    },
    heatDetailToday_deleted = {
      isMapDel = true,
      targetFieldName = "heatDetailToday",
    },
    heatToday = {
    },
    heatWeek = {
    },
    heatWeekDay = {
    },
    inviteSendFrequencyAttr = {
      isMap = true,
      keyName = "chatType",
    },
    inviteSendFrequencyAttr_deleted = {
      isMapDel = true,
      targetFieldName = "inviteSendFrequencyAttr",
    },
    joinRankLastNotifyMap = {
      isMap = true,
      keyName = "clubId",
    },
    joinRankLastNotifyMap_deleted = {
      isMapDel = true,
      targetFieldName = "joinRankLastNotifyMap",
    },
    lastReadMsgSeqId = {
    },
    playerClubRecord = {
      isMap = true,
      keyName = "cid",
    },
    playerClubRecord_deleted = {
      isMapDel = true,
      targetFieldName = "playerClubRecord",
    },
    weekSettleShareAttr = {
      isMap = true,
      keyName = "chatType",
    },
    weekSettleShareAttr_deleted = {
      isMapDel = true,
      targetFieldName = "weekSettleShareAttr",
    },
    worldInviteEnabled = {
    },
  },
  AttrCustomRoomInfo = {
    roomId = {
    },
    roomTypeVal = {
    },
  },
  AttrDisplayBoardData = {
    displayBoardHistoryData = {
      isMap = true,
      keyName = "setEpochSecs",
    },
    displayBoardHistoryData_deleted = {
      isMapDel = true,
      targetFieldName = "displayBoardHistoryData",
    },
    itemUuid = {
    },
  },
  AttrDisplayBoardInfo = {
    content = {
    },
    curMemberNum = {
    },
    displayBoardUgc = {
      isMsg = true,
    },
    isOpen = {
    },
    itemId = {
    },
    maxMemberNum = {
    },
    roomId = {
    },
  },
  AttrDisplayBoardUgc = {
    bucket = {
    },
    ugcId = {
    },
    ugcMapMetaInfo = {
      isMap = true,
      keyName = "keyIgnore",
    },
    ugcMapMetaInfo_deleted = {
      isMapDel = true,
      targetFieldName = "ugcMapMetaInfo",
    },
  },
  AttrInflateRedPacketData = {
    attrUnlockedUidList = {
      isAry = true,
    },
    attrUnlockedUidList_is_cleared = {
      isAryClear = true,
      targetFieldName = "attrUnlockedUidList",
    },
    canInflateTimes = {
    },
    currMoney = {
    },
    fullEpochSecs = {
    },
    isUnlock = {
    },
    memberNum = {
    },
    needUpdate = {
    },
    redPacketMoney = {
      isMap = true,
      keyName = "money",
    },
    redPacketMoney_deleted = {
      isMapDel = true,
      targetFieldName = "redPacketMoney",
    },
  },
  AttrLevelUpChallenge = {
    critEpochSecs = {
    },
    sampleGroupId = {
    },
  },
  AttrOnDueDateCardData = {
    birthdayCardData = {
      isMsg = true,
    },
    birthdayMonthDay = {
    },
    blessingRecordId = {
    },
    mailId = {
    },
    sendeeUid = {
    },
    sendEpochMillis = {
    },
    senderUid = {
    },
  },
  AttRoomMemberClientInfo = {
    voiceRoomTag = {
    },
    voiceState = {
    },
  },
  AttrPermitInfo = {
    exp = {
    },
    gloryReceive = {
      isSet = true,
    },
    gloryReceive_deleted = {
      isSetDel = true,
      targetFieldName = "gloryReceive",
    },
    id = {
    },
    level = {
    },
    receiveLevel = {
      isSet = true,
    },
    receiveLevel_deleted = {
      isSetDel = true,
      targetFieldName = "receiveLevel",
    },
    type = {
    },
    unlock = {
    },
  },
  AttrRecentActivity = {
    lastChampion = {
    },
    lastChampionMs = {
    },
    lastDegreeUpgradeMs = {
    },
    lastDegreeUpgradeStar = {
    },
    lastDegreeUpgradeType = {
    },
    lastPlayModeId = {
    },
    lastPlayModeMs = {
    },
    lastPlayUgcMapMs = {
    },
    lastPublishUgcMapMs = {
    },
    lastSaveXiaoWoMs = {
    },
  },
  AttrRestaurantThemed = {
    isStage2 = {
    },
    lastReceiveEpochMillis = {
    },
    npcData = {
      isMap = true,
      keyName = "npcId",
    },
    npcData_deleted = {
      isMapDel = true,
      targetFieldName = "npcData",
    },
    receivedTimes = {
    },
    receiveHistory = {
      isMap = true,
      keyName = "foodId",
    },
    receiveHistory_deleted = {
      isMapDel = true,
      targetFieldName = "receiveHistory",
    },
    receiveRecord = {
      isMap = true,
      keyName = "epochMillis",
    },
    receiveRecord_deleted = {
      isMapDel = true,
      targetFieldName = "receiveRecord",
    },
    shareRecord = {
      isMap = true,
      keyName = "id",
    },
    shareRecord_deleted = {
      isMapDel = true,
      targetFieldName = "shareRecord",
    },
  },
  AttrRewardRetrievalData = {
    isRedDot = {
    },
    isRedDotRemove = {
    },
    lastCheckEpochSecs = {
    },
    retrievalConfId = {
    },
    retrievalIdRedDot = {
      isMap = true,
      keyName = "retrievalId",
    },
    retrievalIdRedDot_deleted = {
      isMapDel = true,
      targetFieldName = "retrievalIdRedDot",
    },
    retrievalList = {
      isMap = true,
      keyName = "retrievalId",
    },
    retrievalList_deleted = {
      isMapDel = true,
      targetFieldName = "retrievalList",
    },
  },
  AttrRewardRetrievalIdRedDot = {
    isRedDot = {
    },
    isRedDotRemove = {
    },
    retrievalId = {
    },
  },
  AttrRewardRetrievalInfo = {
    conditionGroup = {
      isMsg = true,
    },
    conditionGroupLastCheckEpochSecs = {
    },
    finishedTimes = {
    },
    finishedTimeSecs = {
    },
    isUnlock = {
    },
    retrievalId = {
    },
    retrievalTotalTimes = {
    },
    retrievedTimes = {
    },
    unlockEpochSecs = {
    },
  },
  AttrRoomInfo = {
    chatGroupKey = {
      isMsg = true,
    },
    currentMatchType = {
    },
    customRoomInfo = {
      isMsg = true,
    },
    lastMatchType = {
    },
    roomid = {
    },
    roomMemberClientInfo = {
      isMsg = true,
    },
    status = {
    },
    teamInfo = {
      isMsg = true,
    },
  },
  AttrSceneInfo = {
    interactionId = {
    },
    levelInfo = {
      isMsg = true,
    },
    lobbyMapId = {
    },
    lobbySceneId = {
    },
    roundId = {
    },
  },
  AttrTeamInfo = {
    teamId = {
    },
  },
  AttrTreasureHunt = {
    lotteryDrawNum = {
    },
    treasureHuntHistory = {
      isMap = true,
      keyName = "excavateEpochSecs",
    },
    treasureHuntHistory_deleted = {
      isMapDel = true,
      targetFieldName = "treasureHuntHistory",
    },
  },
  AttrTwoPeopleSquadData = {
    itemNum = {
    },
  },
  AttrUgcMapMetaInfo = {
    isCoverCheckPass = {
    },
    keyIgnore = {
    },
    layerId = {
    },
    msg = {
    },
    msgType = {
    },
    preMsg = {
    },
    processType = {
    },
    size = {
    },
    version = {
    },
  },
  BadGuysRedDot = {
    friendId = {
    },
    gotTime = {
    },
  },
  Bag = {
    gridNum = {
    },
    id = {
    },
  },
  BagInfoDb = {
    actionSeting = {
    },
    createRoleItem = {
      isSet = true,
    },
    createRoleItem_deleted = {
      isSetDel = true,
      targetFieldName = "createRoleItem",
    },
    item = {
      isMap = true,
      keyName = "id",
    },
    item_deleted = {
      isMapDel = true,
      targetFieldName = "item",
    },
    observingItem = {
      isMap = true,
      keyName = "id",
    },
    observingItem_deleted = {
      isMapDel = true,
      targetFieldName = "observingItem",
    },
  },
  BannedItemInfo = {
    bannedTime = {
    },
    itemId = {
    },
  },
  BasicInfo = {
    beforeMidnightTimeMs = {
    },
    belongLogicZoneId = {
    },
    clientWhiteListSign = {
    },
    dailyOnlineTimeMs = {
    },
    everyHourRefreshTimeMs = {
    },
    farmDailyOnlineTimeMs = {
    },
    fiveClockRefreshTimeMs = {
    },
    heartBeatTimeMs = {
    },
    isRegistering = {
    },
    last8ClockRefreshTimeMs = {
    },
    lastHeartBeatTimeMs = {
    },
    lastLoginTimeMs = {
    },
    lastMidnightRefreshTimeMs = {
    },
    lastRecruitPublishTimeMs = {
    },
    lastWeekRefreshTimeMs = {
    },
    limitNotifyTimes = {
    },
    loginTimeMs = {
    },
    logoutTimeMs = {
    },
    onlineMillisRefreshAt8 = {
    },
    onTimeDelayMs = {
    },
    qqCloudGameAuth = {
    },
    sixClockRefreshTimeMs = {
    },
    threeClockRefreshTimeMs = {
    },
    totalLoginTimeMs = {
    },
  },
  BattleInfo = {
    aiGameType = {
    },
    battleid = {
    },
    competitionBasicInfo = {
      isMsg = true,
    },
    desModInfo = {
    },
    dsAddr = {
    },
    dsaInstanceID = {
    },
    dsAuthToken = {
    },
    dsSessionId = {
    },
    globalChatGroupKey = {
      isMsg = true,
    },
    matchType = {
    },
    miscInfo = {
      isMsg = true,
    },
    relatedRoomId = {
    },
    robotType = {
    },
    sceneId = {
    },
    side = {
    },
    sideChatGroupKey = {
      isMsg = true,
    },
    uid = {
    },
  },
  BattleLevelRoundData = {
    levelId = {
    },
    round = {
    },
  },
  BattleMiscInfo = {
    arenaRandomEvents = {
    },
  },
  BattleModeData = {
    id = {
    },
    recentBattleResultData = {
      isMap = true,
      keyName = "id",
    },
    recentBattleResultData_deleted = {
      isMapDel = true,
      targetFieldName = "recentBattleResultData",
    },
  },
  BattleResultData = {
    battleRole = {
    },
    endTime = {
    },
    id = {
    },
    levelRounds = {
      isMap = true,
      keyName = "round",
    },
    levelRounds_deleted = {
      isMapDel = true,
      targetFieldName = "levelRounds",
    },
    result = {
    },
    specialBattleData = {
      isMsg = true,
    },
  },
  BattleSettlementMVPInfo = {
    lastMVPTS = {
    },
    matchTypeID = {
    },
  },
  BenefitCardInfo = {
    beginTime = {
    },
    enable = {
    },
    expireTime = {
    },
  },
  BindAccountInfo = {
    accountType = {
    },
    timestamp = {
    },
  },
  BirthdayInfo = {
    day = {
    },
    month = {
    },
    year = {
    },
  },
  BlockCanSteal = {
    blockTime = {
    },
    friendId = {
    },
  },
  BlockFertilize = {
    blockTime = {
    },
    friendId = {
    },
  },
  BookOfFriends = {
    accumedIncome = {
      isMsg = true,
    },
    contract = {
      isMap = true,
      keyName = "uid",
    },
    contract_deleted = {
      isMapDel = true,
      targetFieldName = "contract",
    },
    dailyExpense = {
      isMsg = true,
    },
    farmGiftTaskInfo = {
      isMsg = true,
    },
    rewardFlag = {
    },
    typeInfo = {
      isMap = true,
      keyName = "type",
    },
    typeInfo_deleted = {
      isMapDel = true,
      targetFieldName = "typeInfo",
    },
  },
  BookOfFriendsContract = {
    expense = {
      isMsg = true,
    },
    income = {
      isMsg = true,
    },
    uid = {
    },
  },
  BookOfFriendsReport = {
    credit = {
    },
    times = {
    },
    upTimeSec = {
    },
  },
  BookOfFriendsTaskInfo = {
    accumedIncome = {
      isMsg = true,
    },
    contract = {
      isMap = true,
      keyName = "uid",
    },
    contract_deleted = {
      isMapDel = true,
      targetFieldName = "contract",
    },
    dailyExpense = {
      isMsg = true,
    },
    rewardFlag = {
    },
    typeInfo = {
      isMap = true,
      keyName = "type",
    },
    typeInfo_deleted = {
      isMapDel = true,
      targetFieldName = "typeInfo",
    },
  },
  BookOfFriendsTypeInfo = {
    income = {
      isMsg = true,
    },
    type = {
    },
  },
  BPInfo = {
    awardedFreeLevels = {
      isSet = true,
    },
    awardedFreeLevels_deleted = {
      isSetDel = true,
      targetFieldName = "awardedFreeLevels",
    },
    awardedPayedLevels = {
      isSet = true,
    },
    awardedPayedLevels_deleted = {
      isSetDel = true,
      targetFieldName = "awardedPayedLevels",
    },
    dailyTask = {
      isMsg = true,
    },
    inherited = {
    },
    LastGetExpTime = {
    },
    level = {
    },
    pay = {
    },
    residualExp = {
    },
    seasonId = {
    },
    seasonTask = {
      isMsg = true,
    },
    totalExp = {
    },
    type = {
    },
    WeekGetExp = {
    },
    weekTask = {
      isMsg = true,
    },
  },
  BPPublicInfo = {
    level = {
    },
    pay = {
    },
    seasonId = {
    },
    type = {
    },
  },
  BPTaskInfo = {
    refreshTs = {
    },
    taskGroup = {
    },
    tasks = {
      isSet = true,
    },
    tasks_deleted = {
      isSetDel = true,
      targetFieldName = "tasks",
    },
  },
  BubbleConfig = {
    id = {
    },
    setTimeMs = {
    },
  },
  Building = {
    confId = {
    },
    firstFlag = {
    },
    level = {
    },
    livingVillagerId = {
    },
    skinForm = {
    },
    skinId = {
    },
  },
  BuildingSkin = {
    expireMs = {
    },
    id = {
    },
  },
  BuyOrderInfo = {
    buyTimeMs = {
    },
    offerId = {
    },
    tradeNo = {
    },
  },
  BuyRecordStruct = {
    buyNum = {
    },
    commodityId = {
    },
    currentCanBuyNum = {
    },
    expireTimeMs = {
    },
    updateTimeMs = {
    },
  },
  CanStealTime = {
    canStealTime = {
    },
    playerUID = {
    },
  },
  CaptureShadowData = {
    beginGrid = {
    },
    canRewardFreeReward = {
    },
    captureCount = {
    },
    costCurrencyNum = {
    },
    mapCount = {
    },
    playerGrid = {
    },
    receivedCaptureRewardId = {
      isAry = true,
    },
    receivedCaptureRewardId_is_cleared = {
      isAryClear = true,
      targetFieldName = "receivedCaptureRewardId",
    },
    receivedOneTimeRewardIds = {
      isAry = true,
    },
    receivedOneTimeRewardIds_is_cleared = {
      isAryClear = true,
      targetFieldName = "receivedOneTimeRewardIds",
    },
    shoeGuaranteeTimes = {
    },
    targetBuff = {
    },
    targetGrid = {
    },
  },
  CatFishingInfo = {
    isReady = {
    },
    lastFishingReceiveTime = {
    },
    nextFishingCD = {
    },
  },
  ChallengeLevelinfo = {
    levelId = {
    },
    levelStauts = {
    },
  },
  ChampionCountInfo = {
    cnt = {
    },
    type = {
    },
  },
  ChangePlatMidasDiff = {
    fromPlatId = {
    },
    presentBalanceDiff = {
    },
    sumBalanceDiff = {
    },
    sumCostDiff = {
    },
    sumPresentDiff = {
    },
    sumSaveDiff = {
    },
    syncTimeMs = {
    },
  },
  ChaseDressItemInfo = {
    actorId = {
    },
    dressItemInfos = {
      isMap = true,
      keyName = "dressUpType",
    },
    dressItemInfos_deleted = {
      isMapDel = true,
      targetFieldName = "dressItemInfos",
    },
  },
  ChaseGameData = {
    bossDressItem = {
      isMap = true,
      keyName = "actorId",
    },
    bossDressItem_deleted = {
      isMapDel = true,
      targetFieldName = "bossDressItem",
    },
    identitySpecialization = {
      isMsg = true,
    },
    normalDressItem = {
      isMap = true,
      keyName = "actorId",
    },
    normalDressItem_deleted = {
      isMapDel = true,
      targetFieldName = "normalDressItem",
    },
  },
  ChaseGameDataNoPublic = {
    dailyTaskRewardIds = {
      isMap = true,
      keyName = "taskId",
    },
    dailyTaskRewardIds_deleted = {
      isMapDel = true,
      targetFieldName = "dailyTaskRewardIds",
    },
    isCompleteOldPlayerUnlock = {
    },
    matchTypeBattleRecords = {
      isMap = true,
      keyName = "matchTypeId",
    },
    matchTypeBattleRecords_deleted = {
      isMapDel = true,
      targetFieldName = "matchTypeBattleRecords",
    },
  },
  ChaseGameSettings = {
    newBieGuideOpen = {
    },
  },
  ChaseIdentityRankItem = {
    badgeId = {
    },
    geoCode = {
    },
    geoLevel = {
    },
    position = {
    },
    rankLevel = {
    },
    score = {
    },
    seasonId = {
    },
  },
  ChaseIdentitySpecialization = {
    identityData = {
      isMap = true,
      keyName = "identityId",
    },
    identityData_deleted = {
      isMapDel = true,
      targetFieldName = "identityData",
    },
    identityQualifyingSeasonInfo = {
      isMap = true,
      keyName = "qualifyType",
    },
    identityQualifyingSeasonInfo_deleted = {
      isMapDel = true,
      targetFieldName = "identityQualifyingSeasonInfo",
    },
    lastPlayIdentityId = {
    },
    lastWeeklySettlementTime = {
    },
  },
  ChaseIdentitySpecializationData = {
    activePoint = {
    },
    activePointUpdateTime = {
    },
    badgeId = {
    },
    battleScore = {
    },
    historyRank = {
      isMap = true,
      keyName = "geoLevel",
    },
    historyRank_deleted = {
      isMapDel = true,
      targetFieldName = "historyRank",
    },
    identityId = {
    },
    lastPlayTime = {
    },
    latestRank = {
      isMap = true,
      keyName = "geoLevel",
    },
    latestRank_deleted = {
      isMapDel = true,
      targetFieldName = "latestRank",
    },
    perfScore = {
    },
  },
  ChaseMatchTypeBattleRecord = {
    matchTypeId = {
    },
    sideStatics = {
      isMap = true,
      keyName = "sideId",
    },
    sideStatics_deleted = {
      isMapDel = true,
      targetFieldName = "sideStatics",
    },
  },
  ChaseSideStatics = {
    battleRecordCnt = {
    },
    sideId = {
    },
  },
  ChaseTaskResult = {
    rewardId = {
    },
    taskId = {
    },
    ts = {
    },
  },
  ChatChannel = {
    chatGroupKey = {
      isMsg = true,
    },
    index = {
    },
  },
  ChatGroupKey = {
    chatType = {
    },
    id = {
    },
    subID = {
    },
  },
  ChatRedDots = {
    chatGroupKey = {
      isMsg = true,
    },
    formatKey = {
    },
    lastUnReadSeq = {
    },
    redDotCnt = {
    },
    unReadSeqIds = {
      isSet = true,
    },
    unReadSeqIds_deleted = {
      isSetDel = true,
      targetFieldName = "unReadSeqIds",
    },
  },
  CheckerboardGridInfo = {
    index = {
    },
    param = {
    },
    roundRewardCount = {
    },
    status = {
    },
    type = {
    },
  },
  CheckerboardInfo = {
    column = {
    },
    gridInfo = {
      isMap = true,
      keyName = "index",
    },
    gridInfo_deleted = {
      isMapDel = true,
      targetFieldName = "gridInfo",
    },
    row = {
    },
  },
  CheckInInfo = {
    checkTime = {
    },
    state = {
    },
    weekDay = {
    },
  },
  CheckInPlanActivity = {
    canDrawRewardNum = {
    },
    checkCumTimes = {
    },
    checkedDays = {
      isSet = true,
    },
    checkedDays_deleted = {
      isSetDel = true,
      targetFieldName = "checkedDays",
    },
    drawnReward = {
      isSet = true,
    },
    drawnReward_deleted = {
      isSetDel = true,
      targetFieldName = "drawnReward",
    },
    lastNormalCheckTime = {
    },
    makeUpTimes = {
    },
  },
  ClearRedDotInfo = {
    filterCommodity = {
      isSet = true,
    },
    filterCommodity_deleted = {
      isSetDel = true,
      targetFieldName = "filterCommodity",
    },
    filterTask = {
      isSet = true,
    },
    filterTask_deleted = {
      isSetDel = true,
      targetFieldName = "filterTask",
    },
    updateTimeMs = {
    },
  },
  ClientCache = {
    expireTime = {
    },
    id = {
    },
    val = {
    },
  },
  ClientLogColoring = {
    beginTimeMs = {
    },
    endTimeMs = {
    },
    isSetTimer = {
    },
    logLevel = {
    },
    stainId = {
    },
  },
  ClientPakInfo = {
    detailPakInfo = {
      isMap = true,
      keyName = "pakGroupId",
    },
    detailPakInfo_deleted = {
      isMapDel = true,
      targetFieldName = "detailPakInfo",
    },
    pakInfo = {
      isMap = true,
      keyName = "pakGroupId",
    },
    pakInfo_deleted = {
      isMapDel = true,
      targetFieldName = "pakInfo",
    },
  },
  CloudNpc = {
    cloudTax = {
      isMsg = true,
    },
    eid = {
    },
    endTimeMs = {
    },
    evtID = {
    },
    itemGot = {
    },
    itemID = {
    },
    itemNum = {
    },
    itemNumPerPlayer = {
    },
    ratio = {
    },
    series = {
    },
  },
  CloudTax = {
    seller = {
      isMap = true,
      keyName = "uid",
    },
    seller_deleted = {
      isMapDel = true,
      targetFieldName = "seller",
    },
    tax = {
    },
    taxLeft = {
    },
    taxLeftStr = {
    },
  },
  ClubBriefData = {
    admins = {
      isMap = true,
      keyName = "uid",
    },
    admins_deleted = {
      isMapDel = true,
      targetFieldName = "admins",
    },
    chatGroupKey = {
      isMsg = true,
    },
    cid = {
    },
    joinTimeMs = {
    },
    name = {
    },
    redDot = {
    },
    title = {
    },
    weekSettleBubble = {
    },
  },
  ClubChallengeActivity = {
    battleInfo = {
      isMsg = true,
    },
    challengeCount = {
    },
    markedMatchTypeId = {
      isSet = true,
    },
    markedMatchTypeId_deleted = {
      isSetDel = true,
      targetFieldName = "markedMatchTypeId",
    },
    pickedRewardId = {
      isSet = true,
    },
    pickedRewardId_deleted = {
      isSetDel = true,
      targetFieldName = "pickedRewardId",
    },
    reportInfo = {
      isMsg = true,
    },
  },
  ClubIdentityData = {
    identity = {
    },
    uid = {
    },
  },
  ClubInviteSendFrequencyAttr = {
    chatType = {
    },
    lastSendTimeMs = {
    },
  },
  ClubNotifyJoinRankAttr = {
    clubId = {
    },
    lastSendTimeMs = {
    },
  },
  ClubWeekSettleShareAttr = {
    chatType = {
    },
    lastSendTimeMs = {
    },
  },
  CocActivityData = {
    activityId = {
    },
    beginTime = {
    },
    EndedAndCleared = {
    },
    endTime = {
    },
  },
  CocActivityInfo = {
    eightDaysActivity = {
      isMsg = true,
    },
  },
  CocBaseCampExtraInfo = {
    resUnits = {
      isMap = true,
      keyName = "type",
    },
    resUnits_deleted = {
      isMapDel = true,
      targetFieldName = "resUnits",
    },
  },
  CocBattleBuildingAttackModeEntry = {
    attackMode = {
    },
    buildingUniqueId = {
    },
  },
  CocBattleSummaryInfo = {
    battleType = {
    },
    hasSettled = {
    },
    oppoUid = {
    },
    settleFightSec = {
    },
    startFightSec = {
    },
    uuid = {
    },
  },
  CocBuilding = {
    assignedVillagerInfo = {
      isMap = true,
      keyName = "id",
    },
    assignedVillagerInfo_deleted = {
      isMapDel = true,
      targetFieldName = "assignedVillagerInfo",
    },
    extraInfo = {
      isMsg = true,
    },
    gridPosXY = {
    },
    level = {
    },
    revampInfo = {
      isMap = true,
      keyName = "revampId",
    },
    revampInfo_deleted = {
      isMapDel = true,
      targetFieldName = "revampInfo",
    },
    state = {
    },
    stateEndTime = {
    },
    typeId = {
    },
    uniqueId = {
    },
  },
  CocBuildingAssignedVillagerInfo = {
    id = {
    },
  },
  CocBuildingExtraInfo = {
    baseCamp = {
      isMsg = true,
    },
    hallOfVillager = {
      isMsg = true,
    },
    prison = {
      isMsg = true,
    },
    resProducer = {
      isMsg = true,
    },
    resStore = {
      isMsg = true,
    },
  },
  CocBuildingRevampInfo = {
    revampId = {
    },
    revampLevel = {
    },
  },
  CocCardInfo = {
    endTime = {
    },
    hasSyncCoc = {
    },
    id = {
    },
    startTime = {
    },
  },
  CocCityBuildingTimeLimitQueue = {
    endTime = {
    },
    idx = {
    },
    occupiedBuildingUniqueId = {
    },
    propviderSourceId = {
    },
    providerSourceType = {
    },
  },
  CocClientNumPropCacheInfo = {
    numPropCache = {
      isMap = true,
      keyName = "id",
    },
    numPropCache_deleted = {
      isMapDel = true,
      targetFieldName = "numPropCache",
    },
  },
  CocCurrentBattleInfo = {
    battleSummaryInfo = {
      isMsg = true,
    },
    dsBattleType = {
    },
  },
  CocDataNumPropAttr = {
    calcMap = {
      isMap = true,
      keyName = "numPropId",
    },
    calcMap_deleted = {
      isMapDel = true,
      targetFieldName = "calcMap",
    },
  },
  CocDataNumPropIdAndSrcMap = {
    numPropId = {
    },
    sourceMap = {
      isMap = true,
      keyName = "sourceType",
    },
    sourceMap_deleted = {
      isMapDel = true,
      targetFieldName = "sourceMap",
    },
  },
  CocDataNumPropKeyFactor = {
    b = {
    },
    endMillisecs = {
    },
    subKey = {
    },
  },
  CocDataNumPropSrcFactor = {
    factorMap = {
      isMap = true,
      keyName = "subKey",
    },
    factorMap_deleted = {
      isMapDel = true,
      targetFieldName = "factorMap",
    },
    sourceType = {
    },
  },
  CocDefenceInfo = {
    defeatedCount = {
    },
    defeatedTimeMs = {
    },
  },
  CocDialogEntry = {
    group = {
      isMap = true,
      keyName = "id",
    },
    group_deleted = {
      isMapDel = true,
      targetFieldName = "group",
    },
    id = {
    },
  },
  CocDialogGroup = {
    finished = {
    },
    id = {
    },
    played = {
    },
  },
  CocDialogInfo = {
    dialog = {
      isMap = true,
      keyName = "id",
    },
    dialog_deleted = {
      isMapDel = true,
      targetFieldName = "dialog",
    },
  },
  CocFeatureInfo = {
    conditionGroup = {
      isMsg = true,
    },
    id = {
    },
    isOpen = {
    },
    isUnlock = {
    },
    ver = {
    },
  },
  CocFeatureUnlock = {
    features = {
      isMap = true,
      keyName = "id",
    },
    features_deleted = {
      isMapDel = true,
      targetFieldName = "features",
    },
    lastRefreshTimeMs = {
    },
  },
  CocFightDeltaInfo = {
    battleSoldiers = {
      isMap = true,
      keyName = "id",
    },
    battleSoldiers_deleted = {
      isMapDel = true,
      targetFieldName = "battleSoldiers",
    },
    captureXingbao = {
    },
    destroyId = {
    },
    destroyVillagerHideBuildingExtraReward = {
      isMap = true,
      keyName = "type",
    },
    destroyVillagerHideBuildingExtraReward_deleted = {
      isMapDel = true,
      targetFieldName = "destroyVillagerHideBuildingExtraReward",
    },
    extraPlunderResource = {
      isMap = true,
      keyName = "type",
    },
    extraPlunderResource_deleted = {
      isMapDel = true,
      targetFieldName = "extraPlunderResource",
    },
    isWin = {
    },
    overallDamagePer = {
    },
    plunderResource = {
      isMap = true,
      keyName = "type",
    },
    plunderResource_deleted = {
      isMapDel = true,
      targetFieldName = "plunderResource",
    },
  },
  CocFightSettleSoldierInfo = {
    id = {
    },
    level = {
    },
    num = {
    },
  },
  CocFriendDonationInfo = {
    dailySendRecord = {
      isMap = true,
      keyName = "timestamp",
    },
    dailySendRecord_deleted = {
      isMapDel = true,
      targetFieldName = "dailySendRecord",
    },
    historyReceivedRecord = {
      isMap = true,
      keyName = "timestamp",
    },
    historyReceivedRecord_deleted = {
      isMapDel = true,
      targetFieldName = "historyReceivedRecord",
    },
    lastRefreshTimeMs = {
    },
    receivedTimes = {
    },
    sendTimes = {
    },
  },
  CocFriendDonationRecord = {
    timestamp = {
    },
    uid = {
    },
  },
  CocFriendGift = {
    uid = {
    },
  },
  CocFriendGiftInfo = {
    current = {
      isMsg = true,
    },
    lastRefreshTimeMs = {
    },
    record = {
      isAry = true,
    },
    record_is_cleared = {
      isAryClear = true,
      targetFieldName = "record",
    },
  },
  CocFriendInfo = {
    donationInfo = {
      isMsg = true,
    },
    giftInfo = {
      isMsg = true,
    },
  },
  CocFriendVillager = {
    dresses = {
      isMap = true,
      keyName = "dressType",
    },
    dresses_deleted = {
      isMapDel = true,
      targetFieldName = "dresses",
    },
    idx = {
    },
    officialBuildingUniqueId = {
    },
    ownerUid = {
    },
  },
  CocHallOfVillagerExtraInfo = {
    level = {
    },
    studentInfos = {
      isMap = true,
      keyName = "position",
    },
    studentInfos_deleted = {
      isMapDel = true,
      targetFieldName = "studentInfos",
    },
  },
  CocHallOfVillagerStudentInfo = {
    isPaidUnlocked = {
    },
    position = {
    },
    villagerId = {
    },
  },
  CocImpression = {
    impression = {
      isMap = true,
      keyName = "numPropId",
    },
    impression_deleted = {
      isMapDel = true,
      targetFieldName = "impression",
    },
  },
  CocImpressionItem = {
    numPropId = {
    },
    sourceMap = {
      isMap = true,
      keyName = "sourceId",
    },
    sourceMap_deleted = {
      isMapDel = true,
      targetFieldName = "sourceMap",
    },
  },
  CocItem = {
    id = {
    },
    number = {
    },
  },
  CocItemInfo = {
    item = {
      isMap = true,
      keyName = "id",
    },
    item_deleted = {
      isMapDel = true,
      targetFieldName = "item",
    },
  },
  CocLevelDefenseCurGameInfo = {
    clientEntrance = {
    },
    levelId = {
    },
    matchUID = {
    },
    startFightTimeSec = {
    },
    startGameTimeSec = {
    },
  },
  CocLevelDefenseFinishLevelInfo = {
    finishTimeSec = {
    },
    levelId = {
    },
    starCnt = {
    },
  },
  CocLevelDefenseInfo = {
    currentGameInfo = {
      isMsg = true,
    },
    finishLevelInfo = {
      isMap = true,
      keyName = "levelId",
    },
    finishLevelInfo_deleted = {
      isMapDel = true,
      targetFieldName = "finishLevelInfo",
    },
    lastFinishLevelTimeOfBeachMonsterEntrance = {
    },
  },
  CocMatchInfo = {
    deltaInfo = {
      isMsg = true,
    },
    fightMode = {
    },
    hasDelta = {
    },
    hasRanked = {
      isAry = true,
    },
    hasRanked_is_cleared = {
      isAryClear = true,
      targetFieldName = "hasRanked",
    },
    matchedMirror = {
    },
    matchedScore = {
    },
    matchedType = {
    },
    matchedUid = {
    },
    matchTime = {
    },
    state = {
    },
    uuid = {
    },
  },
  CocModeAttrSnap = {
    baseCampLv = {
    },
  },
  CocModeInfo = {
    cocActivityInfo = {
      isMsg = true,
    },
    cocCardInfo = {
      isMap = true,
      keyName = "id",
    },
    cocCardInfo_deleted = {
      isMapDel = true,
      targetFieldName = "cocCardInfo",
    },
    hasCoc = {
    },
    modeAttrSnap = {
      isMsg = true,
    },
    needSyncItemIds = {
      isSet = true,
    },
    needSyncItemIds_deleted = {
      isSetDel = true,
      targetFieldName = "needSyncItemIds",
    },
  },
  CocMonthCardInfo = {
    absentDays = {
    },
    beginTimeMs = {
    },
    expirationTime = {
    },
    expireMarked = {
    },
    id = {
    },
    lastGetDailyItem = {
    },
  },
  CocNumPropItem = {
    id = {
    },
    value = {
    },
  },
  CocOfflineBuildingUpgradedEventData = {
    upgradedBuildingUniqueId = {
      isSet = true,
    },
    upgradedBuildingUniqueId_deleted = {
      isSetDel = true,
      targetFieldName = "upgradedBuildingUniqueId",
    },
  },
  CocOfflineDefenceBattleEventData = {
    defenceSuccessCount = {
    },
    deltaCupsScore = {
    },
    newScore = {
    },
    oldScore = {
    },
    totalBattleCnt = {
    },
  },
  CocOfflineEventData = {
    buildingUpgraded = {
      isMsg = true,
    },
    defenceBattle = {
      isMsg = true,
    },
    prosperityValueChange = {
      isMsg = true,
    },
  },
  CocOfflineEventInfo = {
    events = {
      isMap = true,
      keyName = "eventType",
    },
    events_deleted = {
      isMapDel = true,
      targetFieldName = "events",
    },
  },
  CocOfflineEventItem = {
    eventData = {
      isMsg = true,
    },
    eventType = {
    },
  },
  CocOfflineProsperityValueChangeEventData = {
    deltaValue = {
    },
  },
  CocPlayerCityBuildingInfo = {
    battleBuildingAttackerModeInfo = {
      isMap = true,
      keyName = "buildingUniqueId",
    },
    battleBuildingAttackerModeInfo_deleted = {
      isMapDel = true,
      targetFieldName = "battleBuildingAttackerModeInfo",
    },
    buildings = {
      isMap = true,
      keyName = "uniqueId",
    },
    buildings_deleted = {
      isMapDel = true,
      targetFieldName = "buildings",
    },
    timeLimitUpgradeQueues = {
      isMap = true,
      keyName = "idx",
    },
    timeLimitUpgradeQueues_deleted = {
      isMapDel = true,
      targetFieldName = "timeLimitUpgradeQueues",
    },
    timeLimitWorkerHurtBuiltTimes = {
    },
  },
  CocPlayerMapRegionInfo = {
    sceneId = {
    },
    unlockedMinorRegions = {
      isSet = true,
    },
    unlockedMinorRegions_deleted = {
      isSetDel = true,
      targetFieldName = "unlockedMinorRegions",
    },
    unlockedPrimaryRegions = {
      isSet = true,
    },
    unlockedPrimaryRegions_deleted = {
      isSetDel = true,
      targetFieldName = "unlockedPrimaryRegions",
    },
  },
  CocPresetSoldier = {
    id = {
    },
    soldier = {
      isMap = true,
      keyName = "order",
    },
    soldier_deleted = {
      isMapDel = true,
      targetFieldName = "soldier",
    },
  },
  CocPrisonExtraInfo = {
    assignEndTime = {
    },
    assignStartTime = {
    },
    buildingUniqueId = {
    },
    nextCanAssignTime = {
    },
    originalEndTime = {
    },
    seqId = {
    },
    workInfos = {
      isMap = true,
      keyName = "id",
    },
    workInfos_deleted = {
      isMapDel = true,
      targetFieldName = "workInfos",
    },
  },
  CocPrisonWorkInfo = {
    id = {
    },
    name = {
    },
    time = {
    },
    type = {
    },
  },
  CocProsperityInfo = {
    prosperityDetail = {
      isMap = true,
      keyName = "moduleId",
    },
    prosperityDetail_deleted = {
      isMapDel = true,
      targetFieldName = "prosperityDetail",
    },
    totalProsperityValue = {
    },
  },
  CocProsperityValueEntry = {
    moduleId = {
    },
    prosperityValue = {
    },
  },
  CocPVECampaignMode = {
    stageStarInfo = {
      isMsg = true,
    },
  },
  CocPVEChallengeMode = {
    stageStarInfo = {
      isMsg = true,
    },
  },
  CocPVECurrentGameInfo = {
    gameType = {
    },
    stageNum = {
    },
    startFightTimeSec = {
    },
    startGameTimeSec = {
    },
  },
  CocPVEGameInfo = {
    campaignMode = {
      isMsg = true,
    },
    challengeMode = {
      isMsg = true,
    },
    currentGameInfo = {
      isMsg = true,
    },
  },
  CocPVEStageStarInfo = {
    stageStarRecord = {
      isMap = true,
      keyName = "stageId",
    },
    stageStarRecord_deleted = {
      isMapDel = true,
      targetFieldName = "stageStarRecord",
    },
  },
  CocPVEStageStarUnit = {
    stageId = {
    },
    starNum = {
    },
  },
  CocRankingScore = {
    curSeasonMaxScore = {
    },
    hasRewarded = {
      isAry = true,
    },
    hasRewarded_is_cleared = {
      isAryClear = true,
      targetFieldName = "hasRewarded",
    },
    lastSeasonScore = {
    },
    loseCnt = {
    },
    redisKey = {
    },
    score = {
    },
    seasonId = {
    },
    totalCnt = {
    },
    winCnt = {
    },
  },
  CocRecoverPropAttr = {
    currentNum = {
    },
    lastIncTimeSec = {
    },
    maxNum = {
    },
    propType = {
    },
  },
  CocRecoverPropInfo = {
    propAttr = {
      isMap = true,
      keyName = "propType",
    },
    propAttr_deleted = {
      isMapDel = true,
      targetFieldName = "propAttr",
    },
  },
  CocResourceCollectRecord = {
    lastCollectSec = {
    },
    type = {
    },
  },
  CocResourceInfo = {
    collectRecord = {
      isMap = true,
      keyName = "type",
    },
    collectRecord_deleted = {
      isMapDel = true,
      targetFieldName = "collectRecord",
    },
    resUnits = {
      isMap = true,
      keyName = "type",
    },
    resUnits_deleted = {
      isMapDel = true,
      targetFieldName = "resUnits",
    },
  },
  CocResourceUnit = {
    amount = {
    },
    snapshotAmount4Plunder = {
    },
    type = {
    },
  },
  CocResProducerExtraInfo = {
    efficiency = {
    },
    lastSettleSec = {
    },
    settledAmount = {
    },
  },
  CocResStoreExtraInfo = {
    amount = {
    },
  },
  CocScience = {
    id = {
    },
    state = {
    },
    upgradeEndTime = {
    },
    upgradeStartTime = {
    },
  },
  CocScienceInfo = {
    science = {
      isMap = true,
      keyName = "id",
    },
    science_deleted = {
      isMapDel = true,
      targetFieldName = "science",
    },
    upgradeCnt = {
    },
  },
  CocSingleImpressionValue = {
    b = {
    },
    endMillisecs = {
    },
    ownerId = {
    },
  },
  CocSoldierInfo = {
    level = {
      isMap = true,
      keyName = "id",
    },
    level_deleted = {
      isMapDel = true,
      targetFieldName = "level",
    },
    presetSoldier = {
      isMap = true,
      keyName = "id",
    },
    presetSoldier_deleted = {
      isMapDel = true,
      targetFieldName = "presetSoldier",
    },
    readySoldier = {
      isMap = true,
      keyName = "id",
    },
    readySoldier_deleted = {
      isMapDel = true,
      targetFieldName = "readySoldier",
    },
    trainingHistory = {
      isMsg = true,
    },
    trainingQueue = {
      isMsg = true,
    },
    trainingSoldier = {
      isMap = true,
      keyName = "order",
    },
    trainingSoldier_deleted = {
      isMapDel = true,
      targetFieldName = "trainingSoldier",
    },
  },
  CocSoldierLevel = {
    id = {
    },
    level = {
    },
  },
  CocSoldierUnit = {
    amount = {
    },
    id = {
    },
    timestamp = {
    },
  },
  CocSourceImpression = {
    sourceId = {
    },
    sourceImpressionMap = {
      isMap = true,
      keyName = "ownerId",
    },
    sourceImpressionMap_deleted = {
      isMapDel = true,
      targetFieldName = "sourceImpressionMap",
    },
  },
  CocTask = {
    conditionGroup = {
      isMsg = true,
    },
    doing = {
    },
    doingConditionGroup = {
      isMsg = true,
    },
    id = {
    },
    important = {
    },
    lastStatusMs = {
    },
    rewardStatus = {
    },
    taskStatus = {
    },
  },
  CocTaskCompleted = {
    completeTimeMs = {
    },
    id = {
    },
  },
  CocTaskInfo = {
    completed = {
      isMap = true,
      keyName = "id",
    },
    completed_deleted = {
      isMapDel = true,
      targetFieldName = "completed",
    },
    task = {
      isMap = true,
      keyName = "id",
    },
    task_deleted = {
      isMapDel = true,
      targetFieldName = "task",
    },
  },
  CocTrainingHistory = {
    record = {
      isMap = true,
      keyName = "id",
    },
    record_deleted = {
      isMapDel = true,
      targetFieldName = "record",
    },
  },
  CocTrainingHistoryLevel = {
    amount = {
    },
    level = {
    },
  },
  CocTrainingHistoryRecord = {
    id = {
    },
    level = {
      isMap = true,
      keyName = "level",
    },
    level_deleted = {
      isMapDel = true,
      targetFieldName = "level",
    },
  },
  CocTrainingQueue = {
    accelerateEndTimeMs = {
    },
    accelerateStartTimeMs = {
    },
    beginTimeMs = {
    },
    endTimeMs = {
    },
    suspending = {
    },
    training = {
    },
  },
  CocTrainingSoldierUnit = {
    order = {
    },
    ready = {
    },
    training = {
    },
    unit = {
      isMsg = true,
    },
  },
  CocTreasureBox = {
    beginTimeMs = {
    },
    endTimeMs = {
    },
    id = {
    },
    order = {
    },
    state = {
    },
  },
  CocTreasureBoxInfo = {
    capacity = {
    },
    treasureBox = {
      isMap = true,
      keyName = "order",
    },
    treasureBox_deleted = {
      isMapDel = true,
      targetFieldName = "treasureBox",
    },
  },
  CocUserAttr = {
    basicInfo = {
      isMsg = true,
    },
    cityBuildingInfo = {
      isMsg = true,
    },
    clientNumPropCacheInfo = {
      isMsg = true,
    },
    cocMatchInfo = {
      isMsg = true,
    },
    currentBattleInfo = {
      isMsg = true,
    },
    defenceInfo = {
      isMsg = true,
    },
    dialogInfo = {
      isMsg = true,
    },
    featureUnlock = {
      isMsg = true,
    },
    friendInfo = {
      isMsg = true,
    },
    homeDsInfo = {
      isMsg = true,
    },
    homeVisitorInfo = {
      isMsg = true,
    },
    impression = {
      isMsg = true,
    },
    itemInfo = {
      isMsg = true,
    },
    levelDefenseInfo = {
      isMsg = true,
    },
    mapRegionInfo = {
      isMsg = true,
    },
    monthCardInfo = {
      isMap = true,
      keyName = "id",
    },
    monthCardInfo_deleted = {
      isMapDel = true,
      targetFieldName = "monthCardInfo",
    },
    offlineEventInfo = {
      isMsg = true,
    },
    prosperityInfo = {
      isMsg = true,
    },
    pveGameInfo = {
      isMsg = true,
    },
    rankingScore = {
      isMsg = true,
    },
    recoverPropInfo = {
      isMsg = true,
    },
    resourceInfo = {
      isMsg = true,
    },
    scienceInfo = {
      isMsg = true,
    },
    soldierInfo = {
      isMsg = true,
    },
    taskInfo = {
      isMsg = true,
    },
    treasureBoxInfo = {
      isMsg = true,
    },
    uid = {
    },
    version = {
    },
    villagerInfo = {
      isMsg = true,
    },
    xingbaoInfo = {
      isMsg = true,
    },
  },
  CocUserBasicInfo = {
    lastEnterSceneTime = {
    },
    lastExitSceneTime = {
    },
    openId = {
    },
    registerTime = {
    },
  },
  CocVillager = {
    assignedBuildingUniqueId = {
    },
    customName = {
    },
    dialogueInfo = {
      isMsg = true,
    },
    dresses = {
      isMap = true,
      keyName = "dressType",
    },
    dresses_deleted = {
      isMapDel = true,
      targetFieldName = "dresses",
    },
    favorValue = {
    },
    id = {
    },
    lastAccpetDailyFavorRewardTimeMs = {
    },
    level = {
    },
    officialBuildingUniqueId = {
    },
    officialPosId = {
    },
    rewardCnt = {
    },
  },
  CocVillagerDialogueInfo = {
    dialogueId = {
    },
    state = {
    },
    stateTimeMs = {
    },
  },
  CocVillagerDress = {
    dressItemId = {
    },
    dressType = {
    },
    dressUuid = {
    },
    outLookRandom = {
      isSet = true,
    },
    outLookRandom_deleted = {
      isSetDel = true,
      targetFieldName = "outLookRandom",
    },
  },
  CocVillagerInfo = {
    friendVillagers = {
      isMap = true,
      keyName = "idx",
    },
    friendVillagers_deleted = {
      isMapDel = true,
      targetFieldName = "friendVillagers",
    },
    lockedVillagers = {
      isMap = true,
      keyName = "id",
    },
    lockedVillagers_deleted = {
      isMapDel = true,
      targetFieldName = "lockedVillagers",
    },
    villagers = {
      isMap = true,
      keyName = "id",
    },
    villagers_deleted = {
      isMapDel = true,
      targetFieldName = "villagers",
    },
    villagersOnCondition = {
      isMap = true,
      keyName = "id",
    },
    villagersOnCondition_deleted = {
      isMapDel = true,
      targetFieldName = "villagersOnCondition",
    },
  },
  CocVillagerWaitOnCondition = {
    conditionGroup = {
      isMsg = true,
    },
    id = {
    },
    wait2Lock = {
    },
  },
  CocXingbaoInfo = {
    afterRelease = {
      isMap = true,
      keyName = "uid",
    },
    afterRelease_deleted = {
      isMapDel = true,
      targetFieldName = "afterRelease",
    },
    captures = {
      isMap = true,
      keyName = "uid",
    },
    captures_deleted = {
      isMapDel = true,
      targetFieldName = "captures",
    },
    captureTimes = {
    },
    mine = {
      isMsg = true,
    },
    releaseTimes = {
    },
  },
  CocXingbaoUnit = {
    buildingId = {
    },
    dresses = {
      isMap = true,
      keyName = "dressType",
    },
    dresses_deleted = {
      isMapDel = true,
      targetFieldName = "dresses",
    },
    dressUpInfos = {
      isSet = true,
    },
    dressUpInfos_deleted = {
      isSetDel = true,
      targetFieldName = "dressUpInfos",
    },
    fanRong = {
    },
    mainBuildingLv = {
    },
    reward = {
    },
    status = {
    },
    statusBeginTimeMs = {
    },
    statusEndTimeMs = {
    },
    uid = {
    },
    villagerId = {
    },
  },
  CoinInfo = {
    coinNum = {
    },
    coinType = {
    },
  },
  CollectionInfo = {
    collectionId = {
    },
    expireTimeMs = {
    },
    getTimeMs = {
    },
  },
  CollectionInfos = {
    collections = {
      isMap = true,
      keyName = "collectionId",
    },
    collections_deleted = {
      isMapDel = true,
      targetFieldName = "collections",
    },
    collectionType = {
    },
  },
  CombinationInteraction = {
    createTimeMs = {
    },
    iconId = {
    },
    iconName = {
    },
    id = {
    },
    items = {
      isMap = true,
      keyName = "id",
    },
    items_deleted = {
      isMapDel = true,
      targetFieldName = "items",
    },
    name = {
    },
  },
  CombinationItems = {
    combinationItems = {
      isAry = true,
    },
    combinationItems_is_cleared = {
      isAryClear = true,
      targetFieldName = "combinationItems",
    },
    content = {
    },
    id = {
    },
    name = {
    },
  },
  CombinationSettings = {
    combinationInteractions = {
      isMap = true,
      keyName = "id",
    },
    combinationInteractions_deleted = {
      isMapDel = true,
      targetFieldName = "combinationInteractions",
    },
    settingId = {
    },
  },
  CommodityInfo = {
    id = {
    },
    num = {
    },
  },
  CommodityRedPoint = {
    commodityId = {
    },
    showRedPoint = {
    },
  },
  CommonArrayList = {
    list = {
      isAry = true,
    },
    list_is_cleared = {
      isAryClear = true,
      targetFieldName = "list",
    },
  },
  CommonConditionGroup = {
    attrConditionGroup = {
      isMap = true,
      keyName = "id",
    },
    attrConditionGroup_deleted = {
      isMapDel = true,
      targetFieldName = "attrConditionGroup",
    },
    contionGroupType = {
    },
  },
  CommonGiftBuyInfo = {
    buyCount = {
    },
    id = {
    },
  },
  CommonLimitInfo = {
    limitInfo = {
      isMap = true,
      keyName = "limitId",
    },
    limitInfo_deleted = {
      isMapDel = true,
      targetFieldName = "limitInfo",
    },
    limitType = {
    },
  },
  CommonMallInfo = {
    boughtRecord = {
      isMap = true,
      keyName = "commodityId",
    },
    boughtRecord_deleted = {
      isMapDel = true,
      targetFieldName = "boughtRecord",
    },
    commodityNtf = {
      isMap = true,
      keyName = "commodityId",
    },
    commodityNtf_deleted = {
      isMapDel = true,
      targetFieldName = "commodityNtf",
    },
    nextRefreshTime = {
    },
  },
  CommunityChannelHotTopicInfo = {
    arenaHotTopicInfo = {
      isMap = true,
      keyName = "id",
    },
    arenaHotTopicInfo_deleted = {
      isMapDel = true,
      targetFieldName = "arenaHotTopicInfo",
    },
    farmHotTopicInfo = {
      isMap = true,
      keyName = "id",
    },
    farmHotTopicInfo_deleted = {
      isMapDel = true,
      targetFieldName = "farmHotTopicInfo",
    },
    spHotTopicInfo = {
      isMap = true,
      keyName = "id",
    },
    spHotTopicInfo_deleted = {
      isMapDel = true,
      targetFieldName = "spHotTopicInfo",
    },
    tradingCardHotTopicInfo = {
      isMap = true,
      keyName = "id",
    },
    tradingCardHotTopicInfo_deleted = {
      isMapDel = true,
      targetFieldName = "tradingCardHotTopicInfo",
    },
    wolfKillHotTopicInfo = {
      isMap = true,
      keyName = "id",
    },
    wolfKillHotTopicInfo_deleted = {
      isMapDel = true,
      targetFieldName = "wolfKillHotTopicInfo",
    },
  },
  CommunityChannelIconInfo = {
    arenaMVPInfo = {
      isMsg = true,
    },
    HeroIconID = {
    },
    identityIconID = {
    },
    wolfKillMVPInfo = {
      isMsg = true,
    },
  },
  CommunityChannelInfo = {
    chatGroupKey = {
      isMsg = true,
    },
    initiatedQuitTs = {
    },
    joinTS = {
    },
    stickTS = {
    },
  },
  CompetitionBasicInfo = {
    compType = {
    },
    gameType = {
    },
    season = {
    },
  },
  CompetitionWarmUpActivityAttr = {
    stageAttr = {
      isMap = true,
      keyName = "stageId",
    },
    stageAttr_deleted = {
      isMapDel = true,
      targetFieldName = "stageAttr",
    },
  },
  CompetitionWarmUpStageAttr = {
    claimedGameTimesReward = {
      isSet = true,
    },
    claimedGameTimesReward_deleted = {
      isSetDel = true,
      targetFieldName = "claimedGameTimesReward",
    },
    claimedScoreReward = {
      isSet = true,
    },
    claimedScoreReward_deleted = {
      isSetDel = true,
      targetFieldName = "claimedScoreReward",
    },
    gameTimes = {
    },
    score = {
    },
    stageId = {
    },
  },
  ConanIpActiveData = {
    awardCount = {
    },
    buff = {
    },
  },
  ConanWarmupActivityData = {
    answeredDays = {
    },
    rewardedDaysList = {
      isSet = true,
    },
    rewardedDaysList_deleted = {
      isSetDel = true,
      targetFieldName = "rewardedDaysList",
    },
  },
  Condition = {
    conditionStatus = {
    },
    finishCount = {
    },
    id = {
    },
    value = {
    },
    valueInternal = {
    },
    valueList = {
      isAry = true,
    },
    valueList_is_cleared = {
      isAryClear = true,
      targetFieldName = "valueList",
    },
  },
  ConditionGroup = {
    conditions = {
      isMap = true,
      keyName = "id",
    },
    conditions_deleted = {
      isMapDel = true,
      targetFieldName = "conditions",
    },
    status = {
    },
  },
  ConditionGroupInfo = {
    conditionGroup = {
      isMsg = true,
    },
    id = {
    },
  },
  CookAttr = {
    cookBasicInfo = {
      isMsg = true,
    },
    cookBusinessInfo = {
      isMsg = true,
    },
    cookClientCloud = {
      isMap = true,
      keyName = "K",
    },
    cookClientCloud_deleted = {
      isMapDel = true,
      targetFieldName = "cookClientCloud",
    },
    cookCommentInfo = {
      isMsg = true,
    },
    cookDsInfo = {
      isMsg = true,
    },
    cookId = {
    },
    cookInteractInfo = {
      isMsg = true,
    },
    cookSafeInfo = {
      isMsg = true,
    },
    cookVisitorInfo = {
      isMsg = true,
    },
    currentRoomId = {
    },
    customerInfo = {
      isMsg = true,
    },
    dishLevelInfo = {
      isMap = true,
      keyName = "type",
    },
    dishLevelInfo_deleted = {
      isMapDel = true,
      targetFieldName = "dishLevelInfo",
    },
    floatingScreenInfo = {
      isMsg = true,
    },
    hiredInfo = {
      isMsg = true,
    },
    itemInfo = {
      isMap = true,
      keyName = "id",
    },
    itemInfo_deleted = {
      isMapDel = true,
      targetFieldName = "itemInfo",
    },
    layoutInfo = {
      isMap = true,
      keyName = "layoutID",
    },
    layoutInfo_deleted = {
      isMapDel = true,
      targetFieldName = "layoutInfo",
    },
    offlineIncomeInfo = {
      isMsg = true,
    },
    roomInfo = {
      isMap = true,
      keyName = "id",
    },
    roomInfo_deleted = {
      isMapDel = true,
      targetFieldName = "roomInfo",
    },
    sysEmployeeInfo = {
      isMsg = true,
    },
    visitantInfo = {
      isMsg = true,
    },
  },
  CookBasicInfo = {
    createTime = {
    },
    instruction = {
    },
    lastRefreshTimeMs = {
    },
    leaveTimeMs = {
    },
    level = {
    },
    likeNum = {
    },
    name = {
    },
  },
  CookBusinessInfo = {
    coinGain = {
    },
    commentScoreHistory = {
      isMap = true,
      keyName = "k",
    },
    commentScoreHistory_deleted = {
      isMapDel = true,
      targetFieldName = "commentScoreHistory",
    },
    customerFlow = {
    },
    effectiveCustomerFlow = {
    },
    farmExpGain = {
    },
    lastCalculateTime = {
    },
    likeGain = {
    },
    menu = {
      isMsg = true,
    },
    openTime = {
    },
    serveVisitantNum = {
    },
    state = {
    },
  },
  CookCandidate = {
    avatarId = {
    },
    idx = {
    },
    isHired = {
    },
    jobInfo = {
      isMsg = true,
    },
    name = {
    },
    price = {
    },
  },
  CookCommentInfo = {
    commentAndReplyNumber = {
    },
    commentNumberInfo = {
      isMap = true,
      keyName = "commentId",
    },
    commentNumberInfo_deleted = {
      isMapDel = true,
      targetFieldName = "commentNumberInfo",
    },
    commentPhotoNameIdPoll = {
    },
    commentWithPlayerReplyNumber = {
    },
    cookCommentScoreInfo = {
      isMsg = true,
    },
    dayCommentNumber = {
    },
    dayRecord = {
      isMap = true,
      keyName = "cookId",
    },
    dayRecord_deleted = {
      isMapDel = true,
      targetFieldName = "dayRecord",
    },
    lastOfflineCommentTimeMs = {
    },
    lastOnlineCommentTimeMs = {
    },
    lastPullId = {
    },
    lastUnCommentCustomerArriveTimeMs = {
    },
    newNumber = {
    },
    photoNameIdInfo = {
      isMap = true,
      keyName = "id",
    },
    photoNameIdInfo_deleted = {
      isMapDel = true,
      targetFieldName = "photoNameIdInfo",
    },
    totalNumber = {
    },
    unCommentCustomers = {
      isSet = true,
    },
    unCommentCustomers_deleted = {
      isSetDel = true,
      targetFieldName = "unCommentCustomers",
    },
  },
  CookCommentNumberInfo = {
    commentId = {
    },
    customerReplyNum = {
    },
    photoNameId = {
    },
    playerReplyNum = {
    },
    score = {
    },
  },
  CookCommentPhotoNameIdInfo = {
    id = {
    },
    lastAuthTimeMs = {
    },
  },
  CookCommentReplyRecord = {
    cookId = {
    },
    recordCount = {
    },
  },
  CookCommentScoreInfo = {
    avgScore = {
    },
    lastAvgScoreWhenExit = {
    },
    number = {
    },
    totalScore = {
    },
  },
  CookCustomer = {
    arriveTimeMs = {
    },
    avatarId = {
    },
    food = {
    },
    id = {
    },
    name = {
    },
    type = {
    },
    willComment = {
    },
  },
  CookCustomerInfo = {
    avatarCdMap = {
      isMap = true,
      keyName = "k",
    },
    avatarCdMap_deleted = {
      isMapDel = true,
      targetFieldName = "avatarCdMap",
    },
    customerList = {
      isMap = true,
      keyName = "id",
    },
    customerList_deleted = {
      isMapDel = true,
      targetFieldName = "customerList",
    },
    idAdder = {
    },
    lastCustomerArriveTimeMs = {
    },
  },
  CookEmployeeAbility = {
    charm = {
    },
    exclusive = {
    },
    speed = {
    },
  },
  CookEmployeeBasicInfo = {
    createTime = {
    },
    name = {
    },
    pos = {
    },
    recycleTime = {
    },
    state = {
    },
  },
  CookEmployeeJobInfo = {
    ability = {
      isMsg = true,
    },
    job = {
    },
    level = {
    },
    quality = {
    },
  },
  CookEmployer = {
    income = {
    },
    startTime = {
    },
    UID = {
    },
  },
  CookFloatingScreenInfo = {
    content = {
    },
  },
  CookFriendEmployeeInfo = {
    staff = {
      isMap = true,
      keyName = "id",
    },
    staff_deleted = {
      isMapDel = true,
      targetFieldName = "staff",
    },
  },
  CookGreeter = {
    basicInfo = {
      isMsg = true,
    },
    friendUID = {
    },
    id = {
    },
    incomeBuff = {
    },
    wages = {
    },
  },
  CookHiredHistoryItem = {
    endTime = {
    },
    id = {
    },
    income = {
    },
    startTime = {
    },
    UID = {
    },
  },
  CookHiredInfo = {
    employer = {
      isMap = true,
      keyName = "UID",
    },
    employer_deleted = {
      isMapDel = true,
      targetFieldName = "employer",
    },
    hiredHistory = {
      isMap = true,
      keyName = "id",
    },
    hiredHistory_deleted = {
      isMapDel = true,
      targetFieldName = "hiredHistory",
    },
    hireFriendsHistory = {
      isMap = true,
      keyName = "startTime",
    },
    hireFriendsHistory_deleted = {
      isMapDel = true,
      targetFieldName = "hireFriendsHistory",
    },
    hisID = {
    },
  },
  CookHireFriendsHistoryItem = {
    endTime = {
    },
    expend = {
    },
    startTime = {
    },
    UID = {
    },
  },
  CookInfo = {
    myCookInfo = {
      isMsg = true,
    },
    visitCookInfo = {
      isMsg = true,
    },
  },
  CookOfflineIncomeInfo = {
    coin = {
    },
    farmExp = {
    },
    like = {
    },
    storageBenefitTime = {
    },
  },
  CookPublicInfo = {
    cookSafeInfo = {
      isMsg = true,
    },
    cookVisitorCount = {
    },
    roomCount = {
    },
  },
  CookPunish = {
    enable = {
    },
    endTime = {
    },
    reason = {
    },
  },
  CookRecruitmentMarket = {
    candidates = {
      isMap = true,
      keyName = "idx",
    },
    candidates_deleted = {
      isMapDel = true,
      targetFieldName = "candidates",
    },
    highCandidates = {
      isMap = true,
      keyName = "idx",
    },
    highCandidates_deleted = {
      isMapDel = true,
      targetFieldName = "highCandidates",
    },
    isHighCandidateInit = {
    },
    isInit = {
    },
    qualityProtect = {
      isMap = true,
      keyName = "quality",
    },
    qualityProtect_deleted = {
      isMapDel = true,
      targetFieldName = "qualityProtect",
    },
    todayRefreshCount = {
    },
  },
  CookRecruitmentMarketRefreshProtect = {
    charm = {
    },
    exclusive = {
    },
    needValue = {
    },
    quality = {
    },
    speed = {
    },
    value = {
    },
  },
  CookSafeInfo = {
    commentReplyForbidden = {
      isMsg = true,
    },
    floatingScreenForbidden = {
      isMsg = true,
    },
    putDown = {
      isMsg = true,
    },
  },
  CookSysEmployee = {
    avatarId = {
    },
    basicInfo = {
      isMsg = true,
    },
    id = {
    },
    jobInfo = {
      isMsg = true,
    },
  },
  CookSysEmployeeInfo = {
    employeeList = {
      isMap = true,
      keyName = "id",
    },
    employeeList_deleted = {
      isMapDel = true,
      targetFieldName = "employeeList",
    },
    initOriginEmployeeFlag = {
    },
    recruitmentMarket = {
      isMsg = true,
    },
    recycleEmployeeList = {
      isMap = true,
      keyName = "id",
    },
    recycleEmployeeList_deleted = {
      isMapDel = true,
      targetFieldName = "recycleEmployeeList",
    },
  },
  CookVisitant = {
    customer = {
      isMsg = true,
    },
    gainCoin = {
    },
    groupId = {
    },
    ignoredUIDs = {
      isSet = true,
    },
    ignoredUIDs_deleted = {
      isSetDel = true,
      targetFieldName = "ignoredUIDs",
    },
    protectEndTimeMs = {
    },
    quality = {
    },
    serveTimeMs = {
    },
    source = {
    },
    state = {
    },
    uniqueId = {
    },
  },
  CookVisitantCurrentGroupInfo = {
    currentGroupArriveTimeMs = {
    },
    currentGroupId = {
    },
    currentGroupServedNum = {
    },
    currentGroupStolenNum = {
    },
  },
  CookVisitantInfo = {
    currentGroupInfo = {
      isMsg = true,
    },
    lastPrebookGroupId = {
    },
    prebookInfo = {
      isMsg = true,
    },
    prebookList = {
      isSet = true,
    },
    prebookList_deleted = {
      isSetDel = true,
      targetFieldName = "prebookList",
    },
    stealInfo = {
      isMsg = true,
    },
    visitants = {
      isMap = true,
      keyName = "uniqueId",
    },
    visitants_deleted = {
      isMapDel = true,
      targetFieldName = "visitants",
    },
  },
  CookVisitantPrebookInfo = {
    arriveTimeMs = {
    },
    gainCoin = {
    },
    groupId = {
    },
    prebookTimeMs = {
    },
    protectEndTimeMs = {
    },
    visitantNum = {
    },
  },
  CookVisitantStealInfo = {
    lastStealTime = {
      isMap = true,
      keyName = "k",
    },
    lastStealTime_deleted = {
      isMapDel = true,
      targetFieldName = "lastStealTime",
    },
  },
  CosImage = {
    bucket = {
    },
    cdnVersion = {
    },
    md5 = {
    },
    region = {
    },
    url = {
    },
  },
  Crop = {
    careValue = {
    },
    confId = {
    },
    cropClientInfo = {
      isMsg = true,
    },
    cropMonthlyPassInfo = {
      isMsg = true,
    },
    fertilizedCount = {
    },
    fertilizedUIDs = {
      isSet = true,
    },
    fertilizedUIDs_deleted = {
      isSetDel = true,
      targetFieldName = "fertilizedUIDs",
    },
    growValue = {
    },
    ignoredUIDs = {
      isSet = true,
    },
    ignoredUIDs_deleted = {
      isSetDel = true,
      targetFieldName = "ignoredUIDs",
    },
    lastUpdateTime = {
    },
    leftItems = {
      isMap = true,
      keyName = "itemId",
    },
    leftItems_deleted = {
      isMapDel = true,
      targetFieldName = "leftItems",
    },
    magicInfo = {
      isMsg = true,
    },
    normalQualityItems = {
      isMap = true,
      keyName = "itemId",
    },
    normalQualityItems_deleted = {
      isMapDel = true,
      targetFieldName = "normalQualityItems",
    },
    protectEndTime = {
    },
    ripeQuality = {
    },
    ripeQualityMul = {
    },
    specialCropInfo = {
      isMsg = true,
    },
    stolenCount = {
    },
    stolenProtect = {
    },
    stolenUIDs = {
      isSet = true,
    },
    stolenUIDs_deleted = {
      isSetDel = true,
      targetFieldName = "stolenUIDs",
    },
    totalItems = {
      isMap = true,
      keyName = "itemId",
    },
    totalItems_deleted = {
      isMapDel = true,
      targetFieldName = "totalItems",
    },
    totalStolenCount = {
    },
    weatherInfo = {
      isMsg = true,
    },
  },
  CropCanWaterInterval = {
    dryTime = {
    },
    ripeTime = {
    },
  },
  CropClientInfo = {
    feedFlag = {
    },
  },
  CropFertilizeGuaranteed = {
    noBtoGTimes = {
    },
    noBtoSTimes = {
    },
    noStoGTimes = {
    },
    type = {
    },
  },
  CropInfo = {
    cropType = {
    },
    dryTime = {
    },
    objID = {
    },
    plantTime = {
    },
    ripeTime = {
    },
    waterProtectTime = {
    },
  },
  CropLevel = {
    exp = {
    },
    level = {
    },
    levelRewardGot = {
      isMap = true,
      keyName = "k",
    },
    levelRewardGot_deleted = {
      isMapDel = true,
      targetFieldName = "levelRewardGot",
    },
    type = {
    },
  },
  CropMachineContain = {
    billNo = {
    },
    confId = {
    },
    num = {
    },
  },
  CropMachineInfo = {
    confId = {
    },
    contain = {
      isMsg = true,
    },
    finishQuality = {
    },
    finishTimeSec = {
    },
    id = {
    },
    isFinish = {
    },
  },
  CropMagicInfo = {
    delayAddCanEncourageNeedGrowValue = {
    },
    delayAddMaxGrowValue = {
    },
    recovered = {
    },
    speedUpProtectEndTime = {
    },
  },
  CropMonthlyPassInfo = {
    effective = {
    },
  },
  CropWeatherInfo = {
    rainEffectEndTime = {
    },
    rainProtectCanceled = {
    },
    rainProtectEndTime = {
    },
  },
  CupsInfo = {
    cupsHistoryNum = {
    },
    cupsHistoryNumDb = {
    },
    cupsRedClick = {
      isMsg = true,
    },
    curCycle = {
    },
    cycleUnLock = {
      isAry = true,
    },
    cycleUnLock_is_cleared = {
      isAryClear = true,
      targetFieldName = "cycleUnLock",
    },
    dailyCupsNum = {
    },
    dailyCupsNumDb = {
    },
    mainCups = {
      isMsg = true,
    },
    mainCupsCycleMap = {
      isMap = true,
      keyName = "id",
    },
    mainCupsCycleMap_deleted = {
      isMapDel = true,
      targetFieldName = "mainCupsCycleMap",
    },
    modeCupsMap = {
      isMap = true,
      keyName = "id",
    },
    modeCupsMap_deleted = {
      isMapDel = true,
      targetFieldName = "modeCupsMap",
    },
    seasonCupsHistoryNum = {
    },
    seasonCupsHistoryNumDb = {
    },
    unlockCycleCondition = {
      isMap = true,
      keyName = "id",
    },
    unlockCycleCondition_deleted = {
      isMapDel = true,
      targetFieldName = "unlockCycleCondition",
    },
    weeklyProgress = {
    },
    weeklyProgressDb = {
    },
  },
  CupsRedClick = {
    redDotInProgressByOpen = {
      isAry = true,
    },
    redDotInProgressByOpen_is_cleared = {
      isAryClear = true,
      targetFieldName = "redDotInProgressByOpen",
    },
    redDotInProgressByUnlock = {
      isAry = true,
    },
    redDotInProgressByUnlock_is_cleared = {
      isAryClear = true,
      targetFieldName = "redDotInProgressByUnlock",
    },
    showRedByOpen = {
      isAry = true,
    },
    showRedByOpen_is_cleared = {
      isAryClear = true,
      targetFieldName = "showRedByOpen",
    },
    showRedByUnlock = {
      isAry = true,
    },
    showRedByUnlock_is_cleared = {
      isAryClear = true,
      targetFieldName = "showRedByUnlock",
    },
  },
  DailyIntimacyData = {
    lastIncreaseTime = {
    },
    realIntimacyUpdateTime = {
    },
    realTodayIntimacy = {
    },
    realYesterdayIntimacy = {
    },
    todayIntimacy = {
    },
    todayMsgNum = {
    },
    uid = {
    },
  },
  DailySumBuyTimesInfo = {
    buyTimes = {
    },
    typeId = {
    },
  },
  DailyVictoryRecord = {
    dateKey = {
    },
    num = {
    },
  },
  DanceData = {
    highestScore = {
    },
  },
  DanceOutfitData = {
    danceOutfitChangeMs = {
    },
    danceOutfitEquipId = {
    },
    danceOutfitEquipMs = {
    },
    danceOutfitSelectId = {
    },
    dressOutfitCollectHistory = {
      isMap = true,
      keyName = "sortId",
    },
    dressOutfitCollectHistory_deleted = {
      isMapDel = true,
      targetFieldName = "dressOutfitCollectHistory",
    },
    dressOutfitEquipHistory = {
      isMap = true,
      keyName = "index",
    },
    dressOutfitEquipHistory_deleted = {
      isMapDel = true,
      targetFieldName = "dressOutfitEquipHistory",
    },
    dressOutfitGearHistory = {
      isAry = true,
    },
    dressOutfitGearHistory_is_cleared = {
      isAryClear = true,
      targetFieldName = "dressOutfitGearHistory",
    },
    dressOutfitHistoryMaxCount = {
    },
    lastRefreshDailyTimeMs = {
    },
  },
  DBAchievementInfo = {
    id = {
    },
    value = {
    },
    valueInternal = {
    },
    valueList = {
      isAry = true,
    },
    valueList_is_cleared = {
      isAryClear = true,
      targetFieldName = "valueList",
    },
  },
  DBApplyRelation = {
    addMotivation = {
    },
    algoInfo = {
      isMsg = true,
    },
    applyTime = {
    },
    applyType = {
    },
    hotData = {
      isMsg = true,
    },
    intimateId = {
    },
    motivationParams = {
    },
    reason = {
    },
    recommendReason = {
    },
    recommendReasonText = {
    },
    subReason = {
    },
    uid = {
    },
  },
  DBPlayerHotData = {
    creatorId = {
    },
    hidePlayerStatus = {
    },
    hideProfileToFriend = {
    },
    intimacy = {
    },
    lastLogoutTime = {
    },
    playerStatus = {
    },
    recentInteractTs = {
    },
    svrId = {
    },
    togetherBattleCount = {
    },
  },
  DBRelation = {
    addTime = {
    },
    birthdayMonthDay = {
    },
    birthdayVisibleRange = {
    },
    followMallWishList = {
    },
    hasNewIntimate = {
    },
    hotData = {
      isMsg = true,
    },
    intimateId = {
    },
    mallWishListReadTs = {
    },
    nameData = {
      isMsg = true,
    },
    openId = {
    },
    reason = {
    },
    returnExpiredSec = {
    },
    returning = {
    },
    subReason = {
    },
    uid = {
    },
  },
  DBRemoveInfo = {
    removeTime = {
    },
    uid = {
    },
  },
  DBStepInfo = {
    backupMapIds = {
      isAry = true,
    },
    backupMapIds_is_cleared = {
      isAryClear = true,
      targetFieldName = "backupMapIds",
    },
    battleId = {
    },
    changeMapCount = {
    },
    curMapId = {
    },
    failCount = {
    },
    info = {
      isMsg = true,
    },
    isPass = {
    },
    recommendMapId = {
    },
    startTime = {
    },
    stepId = {
    },
    usedMapIds = {
      isAry = true,
    },
    usedMapIds_is_cleared = {
      isAryClear = true,
      targetFieldName = "usedMapIds",
    },
  },
  DepositFutureItem = {
    id = {
    },
    num = {
    },
    untilTs = {
    },
    uuid = {
    },
  },
  DepositGift = {
    confirmTs = {
    },
    futureItems = {
      isMap = true,
      keyName = "uuid",
    },
    futureItems_deleted = {
      isMapDel = true,
      targetFieldName = "futureItems",
    },
    intentionTs = {
    },
  },
  DepositSnapshot = {
    balanceNum = {
    },
    major = {
      isMsg = true,
    },
    minor = {
      isMsg = true,
    },
    timestamp = {
    },
  },
  DfStoreFreeTimes = {
    freeCount = {
    },
    packageId = {
    },
  },
  DfUserDBInfo = {
    storeFreeTimes = {
      isMap = true,
      keyName = "packageId",
    },
    storeFreeTimes_deleted = {
      isMapDel = true,
      targetFieldName = "storeFreeTimes",
    },
    storeResetTimestamp = {
    },
  },
  DishLevel = {
    exp = {
    },
    level = {
    },
    type = {
    },
  },
  DisplayBoardHistoryData = {
    setEpochSecs = {
    },
    ugcId = {
    },
  },
  DoGoodersRedDot = {
    friendId = {
    },
    gotTime = {
    },
  },
  DoubleDiamondActivityData = {
    levelData = {
      isMap = true,
      keyName = "id",
    },
    levelData_deleted = {
      isMapDel = true,
      targetFieldName = "levelData",
    },
  },
  DoubleDiamondLevelData = {
    id = {
    },
    rebateNum = {
    },
  },
  DressInfo = {
    dressItemId = {
    },
    dressType = {
    },
    dressUuid = {
    },
    outLookRandom = {
      isSet = true,
    },
    outLookRandom_deleted = {
      isSetDel = true,
      targetFieldName = "outLookRandom",
    },
  },
  DressItemInfo = {
    dressUpType = {
    },
    itemId = {
    },
    itemUUID = {
    },
  },
  DressOutfitCollectHistory = {
    index = {
    },
    sortId = {
    },
  },
  DressUpDetailInfo = {
    itemId = {
    },
    showStatus = {
    },
    status = {
    },
  },
  DropData = {
    items = {
      isMap = true,
      keyName = "dropKey",
    },
    items_deleted = {
      isMapDel = true,
      targetFieldName = "items",
    },
    treeUid = {
    },
  },
  DropItemList = {
    dropKey = {
    },
    items = {
      isMap = true,
      keyName = "index",
    },
    items_deleted = {
      isMapDel = true,
      targetFieldName = "items",
    },
  },
  DSItem = {
    dsItemConfId = {
    },
    dsItemNum = {
    },
  },
  DSOwnerBackpackInfo = {
    backpackOwnerId = {
    },
    curItemId = {
    },
    itemMap = {
      isMap = true,
      keyName = "dsItemConfId",
    },
    itemMap_deleted = {
      isMapDel = true,
      targetFieldName = "itemMap",
    },
  },
  DSUserBackpackInfo = {
    dsOwnerBackpackMap = {
      isMap = true,
      keyName = "backpackOwnerId",
    },
    dsOwnerBackpackMap_deleted = {
      isMapDel = true,
      targetFieldName = "dsOwnerBackpackMap",
    },
  },
  DsUserDBInfo = {
    dsUserDBInfoUnion = {
      isMsg = true,
    },
    matchType = {
    },
  },
  DsUserDBInfoUnion = {
    dfUserDBInfo = {
      isMsg = true,
    },
    fpsWeaponUnLockUserDBInfo = {
      isMsg = true,
    },
    maydayUserDBInfo = {
      isMsg = true,
    },
    omdUserDBInfo = {
    },
    roguelikeUserDBInfo = {
      isMsg = true,
    },
    tycoonUserDBInfo = {
      isMsg = true,
    },
  },
  ElvesData = {
    curGood = {
    },
    goodReward = {
      isMap = true,
      keyName = "good",
    },
    goodReward_deleted = {
      isMapDel = true,
      targetFieldName = "goodReward",
    },
    id = {
    },
  },
  EntertainmentGuide = {
    isOpen = {
    },
    registerGuideTime = {
    },
    reissueStickers = {
    },
    stickers = {
      isSet = true,
    },
    stickers_deleted = {
      isSetDel = true,
      targetFieldName = "stickers",
    },
    task = {
      isMap = true,
      keyName = "taskId",
    },
    task_deleted = {
      isMapDel = true,
      targetFieldName = "task",
    },
    unlockCondition = {
      isMap = true,
      keyName = "id",
    },
    unlockCondition_deleted = {
      isMapDel = true,
      targetFieldName = "unlockCondition",
    },
    unlockMatchType = {
      isSet = true,
    },
    unlockMatchType_deleted = {
      isSetDel = true,
      targetFieldName = "unlockMatchType",
    },
  },
  EntertainmentGuideTask = {
    condition = {
      isMsg = true,
    },
    status = {
    },
    taskId = {
    },
  },
  EquipCollections = {
    positionCollections = {
      isMsg = true,
    },
    randomCollections = {
      isMsg = true,
    },
    singleCollection = {
    },
  },
  EquipDressInfo = {
    dressUpType = {
    },
    equipCollections = {
      isMsg = true,
    },
  },
  EquipItemInfo = {
    itemType = {
    },
    posItemInfo = {
      isMsg = true,
    },
  },
  EquipPosInfo = {
    collectionId = {
    },
    position = {
    },
  },
  EvictRecord = {
    friendId = {
    },
    reason = {
    },
    updateTime = {
    },
  },
  ExchangeCenter = {
    exchangePair = {
      isMap = true,
      keyName = "id",
    },
    exchangePair_deleted = {
      isMapDel = true,
      targetFieldName = "exchangePair",
    },
  },
  ExchangePair = {
    conditionGroup = {
      isMsg = true,
    },
    exchangeCount = {
    },
    id = {
    },
  },
  ExtCardBagOutPutData = {
    extCardAddWeight = {
      isMap = true,
      keyName = "k",
    },
    extCardAddWeight_deleted = {
      isMapDel = true,
      targetFieldName = "extCardAddWeight",
    },
    Id = {
    },
  },
  FarmActivityOpenTime = {
    activityId = {
    },
    beginTime = {
    },
    endTime = {
    },
  },
  FarmAnwserData = {
    answeredDays = {
    },
    isCompletetask = {
    },
  },
  FarmAquarium = {
    id = {
    },
    lastGetExpTime = {
    },
    lastResetTime = {
    },
    tags = {
      isMap = true,
      keyName = "scale",
    },
    tags_deleted = {
      isMapDel = true,
      targetFieldName = "tags",
    },
  },
  FarmAquariumSeat = {
    benefitStartTime = {
    },
    billNo = {
    },
    idx = {
    },
    itemId = {
    },
    quality = {
    },
    qualityUsedSec = {
    },
    scale = {
    },
    trackId = {
    },
  },
  FarmAquariumTag = {
    scale = {
    },
    seats = {
      isMap = true,
      keyName = "idx",
    },
    seats_deleted = {
      isMapDel = true,
      targetFieldName = "seats",
    },
  },
  FarmAttr = {
    bannedSellItems = {
      isMap = true,
      keyName = "itemId",
    },
    bannedSellItems_deleted = {
      isMapDel = true,
      targetFieldName = "bannedSellItems",
    },
    buildingInfo = {
      isMsg = true,
    },
    cropFertilizeGuaranteed = {
      isMap = true,
      keyName = "type",
    },
    cropFertilizeGuaranteed_deleted = {
      isMapDel = true,
      targetFieldName = "cropFertilizeGuaranteed",
    },
    cropLevelInfo = {
      isMap = true,
      keyName = "type",
    },
    cropLevelInfo_deleted = {
      isMapDel = true,
      targetFieldName = "cropLevelInfo",
    },
    farmAquarium = {
      isMap = true,
      keyName = "id",
    },
    farmAquarium_deleted = {
      isMapDel = true,
      targetFieldName = "farmAquarium",
    },
    farmBasicInfo = {
      isMsg = true,
    },
    farmBlockedFriends = {
      isMap = true,
      keyName = "uid",
    },
    farmBlockedFriends_deleted = {
      isMapDel = true,
      targetFieldName = "farmBlockedFriends",
    },
    farmBuffInfo = {
      isMsg = true,
    },
    farmClientCloud = {
      isMap = true,
      keyName = "K",
    },
    farmClientCloud_deleted = {
      isMapDel = true,
      targetFieldName = "farmClientCloud",
    },
    farmCloudmarket = {
      isMsg = true,
    },
    farmCollection = {
      isMsg = true,
    },
    farmCookViewInfo = {
      isMsg = true,
    },
    farmCropStatisticInfo = {
      isMsg = true,
    },
    FarmDsInfo = {
      isMsg = true,
    },
    farmEventSync = {
      isMsg = true,
    },
    farmExternalOperateInfo = {
      isMsg = true,
    },
    farmFishBowlInfo = {
      isMap = true,
      keyName = "id",
    },
    farmFishBowlInfo_deleted = {
      isMapDel = true,
      targetFieldName = "farmFishBowlInfo",
    },
    farmFishingGuaranteed = {
      isMap = true,
      keyName = "k",
    },
    farmFishingGuaranteed_deleted = {
      isMapDel = true,
      targetFieldName = "farmFishingGuaranteed",
    },
    farmFishingGuaranteedInfo = {
      isMsg = true,
    },
    farmFishingReport = {
      isMsg = true,
    },
    farmFishPoolInfo = {
      isMsg = true,
    },
    farmGiftList = {
      isMsg = true,
    },
    farmGodFigure = {
      isMsg = true,
    },
    farmHotInfo = {
      isMsg = true,
    },
    farmId = {
    },
    farmItemInfo = {
      isMsg = true,
    },
    farmKirin = {
      isMsg = true,
    },
    farmMagic = {
      isMsg = true,
    },
    farmModuleOpenTimeInfo = {
      isMsg = true,
    },
    farmMonthlyPass = {
      isMsg = true,
    },
    farmNewPlayerInfo = {
      isMsg = true,
    },
    farmOwnerInfo = {
      isMsg = true,
    },
    farmPartyInfo = {
      isMsg = true,
    },
    farmPetInfo = {
      isMsg = true,
    },
    farmReport = {
      isMap = true,
      keyName = "reportType",
    },
    farmReport_deleted = {
      isMapDel = true,
      targetFieldName = "farmReport",
    },
    farmSafeInfo = {
      isMsg = true,
    },
    farmSceneDrop = {
      isMsg = true,
    },
    farmSignature = {
      isMsg = true,
    },
    farmStatisticInfo = {
      isMsg = true,
    },
    farmStatusViewInfo = {
      isMsg = true,
    },
    farmStealingInfo = {
      isMsg = true,
    },
    farmTalentInfo = {
      isMsg = true,
    },
    farmTlogRequiredFields = {
      isMsg = true,
    },
    farmVillagerInfo = {
      isMsg = true,
    },
    FarmVisitorInfo = {
      isMsg = true,
    },
    farmWeatherInfo = {
      isMsg = true,
    },
    farmWelcomeInfo = {
      isMsg = true,
    },
    fishCardLevel = {
      isMap = true,
      keyName = "type",
    },
    fishCardLevel_deleted = {
      isMapDel = true,
      targetFieldName = "fishCardLevel",
    },
    furnitureTimeInfo = {
      isMap = true,
      keyName = "confId",
    },
    furnitureTimeInfo_deleted = {
      isMapDel = true,
      targetFieldName = "furnitureTimeInfo",
    },
    handBookInfo = {
      isMap = true,
      keyName = "category",
    },
    handBookInfo_deleted = {
      isMapDel = true,
      targetFieldName = "handBookInfo",
    },
    hotSpring = {
      isMsg = true,
    },
    houseItemStatistics = {
      isMsg = true,
    },
    liuYanMessageInfo = {
      isMsg = true,
    },
    mapInfo = {
      isMap = true,
      keyName = "id",
    },
    mapInfo_deleted = {
      isMapDel = true,
      targetFieldName = "mapInfo",
    },
    npcFarmInfo = {
      isMsg = true,
    },
    redPacketInfo = {
      isMsg = true,
    },
    stolenStat = {
      isMsg = true,
    },
    taskInfo = {
      isMsg = true,
    },
  },
  FarmBasicInfo = {
    boolKV = {
      isAry = true,
    },
    boolKV_is_cleared = {
      isAryClear = true,
      targetFieldName = "boolKV",
    },
    createTime = {
    },
    enterPermission = {
    },
    exp = {
    },
    farmFriendNum = {
    },
    farmSignature = {
    },
    farmSignatureEXP = {
    },
    hasCook = {
    },
    hasHouse = {
    },
    hasVillage = {
    },
    instruction = {
    },
    lastLoginTimeMs = {
    },
    lastRefreshTimeMs = {
    },
    name = {
    },
    openId = {
    },
    relatedFarmID = {
    },
    svrId = {
    },
    version = {
    },
    visitorListTag = {
    },
  },
  FarmBlockedFriend = {
    uid = {
    },
    updateTime = {
    },
  },
  FarmBlockFriend = {
    uid = {
    },
    updateTime = {
    },
  },
  FarmBuff = {
    buffId = {
    },
    endTimeSec = {
    },
    startTimeSec = {
    },
  },
  FarmBuffEffect = {
    effectId = {
    },
    value = {
    },
  },
  FarmBuffInfo = {
    selfBuffEffectList = {
      isMap = true,
      keyName = "effectId",
    },
    selfBuffEffectList_deleted = {
      isMapDel = true,
      targetFieldName = "selfBuffEffectList",
    },
    selfBuffEffectNextFreshTimeSec = {
    },
    selfBuffHotUpdateTimeMs = {
    },
    selfBuffList = {
      isMap = true,
      keyName = "sourceUid",
    },
    selfBuffList_deleted = {
      isMapDel = true,
      targetFieldName = "selfBuffList",
    },
  },
  FarmBuffList = {
    buffs = {
      isMap = true,
      keyName = "buffId",
    },
    buffs_deleted = {
      isMapDel = true,
      targetFieldName = "buffs",
    },
    sourceUid = {
    },
  },
  FarmBuffWish = {
    buffInfo = {
      isMap = true,
      keyName = "k",
    },
    buffInfo_deleted = {
      isMapDel = true,
      targetFieldName = "buffInfo",
    },
    commonArrayList = {
      isMsg = true,
    },
  },
  FarmBuildingInfo = {
    buildings = {
      isMap = true,
      keyName = "confId",
    },
    buildings_deleted = {
      isMapDel = true,
      targetFieldName = "buildings",
    },
    buildingSkins = {
      isMap = true,
      keyName = "id",
    },
    buildingSkins_deleted = {
      isMapDel = true,
      targetFieldName = "buildingSkins",
    },
  },
  FarmBuildingObtainMainExpRecord = {
    alreadyLevel = {
    },
    confId = {
    },
  },
  FarmClientCache = {
    K = {
    },
    V = {
    },
  },
  FarmCloudmarket = {
    cloudNpc = {
      isMap = true,
      keyName = "eid",
    },
    cloudNpc_deleted = {
      isMapDel = true,
      targetFieldName = "cloudNpc",
    },
  },
  FarmCollection = {
    handbooks = {
      isMap = true,
      keyName = "id",
    },
    handbooks_deleted = {
      isMapDel = true,
      targetFieldName = "handbooks",
    },
  },
  FarmCollectionHandbook = {
    id = {
    },
    isAwardFurniture = {
    },
    obtainTime = {
    },
  },
  FarmCookViewInfo = {
    canStealTimeMap = {
      isMap = true,
      keyName = "k",
    },
    canStealTimeMap_deleted = {
      isMapDel = true,
      targetFieldName = "canStealTimeMap",
    },
    canStealTimeMapForRemind = {
      isMap = true,
      keyName = "k",
    },
    canStealTimeMapForRemind_deleted = {
      isMapDel = true,
      targetFieldName = "canStealTimeMapForRemind",
    },
    floatingScreenInfo = {
      isMsg = true,
    },
  },
  FarmCropStatisticInfo = {
    cropWaterAddCareAmount = {
    },
    cropWaterAddCareAmount1 = {
    },
    cropWaterAddCareAmount2 = {
    },
    cropWaterAddCareMeetCount = {
    },
    cropWaterAddCareMeetCountLastResetTime = {
    },
  },
  FarmCustomData = {
    data = {
      isMap = true,
      keyName = "id",
    },
    data_deleted = {
      isMapDel = true,
      targetFieldName = "data",
    },
    dataKey = {
    },
  },
  FarmDailyAwardActivity = {
    activiytBuyTimes = {
    },
    checkInCount = {
    },
    checkInList = {
      isMap = true,
      keyName = "id",
    },
    checkInList_deleted = {
      isMapDel = true,
      targetFieldName = "checkInList",
    },
    isUpgrade = {
    },
    lastCheckInTimeMs = {
    },
    weekendAwardTime = {
    },
  },
  FarmDragonData = {
    personalPoint = {
    },
    teamId = {
    },
  },
  FarmEvent = {
    cd = {
    },
    clientData = {
    },
    cond = {
    },
    data = {
      isMap = true,
      keyName = "id",
    },
    data_deleted = {
      isMapDel = true,
      targetFieldName = "data",
    },
    endTimeMs = {
    },
    evtID = {
    },
    id = {
    },
    series = {
    },
    startTime = {
    },
    step = {
    },
    triggerFarm = {
    },
  },
  FarmEventCD = {
    history = {
      isMap = true,
      keyName = "evtKey",
    },
    history_deleted = {
      isMapDel = true,
      targetFieldName = "history",
    },
    selfCD = {
      isMap = true,
      keyName = "evtKey",
    },
    selfCD_deleted = {
      isMapDel = true,
      targetFieldName = "selfCD",
    },
    sharedCD = {
      isMap = true,
      keyName = "mutexID",
    },
    sharedCD_deleted = {
      isMapDel = true,
      targetFieldName = "sharedCD",
    },
  },
  FarmEventHistory = {
    evtKey = {
    },
    times = {
    },
  },
  FarmEventInfo = {
    commitTimes = {
    },
    customData = {
      isMap = true,
      keyName = "dataKey",
    },
    customData_deleted = {
      isMapDel = true,
      targetFieldName = "customData",
    },
    events = {
      isMap = true,
      keyName = "id",
    },
    events_deleted = {
      isMapDel = true,
      targetFieldName = "events",
    },
    evtIDGen = {
    },
    farmTimer = {
      isMsg = true,
    },
    globalCD = {
    },
    history = {
      isMsg = true,
    },
    lastCommit = {
    },
    notTriggerCount = {
      isMap = true,
      keyName = "evtKey",
    },
    notTriggerCount_deleted = {
      isMapDel = true,
      targetFieldName = "notTriggerCount",
    },
    resetTime = {
    },
    seriesStep = {
      isMap = true,
      keyName = "series",
    },
    seriesStep_deleted = {
      isMapDel = true,
      targetFieldName = "seriesStep",
    },
  },
  FarmEventSeriesStep = {
    series = {
    },
    step = {
    },
  },
  FarmEventSync = {
    events = {
      isMap = true,
      keyName = "id",
    },
    events_deleted = {
      isMapDel = true,
      targetFieldName = "events",
    },
    evtDelCloud = {
      isAry = true,
    },
    evtDelCloud_is_cleared = {
      isAryClear = true,
      targetFieldName = "evtDelCloud",
    },
    farmTimer = {
      isMsg = true,
    },
    globalCD = {
    },
  },
  FarmEventValue = {
    id = {
    },
    iv = {
    },
    sv = {
    },
  },
  FarmEvictInfo = {
    farmId = {
    },
    reason = {
    },
    updateTime = {
    },
  },
  FarmEvtNotTrigger = {
    eventID = {
    },
    evtKey = {
    },
    lastAddMs = {
    },
    series = {
    },
    specialTimes = {
    },
    times = {
    },
    todayCount = {
    },
  },
  FarmEvtSelfCD = {
    endTime = {
    },
    evtKey = {
    },
  },
  FarmEvtSharedCD = {
    endTime = {
    },
    mutexID = {
    },
  },
  FarmExternalOperateInfo = {
    lastOpCntResetTimeMs = {
    },
    opCntThisWeek = {
    },
  },
  FarmFishBowl = {
    billNo = {
    },
    id = {
    },
    itemId = {
    },
  },
  FarmFishCardLevel = {
    exp = {
    },
    level = {
    },
    levelUpTime = {
    },
    type = {
    },
  },
  FarmFishingGuaranteedInfo = {
    ActivityFishGuaranteed = {
      isMap = true,
      keyName = "activityId",
    },
    ActivityFishGuaranteed_deleted = {
      isMapDel = true,
      targetFieldName = "ActivityFishGuaranteed",
    },
    FishPriceGuaranteed = {
      isMap = true,
      keyName = "k",
    },
    FishPriceGuaranteed_deleted = {
      isMapDel = true,
      targetFieldName = "FishPriceGuaranteed",
    },
    FishTypeGuaranteed = {
      isMap = true,
      keyName = "k",
    },
    FishTypeGuaranteed_deleted = {
      isMapDel = true,
      targetFieldName = "FishTypeGuaranteed",
    },
    FishTypeGuaranteedFloat = {
      isMap = true,
      keyName = "k",
    },
    FishTypeGuaranteedFloat_deleted = {
      isMapDel = true,
      targetFieldName = "FishTypeGuaranteedFloat",
    },
    QualityGuaranteed = {
      isMap = true,
      keyName = "k",
    },
    QualityGuaranteed_deleted = {
      isMapDel = true,
      targetFieldName = "QualityGuaranteed",
    },
    QualitySGuaranteed = {
      isMap = true,
      keyName = "k",
    },
    QualitySGuaranteed_deleted = {
      isMapDel = true,
      targetFieldName = "QualitySGuaranteed",
    },
    QualitySSGuaranteed = {
      isMap = true,
      keyName = "k",
    },
    QualitySSGuaranteed_deleted = {
      isMapDel = true,
      targetFieldName = "QualitySSGuaranteed",
    },
    QualitySSSGuaranteed = {
      isMap = true,
      keyName = "k",
    },
    QualitySSSGuaranteed_deleted = {
      isMapDel = true,
      targetFieldName = "QualitySSSGuaranteed",
    },
  },
  FarmFishingInfo = {
    fishingCount = {
    },
    stealFishCount = {
    },
  },
  FarmFishingRecord = {
    farmExp = {
    },
    finalScore = {
    },
    fishCardLevel = {
    },
    fishCardPackCoin = {
    },
    fishCardPackExp = {
    },
    fishPrice = {
    },
    fishType = {
    },
    idx = {
    },
    isFishCardPack = {
    },
    itemGet = {
      isMap = true,
      keyName = "itemId",
    },
    itemGet_deleted = {
      isMapDel = true,
      targetFieldName = "itemGet",
    },
    luckyForCardPack = {
    },
    luckyHook = {
    },
    newFish = {
    },
    newRecord = {
    },
    playerUid = {
    },
    ripeWeekend = {
    },
    startScore = {
    },
    startTimeStamp = {
    },
    state = {
    },
  },
  FarmFishingReport = {
    monthReport = {
      isMap = true,
      keyName = "startTimeStamp",
    },
    monthReport_deleted = {
      isMapDel = true,
      targetFieldName = "monthReport",
    },
    weekReport = {
      isMap = true,
      keyName = "startTimeStamp",
    },
    weekReport_deleted = {
      isMapDel = true,
      targetFieldName = "weekReport",
    },
  },
  FarmFishingReportUnit = {
    reportUnitByLayer = {
      isMap = true,
      keyName = "layer",
    },
    reportUnitByLayer_deleted = {
      isMapDel = true,
      targetFieldName = "reportUnitByLayer",
    },
    startTimeStamp = {
    },
  },
  FarmFishingReportUnitByBait = {
    bait = {
    },
    baitNum = {
    },
    baitPrice = {
    },
    beStolenCount = {
    },
    crownCount = {
    },
    farmExp = {
    },
    fishCardExp = {
    },
    fishCardExpireCoin = {
    },
    fishCount = {
    },
    fishingSuccessCount = {
    },
    fishPrice = {
    },
    orangeCount = {
    },
    talentFishCount = {
    },
  },
  FarmFishingReportUnitByLayer = {
    fishCardLevelAverageOverPurpleAfter = {
    },
    fishCardLevelAverageOverPurpleBefore = {
    },
    layer = {
    },
    reportUnitByBait = {
      isMap = true,
      keyName = "bait",
    },
    reportUnitByBait_deleted = {
      isMapDel = true,
      targetFieldName = "reportUnitByBait",
    },
  },
  FarmFishPoolInfo = {
    plantInfo = {
      isMsg = true,
    },
    produceInfo = {
      isMsg = true,
    },
    ripeInfo = {
      isMsg = true,
    },
    state = {
    },
    stealInfo = {
      isMsg = true,
    },
  },
  FarmGift = {
    billNo = {
    },
    bp = {
    },
    critical = {
    },
    expirtTime = {
    },
    face = {
    },
    giftID64 = {
    },
    giftType = {
    },
    headFrame = {
      isMsg = true,
    },
    itemCount = {
    },
    itemID = {
    },
    msg = {
    },
    msgBak = {
    },
    name = {
    },
    openId = {
    },
    pos = {
    },
    ratio = {
    },
    skinID = {
    },
    time = {
    },
    uid = {
    },
  },
  FarmGiftHeadFrame = {
    dressUpType = {
    },
    itemId = {
    },
    itemUUID = {
    },
  },
  FarmGiftList = {
    giftPickIndex = {
    },
    giftReserved = {
      isMap = true,
      keyName = "billNo",
    },
    giftReserved_deleted = {
      isMapDel = true,
      targetFieldName = "giftReserved",
    },
    gifts = {
      isMap = true,
      keyName = "giftID64",
    },
    gifts_deleted = {
      isMapDel = true,
      targetFieldName = "gifts",
    },
    history = {
      isMap = true,
      keyName = "target",
    },
    history_deleted = {
      isMapDel = true,
      targetFieldName = "history",
    },
    lastActivityCheckTime = {
    },
    lastRecvTime = {
    },
    lastSendGift = {
    },
    lastSendTimeBak = {
    },
    record = {
      isMap = true,
      keyName = "index",
    },
    record_deleted = {
      isMapDel = true,
      targetFieldName = "record",
    },
    recvTimes = {
    },
    sendTimes = {
    },
    sendUids = {
      isAry = true,
    },
    sendUids_is_cleared = {
      isAryClear = true,
      targetFieldName = "sendUids",
    },
  },
  FarmGiftRecord = {
    gift = {
      isMsg = true,
    },
    index = {
    },
    pickTime = {
    },
  },
  FarmGiftReserveFlag = {
    addTime = {
    },
    billNo = {
    },
  },
  FarmGodFigure = {
    currPrayTimes = {
    },
    lastPrayingTime = {
    },
    nextBigTime = {
    },
  },
  FarmHandBook = {
    awardGot = {
      isSet = true,
    },
    awardGot_deleted = {
      isSetDel = true,
      targetFieldName = "awardGot",
    },
    category = {
    },
    handBookRecords = {
      isMap = true,
      keyName = "type",
    },
    handBookRecords_deleted = {
      isMapDel = true,
      targetFieldName = "handBookRecords",
    },
  },
  FarmHandBookRecord = {
    maxScore = {
    },
    type = {
    },
  },
  FarmHotBuffHistory = {
    hotBuffMap = {
      isMap = true,
      keyName = "farmID",
    },
    hotBuffMap_deleted = {
      isMapDel = true,
      targetFieldName = "hotBuffMap",
    },
  },
  FarmHotInfo = {
    hotValueStatisticInfo = {
      isMap = true,
      keyName = "timeKey",
    },
    hotValueStatisticInfo_deleted = {
      isMapDel = true,
      targetFieldName = "hotValueStatisticInfo",
    },
  },
  FarmHotSpring = {
    buffs = {
      isAry = true,
    },
    buffs_is_cleared = {
      isAryClear = true,
      targetFieldName = "buffs",
    },
    buffSource = {
      isMsg = true,
    },
    curBuffs = {
      isAry = true,
    },
    curBuffs_is_cleared = {
      isAryClear = true,
      targetFieldName = "curBuffs",
    },
    curHotSpringUid = {
    },
    enterTime = {
    },
    expRewardTimes = {
    },
    grade = {
    },
    hotBuffHistory = {
      isMsg = true,
    },
    hotType = {
    },
    lastGetBuffTime = {
    },
    lastRefreshMs = {
    },
    nextBuffResetTime = {
    },
    nextResetTime = {
    },
    resetTime = {
    },
    visitors = {
      isMap = true,
      keyName = "uid",
    },
    visitors_deleted = {
      isMapDel = true,
      targetFieldName = "visitors",
    },
  },
  FarmHotSpringBuffSource = {
    name = {
    },
    uid = {
    },
  },
  FarmHotSpringVisitor = {
    uid = {
    },
  },
  FarmHotValue = {
    hotCount = {
    },
    type = {
    },
  },
  FarmHotValueStatisticInfo = {
    hotValue = {
      isMap = true,
      keyName = "type",
    },
    hotValue_deleted = {
      isMapDel = true,
      targetFieldName = "hotValue",
    },
    timeDimension = {
    },
    timeKey = {
    },
  },
  FarmInfo = {
    enterFarmTime = {
    },
    farmEvent = {
      isMsg = true,
    },
    farmMonthlyPass = {
      isMsg = true,
    },
    farmSocialInfo = {
      isMsg = true,
    },
    farmWeekendInfo = {
      isMsg = true,
    },
    fishingInfo = {
      isMsg = true,
    },
    liuYanMessageInfo = {
      isMsg = true,
    },
    myFarmInfo = {
      isMsg = true,
    },
    myNPCFarmInfo = {
      isMsg = true,
    },
    newbieState = {
      isMsg = true,
    },
    npcHouseInfo = {
      isMsg = true,
    },
    obtainExpRecord = {
      isMsg = true,
    },
    visitFarmInfo = {
      isMsg = true,
    },
  },
  FarmingInfo = {
    todayHarvestCropObtainNum = {
    },
    todayWaterRewardTimes = {
    },
  },
  FarmItem = {
    itemId = {
    },
    itemNum = {
    },
    para = {
    },
    uuid = {
    },
  },
  FarmItemInfo = {
    item = {
      isMap = true,
      keyName = "uuid",
    },
    item_deleted = {
      isMapDel = true,
      targetFieldName = "item",
    },
    money = {
      isMap = true,
      keyName = "itemId",
    },
    money_deleted = {
      isMapDel = true,
      targetFieldName = "money",
    },
  },
  FarmKirin = {
    collectFarmExp = {
    },
    incubateEndTime = {
    },
    incubateStartTime = {
    },
    isCanEvo = {
    },
    level = {
    },
    mana = {
    },
    state = {
    },
  },
  FarmLeavingVillager = {
    birthTime = {
    },
    everAcceptHobbyGift = {
    },
    favorValue = {
    },
    fullFavorGiftState = {
    },
    historyStayTime = {
    },
    id = {
    },
    leavingTime = {
    },
    receivedExpFavor = {
    },
  },
  FarmLiuYanMessageInfo = {
    lastPullId = {
    },
    newNumber = {
    },
    permission = {
    },
    totalNumber = {
    },
  },
  FarmLiuYanMessageRecord = {
    farmId = {
    },
    recordCount = {
    },
  },
  FarmMagic = {
    magics = {
      isMap = true,
      keyName = "id",
    },
    magics_deleted = {
      isMapDel = true,
      targetFieldName = "magics",
    },
  },
  FarmMagicExInfo = {
    isEquip = {
    },
  },
  FarmMagicInfo = {
    exInfo = {
      isMsg = true,
    },
    expire = {
    },
    id = {
    },
    lastUseTime = {
    },
    mpInfo = {
      isMsg = true,
    },
  },
  FarmMagicMpInfo = {
    mp = {
    },
    mpLastRecoverTime = {
    },
  },
  FarmModuleOpenTime = {
    moduleId = {
    },
    openTime = {
    },
  },
  FarmModuleOpenTimeInfo = {
    activityOpenTimeMap = {
      isMap = true,
      keyName = "activityId",
    },
    activityOpenTimeMap_deleted = {
      isMapDel = true,
      targetFieldName = "activityOpenTimeMap",
    },
    moduleOpenTimeMap = {
      isMap = true,
      keyName = "moduleId",
    },
    moduleOpenTimeMap_deleted = {
      isMapDel = true,
      targetFieldName = "moduleOpenTimeMap",
    },
  },
  FarmMoney = {
    itemId = {
    },
    itemNum = {
    },
  },
  FarmMonthlyPass = {
    effectTime = {
    },
    endTime = {
    },
    hasSkinID = {
      isSet = true,
    },
    hasSkinID_deleted = {
      isSetDel = true,
      targetFieldName = "hasSkinID",
    },
    isEffective = {
    },
    lastBuyTime = {
    },
    timeslot = {
      isMap = true,
      keyName = "timeStart",
    },
    timeslot_deleted = {
      isMapDel = true,
      targetFieldName = "timeslot",
    },
  },
  FarmNewPlayerInfo = {
    animalFeedFlagUpdated = {
    },
    cropConnectionUpdated = {
    },
    farmStatusViewUpdated = {
    },
    newPlayerSpeedCropRecords = {
      isMap = true,
      keyName = "cropId",
    },
    newPlayerSpeedCropRecords_deleted = {
      isMapDel = true,
      targetFieldName = "newPlayerSpeedCropRecords",
    },
    redFoxStealTeach = {
    },
  },
  FarmNewPlayerSpeedCropRecord = {
    cropId = {
    },
    plantCnt = {
    },
    speedCnt = {
    },
  },
  FarmObtainMainExpRecord = {
    building = {
      isMap = true,
      keyName = "confId",
    },
    building_deleted = {
      isMapDel = true,
      targetFieldName = "building",
    },
  },
  FarmOwnerInfo = {
    isOnline = {
    },
  },
  FarmPartyInfo = {
    expireTime = {
    },
    farmPartyPublicInfo = {
      isMsg = true,
    },
    farmPartyTlogRequiredFields = {
      isMsg = true,
    },
    lastFarmLevel = {
    },
    lastHotSafeRatio = {
    },
    lastHotValue = {
    },
    lastIsInParty = {
    },
    lastIsWhiteList = {
    },
    lastNickName = {
    },
    lastPublishTimeMills = {
    },
    lastUpdateTime = {
    },
    lastVersionGroup = {
    },
    lastVisitorCount = {
    },
  },
  FarmPartyPublicInfo = {
    content = {
    },
    expireTime = {
    },
    image = {
      isMsg = true,
    },
    isOpen = {
    },
    lastPublishTimeMills = {
    },
    scene = {
    },
    url = {
    },
    visitorCount = {
    },
  },
  FarmPartyTlogRequiredFields = {
    maxHotValue = {
    },
    maxVisitorNum = {
    },
    visitorUIDs = {
      isSet = true,
    },
    visitorUIDs_deleted = {
      isSetDel = true,
      targetFieldName = "visitorUIDs",
    },
  },
  FarmPet = {
    clientCache = {
      isMap = true,
      keyName = "k",
    },
    clientCache_deleted = {
      isMapDel = true,
      targetFieldName = "clientCache",
    },
    favorFullGiftItems = {
      isMap = true,
      keyName = "itemId",
    },
    favorFullGiftItems_deleted = {
      isMapDel = true,
      targetFieldName = "favorFullGiftItems",
    },
    favorFullGiftStatus = {
    },
    favorValue = {
    },
    favorValueHistory = {
    },
    feedValue = {
    },
    feedValueLastUpdateTime = {
    },
    id = {
    },
    lastChangeNameTime = {
    },
    name = {
    },
    obtainTime = {
    },
    wearClothing = {
      isMap = true,
      keyName = "clothingType",
    },
    wearClothing_deleted = {
      isMapDel = true,
      targetFieldName = "wearClothing",
    },
  },
  FarmPetInfo = {
    catFavorInfo = {
      isMsg = true,
    },
    catFishingInfo = {
      isMsg = true,
    },
    catFoodValue = {
    },
    catGiftInfo = {
      isMsg = true,
    },
    cats = {
      isMap = true,
      keyName = "id",
    },
    cats_deleted = {
      isMapDel = true,
      targetFieldName = "cats",
    },
    clothingInfo = {
      isMsg = true,
    },
    favorInfo = {
      isMsg = true,
    },
    fertilizeInfo = {
      isMsg = true,
    },
    foodValue = {
    },
    giftInfo = {
      isMsg = true,
    },
    houseKeepingDisable = {
    },
    petModuleOpenTimeMs = {
    },
    pets = {
      isMap = true,
      keyName = "id",
    },
    pets_deleted = {
      isMapDel = true,
      targetFieldName = "pets",
    },
    securityInfo = {
      isMsg = true,
    },
    summonedCatId = {
    },
    summonedPetId = {
    },
    wildCatFavorInfo = {
      isMsg = true,
    },
    wildCatId = {
    },
    wildCatRefreshInfo = {
      isMsg = true,
    },
    wildCats = {
      isMap = true,
      keyName = "id",
    },
    wildCats_deleted = {
      isMapDel = true,
      targetFieldName = "wildCats",
    },
  },
  FarmPinFriend = {
    uid = {
    },
    updateTime = {
    },
  },
  FarmPlayerLiuYanMessageInfo = {
    bannedTime = {
    },
    dayRecord = {
      isMap = true,
      keyName = "farmId",
    },
    dayRecord_deleted = {
      isMapDel = true,
      targetFieldName = "dayRecord",
    },
  },
  FarmPublicInfo = {
    canStealTimeInfo = {
      isMap = true,
      keyName = "playerUID",
    },
    canStealTimeInfo_deleted = {
      isMapDel = true,
      targetFieldName = "canStealTimeInfo",
    },
    cookCanStealTimeInfo = {
      isMap = true,
      keyName = "playerUID",
    },
    cookCanStealTimeInfo_deleted = {
      isMapDel = true,
      targetFieldName = "cookCanStealTimeInfo",
    },
    farmBuildingSkins = {
      isMap = true,
      keyName = "id",
    },
    farmBuildingSkins_deleted = {
      isMapDel = true,
      targetFieldName = "farmBuildingSkins",
    },
    farmLevel = {
    },
    farmMonthCardEndTime = {
    },
    farmPartyInfo = {
      isMsg = true,
    },
    farmPetClothing = {
      isMap = true,
      keyName = "petId",
    },
    farmPetClothing_deleted = {
      isMapDel = true,
      targetFieldName = "farmPetClothing",
    },
    farmSafeInfo = {
      isMsg = true,
    },
    farmSignature = {
    },
    farmSignatureEXP = {
    },
    farmVisitorCount = {
    },
    lastChangeSignatureMs = {
    },
    lastLoginTimeMs = {
    },
    totalLiuYanMessageNumber = {
    },
  },
  FarmPunish = {
    enable = {
    },
    endTime = {
    },
    reason = {
    },
  },
  FarmReport = {
    dayReport = {
      isMap = true,
      keyName = "startTimeStamp",
    },
    dayReport_deleted = {
      isMapDel = true,
      targetFieldName = "dayReport",
    },
    monthReport = {
      isMap = true,
      keyName = "startTimeStamp",
    },
    monthReport_deleted = {
      isMapDel = true,
      targetFieldName = "monthReport",
    },
    reportType = {
    },
    weekReport = {
      isMap = true,
      keyName = "startTimeStamp",
    },
    weekReport_deleted = {
      isMapDel = true,
      targetFieldName = "weekReport",
    },
  },
  FarmReportData = {
    category = {
    },
    farmReportUnit = {
      isMsg = true,
    },
    reportType = {
    },
  },
  FarmReportInfo = {
    endTimeStamp = {
    },
    reportData = {
      isMap = true,
      keyName = "category",
    },
    reportData_deleted = {
      isMapDel = true,
      targetFieldName = "reportData",
    },
    startTimeStamp = {
    },
  },
  FarmReportUnit = {
    farmStealReportUnit = {
      isMsg = true,
    },
  },
  FarmReturningInfo = {
    enterFarmTime = {
    },
    rtnActInfo = {
      isMap = true,
      keyName = "activityId",
    },
    rtnActInfo_deleted = {
      isMapDel = true,
      targetFieldName = "rtnActInfo",
    },
  },
  FarmReturningTask = {
    currentTaskId = {
    },
    taskProcess = {
    },
  },
  FarmRoom = {
    ceilPaperInfo = {
      isMsg = true,
    },
    confId = {
    },
    doorInfo = {
      isMsg = true,
    },
    exitState = {
    },
    extendInfo = {
      isMap = true,
      keyName = "surface",
    },
    extendInfo_deleted = {
      isMapDel = true,
      targetFieldName = "extendInfo",
    },
    floor = {
    },
    floorPaperInfo = {
      isMsg = true,
    },
    height = {
    },
    id = {
    },
    lastEditTime = {
    },
    lastExtendLevel = {
    },
    length = {
    },
    maxExtendedLevel = {
    },
    pos = {
    },
    roomDsInfo = {
      isMsg = true,
    },
    roomVisitorInfo = {
      isMsg = true,
    },
    tmpExtendInfo = {
      isMap = true,
      keyName = "surface",
    },
    tmpExtendInfo_deleted = {
      isMapDel = true,
      targetFieldName = "tmpExtendInfo",
    },
    wallPaperInfo = {
      isMap = true,
      keyName = "id",
    },
    wallPaperInfo_deleted = {
      isMapDel = true,
      targetFieldName = "wallPaperInfo",
    },
    width = {
    },
  },
  FarmRoomExtendInfo = {
    extendCount = {
    },
    surface = {
    },
  },
  FarmRoomItem = {
    confId = {
    },
    direction = {
    },
    furnitureType = {
    },
    id = {
    },
    parentInstanceId = {
    },
    pos = {
    },
    roomId = {
    },
    skinConfId = {
    },
    skinType = {
    },
    subState = {
    },
    wallSurface = {
    },
  },
  FarmSafeInfo = {
    giftSendingForbidden = {
      isMsg = true,
    },
    hotRatio = {
    },
    hotRatioEndTime = {
    },
    partyPublishForbidden = {
      isMsg = true,
    },
    putDown = {
      isMsg = true,
    },
  },
  FarmSceneDrop = {
    selfDrops = {
      isMap = true,
      keyName = "uid",
    },
    selfDrops_deleted = {
      isMapDel = true,
      targetFieldName = "selfDrops",
    },
  },
  FarmSceneDropFarmItem = {
    itemId = {
    },
    itemNum = {
    },
  },
  FarmSceneDropInfo = {
    billNo = {
    },
    dropType = {
    },
    expireTimeSec = {
    },
    farmItems = {
      isMap = true,
      keyName = "itemId",
    },
    farmItems_deleted = {
      isMapDel = true,
      targetFieldName = "farmItems",
    },
    inCook = {
    },
    inFarmPos = {
    },
    inHousePos = {
    },
    locatedFarmId = {
    },
    ownerId = {
    },
    placeType = {
    },
    uid = {
    },
  },
  FarmSendHistory = {
    giftCount = {
    },
    target = {
    },
  },
  FarmSignature = {
    lastChangeMs = {
    },
    readInfos = {
      isMap = true,
      keyName = "uid",
    },
    readInfos_deleted = {
      isMapDel = true,
      targetFieldName = "readInfos",
    },
  },
  FarmSignatureRead = {
    readMs = {
    },
    uid = {
    },
  },
  FarmSocialInfo = {
    farmBeBlocked = {
      isMap = true,
      keyName = "uid",
    },
    farmBeBlocked_deleted = {
      isMapDel = true,
      targetFieldName = "farmBeBlocked",
    },
    farmBlockFriends = {
      isMap = true,
      keyName = "uid",
    },
    farmBlockFriends_deleted = {
      isMapDel = true,
      targetFieldName = "farmBlockFriends",
    },
    farmEvict = {
      isMap = true,
      keyName = "farmId",
    },
    farmEvict_deleted = {
      isMapDel = true,
      targetFieldName = "farmEvict",
    },
    farmFriendNum = {
    },
    farmPinnedFriends = {
      isMap = true,
      keyName = "uid",
    },
    farmPinnedFriends_deleted = {
      isMapDel = true,
      targetFieldName = "farmPinnedFriends",
    },
    hasSyncBlockInfoTag = {
    },
    lastFertilizeTime = {
    },
    lastHitBadGuyListTime = {
    },
    lastStealingTime = {
    },
    lastStealRequestTime = {
    },
    todayFertilizeCount = {
    },
    todayStealingCount = {
    },
  },
  FarmStatisticInfo = {
    dailyAddFarmCoin = {
    },
    dailyAddFarmExp = {
    },
    dailyStealCorpTimes = {
    },
    lastCoinWarningTime = {
    },
    lastExpWarningTime = {
    },
    lastUpdateTime = {
    },
  },
  FarmStatusViewInfo = {
    animalCanEncourageTime = {
    },
    animalCanEncourageType = {
    },
    animalHungryTime = {
    },
    animalHungryType = {
    },
    animalRipeTime = {
    },
    animalRipeType = {
    },
    aquariumRipeTime = {
    },
    collectionDropInFarmTime = {
    },
    collectionDropTime = {
    },
    cookCanOpenTime = {
    },
    cookVisitantCanPrebookTime = {
    },
    cookVisitantCanServeTime = {
    },
    cookVisitantProtectEndTime = {
    },
    cropDryTime = {
    },
    cropDryType = {
    },
    cropRipeTime = {
    },
    cropRipeType = {
    },
    fishPoolLayer = {
    },
    fishProtectTime = {
    },
    fishRipeTime = {
    },
    godFigureCanPrayTime = {
    },
    kirinCanCollectManaTime = {
    },
    kirinIncubateFinishTime = {
    },
    machineRipeTime = {
    },
    machineRipeType = {
    },
    magicSkillMpFullTime = {
    },
    normalCropRipeTime = {
    },
    normalCropRipeType = {
    },
  },
  FarmStealingInfo = {
    badGuysRedDot = {
      isMap = true,
      keyName = "friendId",
    },
    badGuysRedDot_deleted = {
      isMapDel = true,
      targetFieldName = "badGuysRedDot",
    },
    blockCanSteal = {
      isMap = true,
      keyName = "friendId",
    },
    blockCanSteal_deleted = {
      isMapDel = true,
      targetFieldName = "blockCanSteal",
    },
    blockFertilize = {
      isMap = true,
      keyName = "friendId",
    },
    blockFertilize_deleted = {
      isMapDel = true,
      targetFieldName = "blockFertilize",
    },
    canFertilizedCategory = {
    },
    cloudTaxRedDotTime = {
    },
    doGoodersRedDot = {
      isMap = true,
      keyName = "friendId",
    },
    doGoodersRedDot_deleted = {
      isMapDel = true,
      targetFieldName = "doGoodersRedDot",
    },
    evictRecord = {
      isMap = true,
      keyName = "friendId",
    },
    evictRecord_deleted = {
      isMapDel = true,
      targetFieldName = "evictRecord",
    },
    lastBeFertilizedTime = {
    },
    petEvictRedDot = {
      isMap = true,
      keyName = "friendId",
    },
    petEvictRedDot_deleted = {
      isMapDel = true,
      targetFieldName = "petEvictRedDot",
    },
    petFeedRedDot = {
      isMap = true,
      keyName = "friendId",
    },
    petFeedRedDot_deleted = {
      isMapDel = true,
      targetFieldName = "petFeedRedDot",
    },
    stealingRecord = {
      isMap = true,
      keyName = "friendId",
    },
    stealingRecord_deleted = {
      isMapDel = true,
      targetFieldName = "stealingRecord",
    },
    strangerRecommendTime = {
    },
    todayBeFertilizedCount = {
    },
  },
  FarmStealReportUnit = {
    coinGain = {
    },
    crownFishCount = {
    },
    orangeFishCount = {
    },
    stealNum = {
    },
  },
  FarmTalentInfo = {
    talents = {
      isMap = true,
      keyName = "id",
    },
    talents_deleted = {
      isMapDel = true,
      targetFieldName = "talents",
    },
  },
  FarmTalentUnit = {
    id = {
    },
    level = {
    },
    unlockTime = {
    },
  },
  FarmTask = {
    completeConditionInfo = {
      isMsg = true,
    },
    id = {
    },
    pos = {
    },
    status = {
    },
    timeInfo = {
      isMsg = true,
    },
  },
  FarmTaskConditionInfo = {
    currentVal = {
    },
    type = {
    },
  },
  FarmTaskFinish = {
    chainHeadId = {
    },
    finishTime = {
    },
    id = {
    },
  },
  FarmTaskInfo = {
    finishTask = {
      isMap = true,
      keyName = "chainHeadId",
    },
    finishTask_deleted = {
      isMapDel = true,
      targetFieldName = "finishTask",
    },
    runningTask = {
      isMap = true,
      keyName = "id",
    },
    runningTask_deleted = {
      isMapDel = true,
      targetFieldName = "runningTask",
    },
  },
  FarmTaskTimeInfo = {
    completeTime = {
    },
    initTime = {
    },
    triggerTime = {
    },
  },
  FarmTimer = {
    eid = {
      isAry = true,
    },
    eid_is_cleared = {
      isAryClear = true,
      targetFieldName = "eid",
    },
    endTimeMs = {
    },
  },
  FarmTlogRequiredFields = {
    appId = {
    },
    city = {
    },
    clientIP = {
    },
    clientPlat = {
    },
    clientVersion = {
    },
    country = {
    },
    district = {
    },
    iSequence = {
    },
    level = {
    },
    network = {
    },
    npc = {
    },
    platID = {
    },
    province = {
    },
    ReservePara = {
      isAry = true,
    },
    ReservePara_is_cleared = {
      isAryClear = true,
      targetFieldName = "ReservePara",
    },
    seasonId = {
    },
    ServerIp = {
    },
    telecomOper = {
    },
    vClientIPV6 = {
    },
  },
  FarmVillager = {
    acceptGift = {
      isMsg = true,
    },
    birthTime = {
    },
    changedBPIdx = {
    },
    clientCache = {
      isMap = true,
      keyName = "k",
    },
    clientCache_deleted = {
      isMapDel = true,
      targetFieldName = "clientCache",
    },
    controlClient = {
    },
    dailyFavor = {
      isMsg = true,
    },
    everAcceptHobbyGift = {
    },
    favorValue = {
    },
    festivalGift = {
      isMsg = true,
    },
    fullFavorGiftState = {
    },
    hide = {
    },
    historyStayTime = {
    },
    id = {
    },
    lastWalletRefreshTime = {
    },
    nowPosBuildingId = {
    },
    obtainTime = {
    },
    presentGift = {
      isMsg = true,
    },
    receivedExpFavor = {
    },
    stayStaticPeriod = {
    },
    triggeredGift = {
      isMap = true,
      keyName = "giftType",
    },
    triggeredGift_deleted = {
      isMapDel = true,
      targetFieldName = "triggeredGift",
    },
    villaId = {
    },
    wallet = {
    },
  },
  FarmVillagerInfo = {
    dailyRefreshTime = {
    },
    gift = {
      isMsg = true,
    },
    lastRefreshedVillagerId = {
    },
    lastRefreshTime = {
    },
    lastWaitingRefreshTime = {
    },
    leavingVillagers = {
      isMap = true,
      keyName = "id",
    },
    leavingVillagers_deleted = {
      isMapDel = true,
      targetFieldName = "leavingVillagers",
    },
    notFirstRefresh = {
    },
    villagers = {
      isMap = true,
      keyName = "id",
    },
    villagers_deleted = {
      isMapDel = true,
      targetFieldName = "villagers",
    },
    waitingVillager = {
      isMsg = true,
    },
  },
  FarmWaitingVillager = {
    appearTime = {
    },
    changedBPIdx = {
    },
    disappearReason = {
    },
    favorValue = {
    },
    id = {
    },
    returning = {
    },
  },
  FarmWeatherInfo = {
    firstRainWeatherFlag = {
    },
    lightningProtectEndTime = {
    },
    rainProtectCanceled = {
    },
    weatherBeginTime = {
    },
    weatherEndTime = {
    },
    weatherId = {
    },
    weatherTriggeredCount = {
      isMap = true,
      keyName = "k",
    },
    weatherTriggeredCount_deleted = {
      isMapDel = true,
      targetFieldName = "weatherTriggeredCount",
    },
  },
  FarmWeekendInfo = {
    endTime = {
    },
    startTime = {
    },
  },
  FarmWelcomeInfo = {
    bannedTime = {
    },
    content = {
    },
  },
  FarmWildCat = {
    arriveTime = {
    },
    canTame = {
    },
    favorValue = {
    },
    favorValueHistory = {
    },
    id = {
    },
    leaveTime = {
    },
    name = {
    },
  },
  FashionFundData = {
    afterReturn = {
      isSet = true,
    },
    afterReturn_deleted = {
      isSetDel = true,
      targetFieldName = "afterReturn",
    },
    beforeReturn = {
      isSet = true,
    },
    beforeReturn_deleted = {
      isSetDel = true,
      targetFieldName = "beforeReturn",
    },
    purchasedGoods = {
      isSet = true,
    },
    purchasedGoods_deleted = {
      isSetDel = true,
      targetFieldName = "purchasedGoods",
    },
  },
  FeatureIntegrationData = {
    curAwardNum = {
    },
    pictureSet = {
      isSet = true,
    },
    pictureSet_deleted = {
      isSetDel = true,
      targetFieldName = "pictureSet",
    },
  },
  FertilizeHistory = {
    gridId = {
    },
    operatorUid = {
    },
    recordBytes = {
    },
    recordTime = {
    },
    traceId = {
    },
  },
  FindPartnerData = {
    partnerUid = {
    },
    questionList = {
      isMap = true,
      keyName = "questionId",
    },
    questionList_deleted = {
      isMapDel = true,
      targetFieldName = "questionList",
    },
    trophyCnt = {
    },
  },
  FindPartnerQuestionAnswer = {
    choiceId = {
    },
    questionId = {
    },
  },
  FireworksInfo = {
    showText = {
    },
  },
  FishingActivityRecord = {
    activityId = {
    },
    fishingActivityRecord = {
      isMap = true,
      keyName = "rankId",
    },
    fishingActivityRecord_deleted = {
      isMapDel = true,
      targetFieldName = "fishingActivityRecord",
    },
  },
  FishingActivityRecordOfRank = {
    finalScore = {
    },
    fishId = {
    },
    rankId = {
    },
    updateEpochSecs = {
    },
  },
  FishingFameActivityData = {
    fishingFameRecord = {
      isMap = true,
      keyName = "index",
    },
    fishingFameRecord_deleted = {
      isMapDel = true,
      targetFieldName = "fishingFameRecord",
    },
    lastRedDotTimeBucket = {
    },
  },
  FishingFameRecord = {
    finalScore = {
    },
    fishId = {
    },
    index = {
    },
  },
  FishingHallOfFameActivityData = {
    fishingActivityRecord = {
      isMap = true,
      keyName = "activityId",
    },
    fishingActivityRecord_deleted = {
      isMapDel = true,
      targetFieldName = "fishingActivityRecord",
    },
  },
  FishPoolPlantInfo = {
    bait = {
    },
    baitCount = {
    },
    baitPrice = {
    },
    layer = {
    },
    period = {
    },
    startTime = {
    },
  },
  FishPoolProduceInfo = {
    FishingRecords = {
      isMap = true,
      keyName = "idx",
    },
    FishingRecords_deleted = {
      isMapDel = true,
      targetFieldName = "FishingRecords",
    },
    leftFishingCount = {
    },
    totalFishingCount = {
    },
  },
  FishPoolRipeInfo = {
    fishCardLevelAverageOverPurpleWhenRipe = {
    },
    ripeQuality = {
    },
    ripeTime = {
    },
    ripeWeekend = {
    },
  },
  FishPoolStealInfo = {
    ignoredUIDs = {
      isSet = true,
    },
    ignoredUIDs_deleted = {
      isSetDel = true,
      targetFieldName = "ignoredUIDs",
    },
    stealFailCount = {
      isMap = true,
      keyName = "k",
    },
    stealFailCount_deleted = {
      isMapDel = true,
      targetFieldName = "stealFailCount",
    },
    stealProtectCanceled = {
    },
    stolenCount = {
    },
    stolenUIDs = {
      isMap = true,
      keyName = "k",
    },
    stolenUIDs_deleted = {
      isMapDel = true,
      targetFieldName = "stolenUIDs",
    },
  },
  FishPriceRecord = {
    bait = {
    },
    k = {
    },
    layer = {
    },
    values = {
      isAry = true,
    },
    values_is_cleared = {
      isAryClear = true,
      targetFieldName = "values",
    },
  },
  FittingSlot = {
    dresses = {
      isMap = true,
      keyName = "dressType",
    },
    dresses_deleted = {
      isMapDel = true,
      targetFieldName = "dresses",
    },
    id = {
    },
    random = {
    },
    unlock = {
    },
  },
  FittingSlots = {
    createRoleDresses = {
    },
    currId = {
    },
    defaultDressChange = {
    },
    defaultDresses = {
      isMap = true,
      keyName = "dressType",
    },
    defaultDresses_deleted = {
      isMapDel = true,
      targetFieldName = "defaultDresses",
    },
    lastUpdateTime = {
    },
    randomOpen = {
    },
    showIds = {
      isSet = true,
    },
    showIds_deleted = {
      isSetDel = true,
      targetFieldName = "showIds",
    },
    slots = {
      isMap = true,
      keyName = "id",
    },
    slots_deleted = {
      isMapDel = true,
      targetFieldName = "slots",
    },
    tmpRandomSlot = {
    },
  },
  FlashRaceCheeringData = {
    voteCacheData = {
      isMap = true,
      keyName = "k",
    },
    voteCacheData_deleted = {
      isMapDel = true,
      targetFieldName = "voteCacheData",
    },
    voteNum = {
    },
  },
  FlyingChessActivityInfo = {
    bigRewardPos = {
      isMsg = true,
    },
    boxRewardMissCount = {
    },
    curPos = {
      isMsg = true,
    },
    gridIndex = {
      isAry = true,
    },
    gridIndex_is_cleared = {
      isAryClear = true,
      targetFieldName = "gridIndex",
    },
    hasGetBigReward = {
    },
    hasGetFreeGift = {
    },
    roundNum = {
    },
    roundRewardId = {
      isSet = true,
    },
    roundRewardId_deleted = {
      isSetDel = true,
      targetFieldName = "roundRewardId",
    },
    runCount = {
    },
  },
  FlyingChessActivityPosInfo = {
    gridIndex = {
    },
    mapId = {
    },
    round = {
    },
  },
  FoodFestivalData = {
    activityId = {
    },
    allCoinNum = {
    },
    bigRewardState = {
    },
    elvesData = {
      isMap = true,
      keyName = "id",
    },
    elvesData_deleted = {
      isMapDel = true,
      targetFieldName = "elvesData",
    },
    lastChargedTime = {
    },
  },
  FpsSettings = {
    aimBtnRotate = {
    },
    aimCameraSensitivity = {
    },
    aimGyroSensitivity = {
    },
    cameraSensitivity = {
    },
    gyroSensitivity = {
    },
    isSetByPlayer = {
    },
    joyStickMode = {
    },
    openGyro = {
    },
    shootMode = {
      isAry = true,
    },
    shootMode_is_cleared = {
      isAryClear = true,
      targetFieldName = "shootMode",
    },
    sightCameraSensitivity = {
      isAry = true,
    },
    sightCameraSensitivity_is_cleared = {
      isAryClear = true,
      targetFieldName = "sightCameraSensitivity",
    },
    sightGyroSensitivity = {
      isAry = true,
    },
    sightGyroSensitivity_is_cleared = {
      isAryClear = true,
      targetFieldName = "sightGyroSensitivity",
    },
    sniperFireMode = {
    },
    viewMode = {
      isAry = true,
    },
    viewMode_is_cleared = {
      isAryClear = true,
      targetFieldName = "viewMode",
    },
  },
  FpsWeaponUnLockInfo = {
    weaponUnlockConditionId = {
    },
    weaponUnlockConditionNum = {
    },
    weaponUnlockConditionSuccessNum = {
    },
  },
  FpsWeaponUnLockUserDBInfo = {
    weaponUnLockInfo = {
      isMap = true,
      keyName = "weaponUnlockConditionId",
    },
    weaponUnLockInfo_deleted = {
      isMapDel = true,
      targetFieldName = "weaponUnLockInfo",
    },
  },
  FriendInteractAttr = {
    interactDetail = {
      isMap = true,
      keyName = "type",
    },
    interactDetail_deleted = {
      isMapDel = true,
      targetFieldName = "interactDetail",
    },
    uid = {
    },
  },
  FriendInteractDetailAttr = {
    interactCount = {
    },
    type = {
    },
  },
  FriendRecommendInfo = {
    nextAllowRecommendSec = {
    },
  },
  FrozenCardInfo = {
    cardList = {
      isMap = true,
      keyName = "id",
    },
    cardList_deleted = {
      isMapDel = true,
      targetFieldName = "cardList",
    },
    deductSourceType = {
    },
    expireTimeMs = {
    },
    tradeId = {
    },
  },
  FurnitureTimeInfo = {
    confId = {
    },
    updateTime = {
    },
  },
  GameGearSettings = {
    hideFashionScores = {
      isMap = true,
      keyName = "gameMode",
    },
    hideFashionScores_deleted = {
      isMapDel = true,
      targetFieldName = "hideFashionScores",
    },
  },
  GameModeReturnCompleteRecord = {
    completeCount = {
    },
    completeTimeMs = {
    },
    id = {
    },
  },
  GameModeReturnData = {
    completeReturnRecords = {
      isMap = true,
      keyName = "id",
    },
    completeReturnRecords_deleted = {
      isMapDel = true,
      targetFieldName = "completeReturnRecords",
    },
    runningReturnDatas = {
      isMap = true,
      keyName = "id",
    },
    runningReturnDatas_deleted = {
      isMapDel = true,
      targetFieldName = "runningReturnDatas",
    },
  },
  GameModeReturnRunningData = {
    drawInnReward = {
    },
    endTimeMs = {
    },
    id = {
    },
    progress = {
    },
    progressDraw = {
      isSet = true,
    },
    progressDraw_deleted = {
      isSetDel = true,
      targetFieldName = "progressDraw",
    },
    redDots = {
    },
    showFace = {
    },
    startTimeMs = {
    },
    state = {
    },
    taskRedDot = {
      isSet = true,
    },
    taskRedDot_deleted = {
      isSetDel = true,
      targetFieldName = "taskRedDot",
    },
    triggerTags = {
      isMap = true,
      keyName = "id",
    },
    triggerTags_deleted = {
      isMapDel = true,
      targetFieldName = "triggerTags",
    },
    triggerTimeMs = {
    },
    unlockRedDot = {
      isSet = true,
    },
    unlockRedDot_deleted = {
      isSetDel = true,
      targetFieldName = "unlockRedDot",
    },
    unlockStageIds = {
      isSet = true,
    },
    unlockStageIds_deleted = {
      isSetDel = true,
      targetFieldName = "unlockStageIds",
    },
  },
  GameModeReturnTriggerTag = {
    id = {
    },
    value = {
    },
  },
  GameplayAddItemBillNo = {
    addTimeMs = {
    },
    billNo = {
    },
  },
  GameplayEventSerialId = {
    reportTimeMs = {
    },
    serialId = {
    },
  },
  GameplayItem = {
    billNo = {
    },
    expireMs = {
    },
    itemChangeReason = {
    },
    itemChangeSubReason = {
    },
    itemId = {
    },
    itemNum = {
    },
    uuid = {
    },
  },
  GameplayItemSet = {
    featureId = {
    },
    itemList = {
      isMap = true,
      keyName = "uuid",
    },
    itemList_deleted = {
      isMapDel = true,
      targetFieldName = "itemList",
    },
  },
  GameProtectionSpecificSwitch = {
    forbiddenPublicChat = {
    },
    forbiddenStrangerChat = {
    },
    forbiddenStrangerRoomInvitation = {
    },
    forbiddenStrangerVisitXiaowo = {
    },
  },
  GameTimesStatData = {
    gameType = {
    },
    gameTypeDataItem = {
      isMap = true,
      keyName = "dataType",
    },
    gameTypeDataItem_deleted = {
      isMapDel = true,
      targetFieldName = "gameTypeDataItem",
    },
  },
  GameTimesStatistics = {
    gameTimesData = {
      isMap = true,
      keyName = "gameType",
    },
    gameTimesData_deleted = {
      isMapDel = true,
      targetFieldName = "gameTimesData",
    },
  },
  GameTvInfo = {
    hasReward = {
    },
    rewardExpireTime = {
    },
  },
  GameTypeDataItem = {
    dataStatType = {
    },
    dataType = {
    },
    statTime = {
    },
    value = {
    },
  },
  GeneralRedDotInfo = {
    moduleRedDotMap = {
      isMap = true,
      keyName = "moduleId",
    },
    moduleRedDotMap_deleted = {
      isMapDel = true,
      targetFieldName = "moduleRedDotMap",
    },
    moduleType = {
    },
  },
  GiftPackageRandomRecord = {
    giftId = {
    },
    randomGroupInfo = {
      isMap = true,
      keyName = "groupId",
    },
    randomGroupInfo_deleted = {
      isMapDel = true,
      targetFieldName = "randomGroupInfo",
    },
  },
  GiftRandomGroupInfo = {
    groupId = {
    },
    randomIndexInfo = {
      isMap = true,
      keyName = "index",
    },
    randomIndexInfo_deleted = {
      isMapDel = true,
      targetFieldName = "randomIndexInfo",
    },
  },
  GiftRandomIndexInfo = {
    index = {
    },
    randomCount = {
    },
  },
  GoodReward = {
    good = {
    },
    reward = {
    },
  },
  Grid = {
    crop = {
      isMsg = true,
    },
    expLevel = {
    },
    id = {
    },
    lastOpAppoint = {
    },
    lastOperator = {
    },
    lastOpSource = {
    },
    lastOpTimeMs = {
    },
    lastOpType = {
    },
    level = {
    },
  },
  GroupingReturnData = {
    buyCommodity = {
    },
    groupId = {
    },
    rewardStage = {
    },
  },
  GuidedDiscoverInfo = {
    alreadyHttp = {
    },
    clickedFound = {
    },
    id = {
      isSet = true,
    },
    id_deleted = {
      isSetDel = true,
      targetFieldName = "id",
    },
  },
  GuideInfo = {
    id = {
    },
    times = {
    },
    timestamp = {
    },
  },
  GuideStatistics = {
    newInfo = {
      isMap = true,
      keyName = "id",
    },
    newInfo_deleted = {
      isMapDel = true,
      targetFieldName = "newInfo",
    },
  },
  GuideTaskExtraInfo = {
    seqId = {
    },
  },
  HalfYearNavigationActivity = {
    checkedDays = {
      isMap = true,
      keyName = "day",
    },
    checkedDays_deleted = {
      isMapDel = true,
      targetFieldName = "checkedDays",
    },
  },
  HalfYearNavigationDayInfo = {
    checkedIds = {
      isSet = true,
    },
    checkedIds_deleted = {
      isSetDel = true,
      targetFieldName = "checkedIds",
    },
    day = {
    },
    isRewarded = {
    },
  },
  HalfYearWarmUpActivity = {
    checkedDays = {
      isMap = true,
      keyName = "day",
    },
    checkedDays_deleted = {
      isMapDel = true,
      targetFieldName = "checkedDays",
    },
    makeUpDay = {
    },
    makeUpTimes = {
    },
    rewardedList = {
      isSet = true,
    },
    rewardedList_deleted = {
      isSetDel = true,
      targetFieldName = "rewardedList",
    },
  },
  HalfYearWarmUpDayInfo = {
    checkInTimeStamp = {
    },
    day = {
    },
    isMakeUp = {
    },
    isRewarded = {
    },
  },
  HarvestItem = {
    itemId = {
    },
    itemNum = {
    },
  },
  HeatDetail = {
    cnt = {
    },
    conditionGroup = {
      isMsg = true,
    },
    id = {
    },
  },
  HideFashionScoreSetting = {
    gameMode = {
    },
    hide = {
    },
  },
  HistoryFestivalGift = {
    giftId = {
    },
    trigTime = {
    },
  },
  HistoryItemTypeInfo = {
    num = {
    },
    tempNum = {
    },
    type = {
    },
  },
  HistoryItemUsedInfo = {
    itemId = {
    },
    num = {
    },
    tempNum = {
    },
  },
  HOKABTestInfo = {
    reserve1 = {
    },
    reserve2 = {
    },
    reserve3 = {
    },
    reserve4 = {
    },
    testFlag = {
    },
    updateTime = {
    },
    userType = {
    },
  },
  HOKAIInviteDailyData = {
    inviteCount = {
    },
    reserve1 = {
    },
    reserve2 = {
    },
    reserve3 = {
    },
    reserve4 = {
    },
    zeroDayTime = {
    },
  },
  HOKAIInviteInfo = {
    inviteInfo = {
      isMsg = true,
    },
    reserve1 = {
    },
    reserve2 = {
    },
    reserve3 = {
    },
    reserve4 = {
    },
    showInfo = {
      isMsg = true,
    },
  },
  HOKAIInviteShowInviteData = {
    reserve1 = {
    },
    reserve2 = {
    },
    reserve3 = {
    },
    reserve4 = {
    },
    showCount = {
    },
    showFlag = {
    },
    showFlagTime = {
    },
    zeroDayTime = {
    },
  },
  HOKAttrInfo = {
    abTestInfo = {
      isMsg = true,
    },
    aiInviteInfo = {
      isMsg = true,
    },
    mobaTagInfo = {
      isMsg = true,
    },
    xiaoBaiAbTestInfo = {
      isMsg = true,
    },
  },
  HOKExpandSetting = {
    settingKey = {
    },
    settingValue = {
    },
  },
  HOKMobaTagInfo = {
    ext1 = {
    },
    ext2 = {
    },
    reserve1 = {
    },
    reserve2 = {
    },
    reserve3 = {
    },
    reserve4 = {
    },
    updateTime = {
    },
  },
  HOKSettings = {
    autoAddSkill = {
    },
    autoBuyCard = {
    },
    cameraControlType = {
    },
    cameraSensitivity = {
    },
    isAutoNormalAttack = {
    },
    isDefenceTowerRemind = {
    },
    mapPosition = {
    },
    maxAngle = {
    },
    minAngle = {
    },
    settingMap = {
      isMap = true,
      keyName = "settingKey",
    },
    settingMap_deleted = {
      isMapDel = true,
      targetFieldName = "settingMap",
    },
    shopPosition = {
    },
    showActivityTitle = {
    },
    showEnemyHeadIconOutOfScreen = {
    },
    targetSelectionStrategy = {
    },
  },
  HOKXiaoBaiWarmRoundABTestInfo = {
    freeCount = {
    },
    rankCount = {
    },
    reserve1 = {
    },
    reserve2 = {
    },
    reserve3 = {
    },
    reserve4 = {
    },
    roundCount = {
    },
    roundType = {
    },
    score = {
    },
    updateTime = {
    },
  },
  HomePageActionInfo = {
    actionId = {
    },
    expireTime = {
    },
  },
  HomePageActionShowInfo = {
    dressItemInfo = {
      isMap = true,
      keyName = "dressUpType",
    },
    dressItemInfo_deleted = {
      isMapDel = true,
      targetFieldName = "dressItemInfo",
    },
    interactionInfo = {
      isMap = true,
      keyName = "index",
    },
    interactionInfo_deleted = {
      isMapDel = true,
      targetFieldName = "interactionInfo",
    },
    interActionList = {
      isMap = true,
      keyName = "actionId",
    },
    interActionList_deleted = {
      isMapDel = true,
      targetFieldName = "interActionList",
    },
    interactionUid = {
    },
    selfActionList = {
      isMap = true,
      keyName = "actionId",
    },
    selfActionList_deleted = {
      isMapDel = true,
      targetFieldName = "selfActionList",
    },
    showAnimationButton = {
    },
    showOptions = {
    },
    showOrangeGearButton = {
    },
    vehicle = {
    },
    vehicleAccessories = {
      isMsg = true,
    },
  },
  HotTopicInfo = {
    id = {
    },
    thumbUpMS = {
    },
  },
  HourChargeInfo = {
    hourTime = {
    },
    moneyNum = {
    },
  },
  HouseAttr = {
    currentRoomId = {
    },
    farmVillager = {
      isMsg = true,
    },
    houseBasicInfo = {
      isMsg = true,
    },
    houseClientCloud = {
      isMap = true,
      keyName = "K",
    },
    houseClientCloud_deleted = {
      isMapDel = true,
      targetFieldName = "houseClientCloud",
    },
    houseDsInfo = {
      isMsg = true,
    },
    houseId = {
    },
    houseInteractInfo = {
      isMsg = true,
    },
    houseOwnerInfo = {
      isMsg = true,
    },
    houseSafeInfo = {
      isMsg = true,
    },
    houseVisitorInfo = {
      isMsg = true,
    },
    itemInfo = {
      isMap = true,
      keyName = "id",
    },
    itemInfo_deleted = {
      isMapDel = true,
      targetFieldName = "itemInfo",
    },
    layoutInfo = {
      isMap = true,
      keyName = "layoutID",
    },
    layoutInfo_deleted = {
      isMapDel = true,
      targetFieldName = "layoutInfo",
    },
    redPacketInfo = {
      isMsg = true,
    },
    roomInfo = {
      isMap = true,
      keyName = "id",
    },
    roomInfo_deleted = {
      isMapDel = true,
      targetFieldName = "roomInfo",
    },
    specialFurniture = {
      isMsg = true,
    },
  },
  HouseBasicInfo = {
    buildingId = {
    },
    createTime = {
    },
    instruction = {
    },
    lastRefreshTimeMs = {
    },
    leaveTimeMs = {
    },
    name = {
    },
    openId = {
    },
    version = {
    },
    visitorListTag = {
    },
  },
  HouseClientCache = {
    K = {
    },
    V = {
    },
  },
  HouseInfo = {
    myHouseInfo = {
      isMsg = true,
    },
    visitHouseInfo = {
      isMsg = true,
    },
  },
  HouseInteractInfo = {
    houseItemInteracts = {
      isMap = true,
      keyName = "itemUid",
    },
    houseItemInteracts_deleted = {
      isMapDel = true,
      targetFieldName = "houseItemInteracts",
    },
  },
  HouseItemInfo = {
    confId = {
    },
    number = {
    },
  },
  HouseItemInteract = {
    detail = {
    },
    itemUid = {
    },
  },
  HouseItemMessage = {
    confId = {
    },
    direction = {
    },
    furnitureType = {
    },
    id = {
    },
    opType = {
    },
    parentInstanceId = {
    },
    skinConfId = {
    },
    subState = {
    },
    surface = {
    },
    x = {
    },
    y = {
    },
    z = {
    },
  },
  HouseItemStatistics = {
    cookItemInfo = {
      isMap = true,
      keyName = "confId",
    },
    cookItemInfo_deleted = {
      isMapDel = true,
      targetFieldName = "cookItemInfo",
    },
    itemInfo = {
      isMap = true,
      keyName = "confId",
    },
    itemInfo_deleted = {
      isMapDel = true,
      targetFieldName = "itemInfo",
    },
  },
  HouseLayoutInfo = {
    buildingId = {
    },
    clientVersion = {
    },
    itemInfo = {
      isMap = true,
      keyName = "id",
    },
    itemInfo_deleted = {
      isMapDel = true,
      targetFieldName = "itemInfo",
    },
    layoutDesc = {
    },
    layoutID = {
    },
    layoutName = {
    },
    layoutPic = {
    },
    lightInfo = {
    },
    roomInfo = {
      isMsg = true,
    },
    updateTime = {
    },
  },
  HouseOwnerInfo = {
    isOnline = {
    },
  },
  HousePublicInfo = {
    houseSafeInfo = {
      isMsg = true,
    },
    houseVisitorCount = {
    },
    roomCount = {
    },
  },
  HousePunish = {
    enable = {
    },
    endTime = {
    },
    reason = {
    },
  },
  HouseSafeInfo = {
    putDown = {
      isMsg = true,
    },
  },
  HSBuffHistory = {
    farmID = {
    },
    timeMs = {
      isAry = true,
    },
    timeMs_is_cleared = {
      isAryClear = true,
      targetFieldName = "timeMs",
    },
  },
  IdcNetworkInfoDb = {
    millTs = {
    },
    records = {
      isMap = true,
      keyName = "id",
    },
    records_deleted = {
      isMapDel = true,
      targetFieldName = "records",
    },
  },
  IdcNetworkRecordDb = {
    id = {
    },
    rttMs = {
    },
  },
  IdentityQualifyingSeasonInfo = {
    curSeasonId = {
    },
    qualifyType = {
    },
    updateTime = {
    },
  },
  IdipStatistics = {
    num = {
    },
    timestamp = {
    },
  },
  IdipTask = {
    completeCount = {
    },
    id = {
    },
  },
  IdipTaskInfo = {
    battle = {
      isMap = true,
      keyName = "timestamp",
    },
    battle_deleted = {
      isMapDel = true,
      targetFieldName = "battle",
    },
    charge = {
      isMap = true,
      keyName = "timestamp",
    },
    charge_deleted = {
      isMapDel = true,
      targetFieldName = "charge",
    },
    curTime = {
    },
    login = {
      isMap = true,
      keyName = "timestamp",
    },
    login_deleted = {
      isMapDel = true,
      targetFieldName = "login",
    },
    task = {
      isMap = true,
      keyName = "id",
    },
    task_deleted = {
      isMapDel = true,
      targetFieldName = "task",
    },
  },
  InflateRedPacketMemberData = {
    inflateNum = {
    },
    isUnlock = {
    },
    money = {
    },
    uid = {
    },
  },
  InflateRedPacketMoney = {
    chargeEpochSecs = {
    },
    inflatedTimes = {
    },
    inflateNum = {
    },
    isFirstTimeInflated = {
    },
    money = {
    },
    receiveEpochSecs = {
    },
  },
  InheritMsg = {
    srcStarPRoleId = {
    },
    srcStarPWorldId = {
    },
  },
  Int32Map = {
    kvs = {
      isMap = true,
      keyName = "k",
    },
    kvs_deleted = {
      isMapDel = true,
      targetFieldName = "kvs",
    },
  },
  Int32Set = {
    element = {
      isSet = true,
    },
    element_deleted = {
      isSetDel = true,
      targetFieldName = "element",
    },
  },
  IntellectualActivity = {
    activities = {
      isSet = true,
    },
    activities_deleted = {
      isSetDel = true,
      targetFieldName = "activities",
    },
  },
  IntelligenceStationData = {
    alreadyReward = {
      isSet = true,
    },
    alreadyReward_deleted = {
      isSetDel = true,
      targetFieldName = "alreadyReward",
    },
    dailyValue = {
    },
    totalValue = {
    },
  },
  InterActAddFavor = {
    actId = {
    },
    addFavor = {
    },
  },
  Interaction = {
    AccessoriesId = {
      isMsg = true,
    },
    combination = {
      isMsg = true,
    },
    combinationId = {
    },
    itemUuid = {
    },
    pos = {
    },
    type = {
    },
  },
  InteractionInfo = {
    index = {
    },
    isShowFriend = {
    },
    uid = {
    },
  },
  InterServerGiftActivity = {
    buyNum = {
    },
    finalRewardedItem = {
      isSet = true,
    },
    finalRewardedItem_deleted = {
      isSetDel = true,
      targetFieldName = "finalRewardedItem",
    },
    progressRewardedItem = {
      isSet = true,
    },
    progressRewardedItem_deleted = {
      isSetDel = true,
      targetFieldName = "progressRewardedItem",
    },
    unlockPieces = {
      isSet = true,
    },
    unlockPieces_deleted = {
      isSetDel = true,
      targetFieldName = "unlockPieces",
    },
  },
  IntimatePlayerInfo = {
    addTime = {
    },
    hasNewIntimate = {
    },
    intimacy = {
    },
    intimateId = {
    },
    level = {
    },
    uid = {
    },
  },
  IntimateRelationGuideAttr = {
    guideDetail = {
      isMap = true,
      keyName = "uid",
    },
    guideDetail_deleted = {
      isMapDel = true,
      targetFieldName = "guideDetail",
    },
  },
  IntimateRelationGuideDetail = {
    nextTriggerTimeMs = {
    },
    uid = {
    },
  },
  IntimateRelationInfo = {
    hide = {
    },
    intimatePlayer = {
      isMap = true,
      keyName = "uid",
    },
    intimatePlayer_deleted = {
      isMapDel = true,
      targetFieldName = "intimatePlayer",
    },
  },
  IntimateRelationMotionAttr = {
    motionId = {
      isSet = true,
    },
    motionId_deleted = {
      isSetDel = true,
      targetFieldName = "motionId",
    },
    uid = {
    },
  },
  IntimateRelationOnlineNoticeAttr = {
    lastNoticeTime = {
    },
    noticeDetail = {
      isMap = true,
      keyName = "uid",
    },
    noticeDetail_deleted = {
      isMapDel = true,
      targetFieldName = "noticeDetail",
    },
    noticeDressItem = {
      isMap = true,
      keyName = "itemType",
    },
    noticeDressItem_deleted = {
      isMapDel = true,
      targetFieldName = "noticeDressItem",
    },
  },
  IntimateRelationOnlineNoticeDetailAttr = {
    lastNoticeTime = {
    },
    rejectNoticeTime = {
    },
    uid = {
    },
  },
  IntimateRelationOnlineNoticeDressItemAttr = {
    allDressItem = {
    },
    itemType = {
    },
    singleDressItem = {
      isMap = true,
      keyName = "uid",
    },
    singleDressItem_deleted = {
      isMapDel = true,
      targetFieldName = "singleDressItem",
    },
  },
  IntimateRelationOnlineNoticeSingleDressItem = {
    itemId = {
    },
    uid = {
    },
  },
  InviteeInfo = {
    activityId = {
    },
    activityType = {
    },
    deviceId = {
    },
    inviteeUid = {
    },
    isRecall = {
    },
    registerTimeMs = {
    },
  },
  ItaBagInfo = {
    badgeInfo = {
      isMap = true,
      keyName = "k",
    },
    badgeInfo_deleted = {
      isMapDel = true,
      targetFieldName = "badgeInfo",
    },
    cosUrl = {
    },
    itemUUID = {
    },
  },
  Item = {
    bagTag = {
    },
    cooling = {
    },
    equipPos = {
    },
    expireMs = {
    },
    expireType = {
    },
    favoriteTime = {
    },
    getTimeMs = {
    },
    gridId = {
    },
    id = {
    },
    itemId = {
    },
    lessorUid = {
    },
    number = {
    },
    status = {
    },
    updateMs = {
    },
  },
  ItemChangeRecord = {
    changeReason = {
    },
    changeSubReason = {
    },
    changeTimeMs = {
    },
    id = {
    },
    itemCount = {
    },
    itemId = {
    },
  },
  ItemDetailInfo = {
    itemId = {
    },
    showStatus = {
    },
  },
  ItemInfoDb = {
    gameplayAddItemBillNo = {
      isMap = true,
      keyName = "billNo",
    },
    gameplayAddItemBillNo_deleted = {
      isMapDel = true,
      targetFieldName = "gameplayAddItemBillNo",
    },
    gameplayTempItem = {
      isMap = true,
      keyName = "featureId",
    },
    gameplayTempItem_deleted = {
      isMapDel = true,
      targetFieldName = "gameplayTempItem",
    },
    historyItemType = {
      isMap = true,
      keyName = "type",
    },
    historyItemType_deleted = {
      isMapDel = true,
      targetFieldName = "historyItemType",
    },
    historyItemUsed = {
      isMap = true,
      keyName = "itemId",
    },
    historyItemUsed_deleted = {
      isMapDel = true,
      targetFieldName = "historyItemUsed",
    },
    item = {
      isMap = true,
      keyName = "id",
    },
    item_deleted = {
      isMapDel = true,
      targetFieldName = "item",
    },
    itemDetail = {
      isMap = true,
      keyName = "itemId",
    },
    itemDetail_deleted = {
      isMapDel = true,
      targetFieldName = "itemDetail",
    },
    observingItem = {
      isMap = true,
      keyName = "id",
    },
    observingItem_deleted = {
      isMapDel = true,
      targetFieldName = "observingItem",
    },
  },
  ItemInteract = {
    detail = {
    },
    itemUid = {
    },
    state = {
    },
  },
  ItemLock = {
    character = {
      isMap = true,
      keyName = "seatIndex",
    },
    character_deleted = {
      isMapDel = true,
      targetFieldName = "character",
    },
    detail = {
    },
    itemUid = {
    },
  },
  ItemPackageLimit = {
    guaranteeLimitInfo = {
      isMap = true,
      keyName = "limitId",
    },
    guaranteeLimitInfo_deleted = {
      isMapDel = true,
      targetFieldName = "guaranteeLimitInfo",
    },
    itemId = {
    },
    limitInfo = {
      isMap = true,
      keyName = "limitId",
    },
    limitInfo_deleted = {
      isMapDel = true,
      targetFieldName = "limitInfo",
    },
    totalDrawTimes = {
    },
  },
  KeyValStr = {
    k = {
    },
    v = {
    },
  },
  KungFuPandaData = {
    feedingCount = {
    },
    helpDataList = {
      isMap = true,
      keyName = "id",
    },
    helpDataList_deleted = {
      isMapDel = true,
      targetFieldName = "helpDataList",
    },
    lastResetTimeMs = {
    },
    latestInitRacingCostTimeMs = {
    },
    latestRacingCostTimeMs = {
    },
    racingRankRewardRecord = {
      isMap = true,
      keyName = "rankDay",
    },
    racingRankRewardRecord_deleted = {
      isMapDel = true,
      targetFieldName = "racingRankRewardRecord",
    },
    totalFeedNoodleCount = {
    },
  },
  KungFuPandaHelpData = {
    friendUid = {
    },
    helpTimeMs = {
    },
    id = {
    },
    updateTimeMs = {
    },
  },
  KvFI = {
    k = {
    },
    value = {
    },
  },
  KvIF = {
    k = {
    },
    value = {
    },
  },
  KvII = {
    k = {
    },
    value = {
    },
  },
  KvIL = {
    k = {
    },
    value = {
    },
  },
  KvLL = {
    k = {
    },
    value = {
    },
  },
  KvLStr = {
    k = {
    },
    value = {
    },
  },
  KvSS = {
    k = {
    },
    value = {
    },
  },
  LabelInfo = {
    conditionGroup = {
      isMsg = true,
    },
    id = {
    },
    status = {
    },
  },
  LastDressOutLookInfo = {
    baseDressOutLook = {
    },
    lastDressOutLook = {
    },
  },
  LayoutIDInfo = {
    layoutID = {
    },
    layoutName = {
    },
    status = {
    },
    updateTime = {
    },
  },
  LayoutInfo = {
    layoutDesc = {
    },
    layoutID = {
    },
    layoutName = {
    },
    layoutPic = {
    },
    updateTime = {
    },
  },
  LetsGoSpecPlayerPublicInfo = {
    continuousWin = {
    },
    countdownFinishTimes = {
    },
    crashTotalTimes = {
    },
    crownPlayTimes = {
    },
    teamPlayTimes = {
    },
    winChampAtTheEndTimes = {
    },
    winChampTimes = {
    },
    winTimes = {
    },
    winTop3Times = {
    },
  },
  LevelAchievementInfo = {
    achievementGroupId = {
    },
    bestScore = {
    },
    dataByPlay = {
      isMap = true,
      keyName = "playId",
    },
    dataByPlay_deleted = {
      isMapDel = true,
      targetFieldName = "dataByPlay",
    },
    id = {
    },
    type = {
    },
  },
  LevelIllustration = {
    championInfo = {
      isMap = true,
      keyName = "type",
    },
    championInfo_deleted = {
      isMapDel = true,
      targetFieldName = "championInfo",
    },
    levelAchievementInfo = {
      isMap = true,
      keyName = "id",
    },
    levelAchievementInfo_deleted = {
      isMapDel = true,
      targetFieldName = "levelAchievementInfo",
    },
  },
  LevelInfo = {
    championNum = {
    },
    collections = {
      isMap = true,
      keyName = "id",
    },
    collections_deleted = {
      isMapDel = true,
      targetFieldName = "collections",
    },
    maxLevelScore = {
      isMap = true,
      keyName = "levelId",
    },
    maxLevelScore_deleted = {
      isMapDel = true,
      targetFieldName = "maxLevelScore",
    },
    maxTotalScore = {
    },
  },
  LevelRecord = {
    records = {
      isMap = true,
      keyName = "levelId",
    },
    records_deleted = {
      isMapDel = true,
      targetFieldName = "records",
    },
  },
  LevelRecordByPlay = {
    data = {
      isMsg = true,
    },
    playId = {
    },
  },
  LevelRecordData = {
    score = {
    },
    timeCost = {
    },
  },
  LevelRecordDetailItem = {
    data = {
      isMsg = true,
    },
    itemId = {
    },
    play = {
    },
    season = {
    },
    timestamp = {
    },
  },
  LevelRecordDetails = {
    detailItems = {
      isMap = true,
      keyName = "itemId",
    },
    detailItems_deleted = {
      isMapDel = true,
      targetFieldName = "detailItems",
    },
    levelId = {
    },
  },
  LevelScore = {
    levelId = {
    },
    score = {
    },
  },
  LimitInfoStruct = {
    expireTimeMs = {
    },
    limitId = {
    },
    maxValue = {
    },
    value = {
    },
  },
  LimitTimeExperienceItem = {
    conditionGroupMap = {
      isMsg = true,
    },
    id = {
    },
    itermList = {
      isSet = true,
    },
    itermList_deleted = {
      isSetDel = true,
      targetFieldName = "itermList",
    },
    redPoint = {
    },
  },
  LiuYanMessageRecord = {
    recordCount = {
    },
    xiaoWoId = {
    },
  },
  LobbyChangeColorInfo = {
    endTime = {
    },
    id = {
    },
    levelName = {
    },
    name = {
    },
    startTime = {
    },
  },
  LobbyEggFindKindCountInfo = {
    eggCount = {
    },
    eggKindId = {
    },
  },
  LobbyInfo = {
    changeColorInfo = {
      isMsg = true,
    },
    dsAddr = {
    },
    dsaInstanceID = {
    },
    dsAuthToken = {
    },
    gameType = {
    },
    lastInspectorUgcLobby = {
      isMsg = true,
    },
    lastLobbyId = {
    },
    lastMapId = {
    },
    lastTestUgcLobby = {
      isMsg = true,
    },
    lastUgcId = {
    },
    lobbyCreatorUid = {
    },
    lobbyId = {
    },
    mapId = {
    },
    name = {
    },
    npcInfos = {
      isMap = true,
      keyName = "npcId",
    },
    npcInfos_deleted = {
      isMapDel = true,
      targetFieldName = "npcInfos",
    },
    ugcId = {
    },
    ugcMapInfo = {
      isMsg = true,
    },
    ugcSafeStatus = {
    },
  },
  LobbyLobbyMatchHistoryInfo = {
    matchID = {
    },
    timeMS = {
    },
    uid = {
    },
  },
  LobbyMatchInfo = {
    matchHistoryList = {
      isMap = true,
      keyName = "matchID",
    },
    matchHistoryList_deleted = {
      isMapDel = true,
      targetFieldName = "matchHistoryList",
    },
    matchID = {
    },
    matchSvrID = {
    },
    matchTypeID = {
    },
    roomInfoID = {
    },
    ruleID = {
    },
    state = {
    },
    timeMillSec = {
    },
  },
  LobbyNpcInfo = {
    npcId = {
    },
    npcStageId = {
    },
    npcStageList = {
      isSet = true,
    },
    npcStageList_deleted = {
      isSetDel = true,
      targetFieldName = "npcStageList",
    },
  },
  LobbyTaskInfo = {
    eggFindKindCountMap = {
      isMap = true,
      keyName = "eggKindId",
    },
    eggFindKindCountMap_deleted = {
      isMapDel = true,
      targetFieldName = "eggFindKindCountMap",
    },
  },
  LockChar = {
    characterID = {
    },
    seatIndex = {
    },
  },
  LoginFeatureData = {
    inheritData = {
      isMsg = true,
    },
  },
  LotteryData = {
    lotteryCount = {
    },
    lotteryHistory = {
      isMap = true,
      keyName = "k",
    },
    lotteryHistory_deleted = {
      isMapDel = true,
      targetFieldName = "lotteryHistory",
    },
    lotteryResultMap = {
      isMap = true,
      keyName = "k",
    },
    lotteryResultMap_deleted = {
      isMapDel = true,
      targetFieldName = "lotteryResultMap",
    },
  },
  LotteryDrawActivity = {
    rewardInfo = {
      isMap = true,
      keyName = "rewardId",
    },
    rewardInfo_deleted = {
      isMapDel = true,
      targetFieldName = "rewardInfo",
    },
    totalDrawCount = {
    },
  },
  LotteryDrawActivityRewardInfo = {
    rewardGetCount = {
    },
    rewardId = {
    },
  },
  LotteryDrawInfo = {
    rewardInfo = {
      isMap = true,
      keyName = "rewardId",
    },
    rewardInfo_deleted = {
      isMapDel = true,
      targetFieldName = "rewardInfo",
    },
    totalDrawCount = {
    },
  },
  LuckyBalloonData = {
    blessIds = {
      isMap = true,
      keyName = "id",
    },
    blessIds_deleted = {
      isMapDel = true,
      targetFieldName = "blessIds",
    },
    drawnCount = {
    },
    rewardIds = {
      isMap = true,
      keyName = "id",
    },
    rewardIds_deleted = {
      isMapDel = true,
      targetFieldName = "rewardIds",
    },
  },
  LuckyBalloonItem = {
    drawnAtCount = {
    },
    drawnTs = {
    },
    id = {
    },
  },
  LuckyFriendApplyAttr = {
    expireTime = {
    },
    friendUid = {
    },
    taskId = {
    },
    taskUniqueId = {
    },
  },
  LuckyFriendAttr = {
    acceptCnt = {
    },
    configId = {
    },
    matchedTaskId = {
      isSet = true,
    },
    matchedTaskId_deleted = {
      isSetDel = true,
      targetFieldName = "matchedTaskId",
    },
    receiveApplyAttr = {
      isMap = true,
      keyName = "friendUid",
    },
    receiveApplyAttr_deleted = {
      isMapDel = true,
      targetFieldName = "receiveApplyAttr",
    },
    stageEndTimeMs = {
    },
    stageStartTimeMs = {
    },
    taskAttr = {
      isMsg = true,
    },
    triggerCnt = {
    },
  },
  LuckyFriendTaskAttr = {
    matchFriendUid = {
    },
    sendApplyFriendUid = {
    },
    state = {
    },
    stateEndTimeMs = {
    },
    taskId = {
    },
    taskUniqueId = {
    },
  },
  LuckyMoneyActivity = {
    friendRandomCount = {
    },
    randomCount = {
    },
    randomRecord = {
      isMap = true,
      keyName = "giftId",
    },
    randomRecord_deleted = {
      isMapDel = true,
      targetFieldName = "randomRecord",
    },
    recvList = {
      isMap = true,
      keyName = "id",
    },
    recvList_deleted = {
      isMapDel = true,
      targetFieldName = "recvList",
    },
    shareList = {
      isMap = true,
      keyName = "id",
    },
    shareList_deleted = {
      isMapDel = true,
      targetFieldName = "shareList",
    },
    shareTimeMs = {
    },
    showEndTimeMs = {
    },
  },
  LuckyMoneyInfo = {
    id = {
    },
    luckyMoneyIndex = {
    },
    receivedCount = {
    },
    rewards = {
      isMap = true,
      keyName = "id",
    },
    rewards_deleted = {
      isMapDel = true,
      targetFieldName = "rewards",
    },
    sharePlayerUid = {
    },
  },
  LuckyRebateActivityData = {
    consumptionAmount = {
    },
    drawCnt = {
    },
    intermediateResult = {
    },
    numberLimitIndex = {
    },
    rewardLevel = {
    },
    rewardSettlement = {
    },
    topDrawCnt = {
    },
  },
  LuckyStarActivity = {
    giveRequiredMap = {
      isSet = true,
    },
    giveRequiredMap_deleted = {
      isSetDel = true,
      targetFieldName = "giveRequiredMap",
    },
    isReward = {
    },
    receiveStarNum = {
    },
    roundDrawNum = {
    },
    roundNum = {
    },
    starInfo = {
      isMap = true,
      keyName = "uniqueId",
    },
    starInfo_deleted = {
      isMapDel = true,
      targetFieldName = "starInfo",
    },
  },
  LuckyStarInfo = {
    blessId = {
    },
    getTimeMs = {
    },
    giveLock = {
    },
    giverUid = {
    },
    starId = {
    },
    uniqueId = {
    },
  },
  LuckyTurntableActivityData = {
    curRoundId = {
    },
    roundInfo = {
      isMap = true,
      keyName = "id",
    },
    roundInfo_deleted = {
      isMapDel = true,
      targetFieldName = "roundInfo",
    },
  },
  LuckyTurntableRoundInfo = {
    id = {
    },
    isOpen = {
    },
    openTurntableId = {
    },
  },
  MailCache = {
    delMailIds = {
      isSet = true,
    },
    delMailIds_deleted = {
      isSetDel = true,
      targetFieldName = "delMailIds",
    },
    globalMailIdsIdip = {
      isMap = true,
      keyName = "k",
    },
    globalMailIdsIdip_deleted = {
      isMapDel = true,
      targetFieldName = "globalMailIdsIdip",
    },
    globalMailIdsXls = {
      isMap = true,
      keyName = "k",
    },
    globalMailIdsXls_deleted = {
      isMapDel = true,
      targetFieldName = "globalMailIdsXls",
    },
    gotMailIds = {
      isSet = true,
    },
    gotMailIds_deleted = {
      isSetDel = true,
      targetFieldName = "gotMailIds",
    },
    lastRecvGlobalMailMs = {
    },
    purchasedMailIds = {
      isSet = true,
    },
    purchasedMailIds_deleted = {
      isSetDel = true,
      targetFieldName = "purchasedMailIds",
    },
  },
  MainCups = {
    progress = {
    },
    progressDb = {
    },
    receivedReward = {
      isSet = true,
    },
    receivedReward_deleted = {
      isSetDel = true,
      targetFieldName = "receivedReward",
    },
    weeklyProgress = {
    },
    weeklyProgressDb = {
    },
  },
  MainCupsCycle = {
    cupsCycleNum = {
    },
    cupsCycleNumDb = {
    },
    id = {
    },
    progress = {
    },
    progressDb = {
    },
    receivedReward = {
      isSet = true,
    },
    receivedReward_deleted = {
      isSetDel = true,
      targetFieldName = "receivedReward",
    },
  },
  MainMasterInfo = {
    cycleId = {
    },
    progress = {
    },
    progressDb = {
    },
    rewardedId = {
      isSet = true,
    },
    rewardedId_deleted = {
      isSetDel = true,
      targetFieldName = "rewardedId",
    },
  },
  MainMasterPath = {
    masterEnumId = {
    },
    masterPathInfo = {
      isMap = true,
      keyName = "cycleId",
    },
    masterPathInfo_deleted = {
      isMapDel = true,
      targetFieldName = "masterPathInfo",
    },
    unlockCycleCondition = {
      isMap = true,
      keyName = "id",
    },
    unlockCycleCondition_deleted = {
      isMapDel = true,
      targetFieldName = "unlockCycleCondition",
    },
  },
  MallDemandInfo = {
    demandCount = {
    },
    giveCount = {
    },
  },
  MallGiftCard = {
    cardType = {
    },
    wordsContent = {
    },
    wordsId = {
    },
  },
  MallGiveRecord = {
    card = {
      isMsg = true,
    },
    friendUid = {
    },
    giveCommidityList = {
      isMap = true,
      keyName = "id",
    },
    giveCommidityList_deleted = {
      isMapDel = true,
      targetFieldName = "giveCommidityList",
    },
    giveId = {
    },
    giveItemList = {
      isMap = true,
      keyName = "id",
    },
    giveItemList_deleted = {
      isMapDel = true,
      targetFieldName = "giveItemList",
    },
    giveTime = {
    },
    mailId = {
    },
  },
  MallInfo = {
    commonMallInfo = {
      isMsg = true,
    },
    expiredScenePackageId = {
      isSet = true,
    },
    expiredScenePackageId_deleted = {
      isSetDel = true,
      targetFieldName = "expiredScenePackageId",
    },
    mallDeletedGiveRecord = {
      isSet = true,
    },
    mallDeletedGiveRecord_deleted = {
      isSetDel = true,
      targetFieldName = "mallDeletedGiveRecord",
    },
    mallDemandInfo = {
      isMsg = true,
    },
    mallGiveRecord = {
      isMap = true,
      keyName = "giveId",
    },
    mallGiveRecord_deleted = {
      isMapDel = true,
      targetFieldName = "mallGiveRecord",
    },
    mallRedPointInfo = {
      isMap = true,
      keyName = "mallId",
    },
    mallRedPointInfo_deleted = {
      isMapDel = true,
      targetFieldName = "mallRedPointInfo",
    },
    recScenePackageInfo = {
      isMsg = true,
    },
    recvLimitRecords = {
      isMap = true,
      keyName = "id",
    },
    recvLimitRecords_deleted = {
      isMapDel = true,
      targetFieldName = "recvLimitRecords",
    },
    scenePackageInfo = {
      isMap = true,
      keyName = "id",
    },
    scenePackageInfo_deleted = {
      isMapDel = true,
      targetFieldName = "scenePackageInfo",
    },
    themeMallData = {
      isMap = true,
      keyName = "id",
    },
    themeMallData_deleted = {
      isMapDel = true,
      targetFieldName = "themeMallData",
    },
  },
  MallRedPoint = {
    mallId = {
    },
    redPointTypeInfo = {
      isMap = true,
      keyName = "redPointType",
    },
    redPointTypeInfo_deleted = {
      isMapDel = true,
      targetFieldName = "redPointTypeInfo",
    },
  },
  MallRedPointStatus = {
    redPointType = {
    },
    status = {
    },
    updateTime = {
    },
  },
  MallWishCommodity = {
    addTimeMs = {
    },
    commodityId = {
    },
    hasOwn = {
    },
    hideToOthers = {
    },
    notSale = {
    },
  },
  MallWishListPublic = {
    commoditys = {
      isMap = true,
      keyName = "commodityId",
    },
    commoditys_deleted = {
      isMapDel = true,
      targetFieldName = "commoditys",
    },
  },
  MapStarPShop = {
    mapStarpShop = {
      isMap = true,
      keyName = "shopType",
    },
    mapStarpShop_deleted = {
      isMapDel = true,
      targetFieldName = "mapStarpShop",
    },
  },
  MarqueeNoticeInfo = {
    notices = {
      isMap = true,
      keyName = "noticeId",
    },
    notices_deleted = {
      isMapDel = true,
      targetFieldName = "notices",
    },
  },
  MarqueNoticeInfoItem = {
    hasNoticedTimes = {
    },
    hasSentCanceled = {
    },
    hasSentThisLogin = {
    },
    lastNoticeTimeSec = {
    },
    noticeId = {
    },
  },
  MasterPathData = {
    mainMasterPathMap = {
      isMap = true,
      keyName = "masterEnumId",
    },
    mainMasterPathMap_deleted = {
      isMapDel = true,
      targetFieldName = "mainMasterPathMap",
    },
  },
  MatchIsolateInfoDb = {
    ailevel = {
    },
    reason = {
    },
    time = {
    },
    type = {
    },
  },
  MatchStaticsDb = {
    idcNetworkInfo = {
      isMsg = true,
    },
    loginCountryCode = {
    },
    matchIsolateInfo = {
      isMsg = true,
    },
    mmrScoresInfo = {
      isMsg = true,
    },
    warmRoundInfo = {
      isMsg = true,
    },
  },
  MatchTypeHistory = {
    curRecommendId = {
    },
    historyPlay = {
      isSet = true,
    },
    historyPlay_deleted = {
      isSetDel = true,
      targetFieldName = "historyPlay",
    },
    newRecommend = {
    },
  },
  MatchUnlockInfo = {
    matchTypeId = {
    },
    unlockType = {
      isSet = true,
    },
    unlockType_deleted = {
      isSetDel = true,
      targetFieldName = "unlockType",
    },
  },
  MaydayUserDBInfo = {
    infInfo = {
      isMsg = true,
    },
    infMaxLevel = {
    },
    monsterPictures = {
      isAry = true,
    },
    monsterPictures_is_cleared = {
      isAryClear = true,
      targetFieldName = "monsterPictures",
    },
    WinDifficultys = {
      isAry = true,
    },
    WinDifficultys_is_cleared = {
      isAryClear = true,
      targetFieldName = "WinDifficultys",
    },
  },
  MaydayUserInfIdentityInfo = {
    identityID = {
    },
    level = {
    },
  },
  MaydayUserInfPosInfo = {
    bookCount = {
    },
    IdentityInfo = {
      isMap = true,
      keyName = "identityID",
    },
    IdentityInfo_deleted = {
      isMapDel = true,
      targetFieldName = "IdentityInfo",
    },
    pos = {
    },
  },
  MaydayUserInfRoomInfo = {
    groupMoney = {
    },
    level = {
    },
    levelSaveCount = {
    },
    posInfo = {
      isMap = true,
      keyName = "pos",
    },
    posInfo_deleted = {
      isMapDel = true,
      targetFieldName = "posInfo",
    },
    techPoint = {
    },
  },
  Menu = {
    dishes = {
      isSet = true,
    },
    dishes_deleted = {
      isSetDel = true,
      targetFieldName = "dishes",
    },
  },
  MessageSlipRedDotInfo = {
    commentReadIndex = {
    },
    commentRedDot = {
    },
    favourReadIndex = {
    },
    favourRedDot = {
    },
    id = {
    },
  },
  MidasChargeInfo = {
    num = {
    },
    productId = {
    },
  },
  MidasLockInfo = {
    productId = {
    },
    ts = {
    },
  },
  MidasPresentFailOrder = {
    addTime = {
    },
    billNo = {
    },
    busBillNo = {
    },
    failPayToken = {
    },
    presentCnt = {
    },
    presentReason = {
    },
    presentReasonValue = {
    },
    retryNum = {
    },
    subReasonData = {
      isMsg = true,
    },
  },
  MinesweeperActivity = {
    checkerboardData = {
      isMsg = true,
    },
    lotteryDrawData = {
      isMsg = true,
    },
    totalRewardCount = {
    },
  },
  MMRScoreDb = {
    id = {
    },
    score = {
    },
  },
  MMRScoresInfoDb = {
    scores = {
      isMap = true,
      keyName = "id",
    },
    scores_deleted = {
      isMapDel = true,
      targetFieldName = "scores",
    },
  },
  MobaChallengeData = {
    challengeLevelinfo = {
      isMap = true,
      keyName = "levelId",
    },
    challengeLevelinfo_deleted = {
      isMapDel = true,
      targetFieldName = "challengeLevelinfo",
    },
  },
  MobaRandomVote = {
    lastVoteTimeMs = {
    },
    todayVoteEventId = {
    },
    todayVoteNum = {
    },
    totalVoteNum = {
    },
  },
  MobaSquadDrawRedPacketDailyTaskRecord = {
    dateTime = {
    },
    mobaTaskInfo = {
      isMap = true,
      keyName = "taskId",
    },
    mobaTaskInfo_deleted = {
      isMapDel = true,
      targetFieldName = "mobaTaskInfo",
    },
  },
  MobaSquadDrawRedPacketData = {
    historyTaskRecord = {
      isMap = true,
      keyName = "dateTime",
    },
    historyTaskRecord_deleted = {
      isMapDel = true,
      targetFieldName = "historyTaskRecord",
    },
    mobaTaskInfo = {
      isMap = true,
      keyName = "taskId",
    },
    mobaTaskInfo_deleted = {
      isMapDel = true,
      targetFieldName = "mobaTaskInfo",
    },
    refreshTimeMs = {
    },
    remainingDrawCount = {
    },
    squadInfos = {
      isMap = true,
      keyName = "dateTime",
    },
    squadInfos_deleted = {
      isMapDel = true,
      targetFieldName = "squadInfos",
    },
    taskRecord = {
      isMsg = true,
    },
  },
  MobaSquadDrawRedPacketSquadInfo = {
    dateTime = {
    },
    memberIds = {
      isSet = true,
    },
    memberIds_deleted = {
      isSetDel = true,
      targetFieldName = "memberIds",
    },
  },
  MobaSquadDrawRedPacketSquadTask = {
    taskCompleteCount = {
    },
    taskId = {
    },
  },
  ModeCups = {
    id = {
    },
    progress = {
    },
    progressDb = {
    },
  },
  ModNote = {
    activityHistoryData = {
      isMsg = true,
    },
    activityHistoryPlusData = {
      isMsg = true,
    },
    currLvOnlineTime = {
    },
    qrCodePoint = {
    },
    recentGameStartCount = {
    },
    recentPlaySwitchCount = {
    },
    todayGameStartCount = {
    },
    todayPlaySwitchCount = {
    },
    totalPlaySwitchCount = {
    },
    updateTime = {
    },
    updateTimePlaySwitch = {
    },
    visited = {
    },
  },
  ModSettings = {
    actionSeting = {
    },
    bgmVolume = {
    },
    hideEmoji = {
    },
    hideNick = {
    },
    modQuality = {
    },
    noteRatio = {
    },
    simplifySpecialEffects = {
    },
    soundEffectsVolume = {
    },
  },
  ModuleRedDotInfo = {
    moduleId = {
    },
    redDotMap = {
      isMap = true,
      keyName = "redDotType",
    },
    redDotMap_deleted = {
      isMapDel = true,
      targetFieldName = "redDotMap",
    },
  },
  Money = {
    buyLock = {
      isMap = true,
      keyName = "productId",
    },
    buyLock_deleted = {
      isMapDel = true,
      targetFieldName = "buyLock",
    },
    buyOrderInfo = {
      isMsg = true,
    },
    changePlatMidasDiff = {
      isMsg = true,
    },
    coin = {
      isMap = true,
      keyName = "coinType",
    },
    coin_deleted = {
      isMapDel = true,
      targetFieldName = "coin",
    },
    firstSaveFlag = {
    },
    hourChargeInfo = {
      isMap = true,
      keyName = "hourTime",
    },
    hourChargeInfo_deleted = {
      isMapDel = true,
      targetFieldName = "hourChargeInfo",
    },
    midasChargeInfo = {
      isMap = true,
      keyName = "productId",
    },
    midasChargeInfo_deleted = {
      isMapDel = true,
      targetFieldName = "midasChargeInfo",
    },
    platSaveAmt = {
    },
    presentBalance = {
    },
    presentFailOrder = {
      isMap = true,
      keyName = "billNo",
    },
    presentFailOrder_deleted = {
      isMapDel = true,
      targetFieldName = "presentFailOrder",
    },
    saveAmt = {
    },
    saveRmb = {
    },
    sumBalance = {
    },
    sumCost = {
    },
    sumPresent = {
    },
  },
  MoneyTree = {
    confID = {
    },
    objID = {
    },
    shakeData = {
      isMsg = true,
    },
    waterData = {
      isMsg = true,
    },
    waterHistory = {
      isMap = true,
      keyName = "timestamp",
    },
    waterHistory_deleted = {
      isMapDel = true,
      targetFieldName = "waterHistory",
    },
  },
  MoneyTreeDropItems = {
    confID = {
    },
    counts = {
    },
    index = {
    },
    type = {
    },
  },
  MoneyTreeHistory = {
    dropItems = {
      isMap = true,
      keyName = "index",
    },
    dropItems_deleted = {
      isMapDel = true,
      targetFieldName = "dropItems",
    },
    operator = {
    },
    timestamp = {
    },
  },
  MonopolyActivityData = {
    cumulativeInfo = {
      isMap = true,
      keyName = "gridId",
    },
    cumulativeInfo_deleted = {
      isMapDel = true,
      targetFieldName = "cumulativeInfo",
    },
    curIndex = {
    },
    drawInfo = {
      isMap = true,
      keyName = "poolId",
    },
    drawInfo_deleted = {
      isMapDel = true,
      targetFieldName = "drawInfo",
    },
    finishedRoundNum = {
    },
    finishedStepNum = {
    },
    guaranteeDrawCount = {
    },
    hasReceiveReward = {
    },
    receivedRoundReward = {
      isSet = true,
    },
    receivedRoundReward_deleted = {
      isSetDel = true,
      targetFieldName = "receivedRoundReward",
    },
    receivedStepReward = {
      isSet = true,
    },
    receivedStepReward_deleted = {
      isSetDel = true,
      targetFieldName = "receivedStepReward",
    },
  },
  MonopolyCumulativeData = {
    cumulativeCount = {
    },
    gridId = {
    },
  },
  MonopolyDrawData = {
    missCount = {
    },
    poolId = {
    },
  },
  MonthCardInfo = {
    absentDays = {
    },
    beginTimeMs = {
    },
    cumDays = {
    },
    expirationTime = {
    },
    expiredRedDot = {
    },
    expiredRedDotTimeMs = {
    },
    expiredRemindMallTimeMs = {
    },
    id = {
    },
    lastGetDailyItem = {
    },
    lastRecordRewardMs = {
    },
  },
  MultiPlayerSquadInfo = {
    accumulativeItems = {
      isMap = true,
      keyName = "itemId",
    },
    accumulativeItems_deleted = {
      isMapDel = true,
      targetFieldName = "accumulativeItems",
    },
    activityId = {
    },
    activityNo = {
    },
    canDig = {
    },
    items = {
      isMap = true,
      keyName = "index",
    },
    items_deleted = {
      isMapDel = true,
      targetFieldName = "items",
    },
    lastOperationTimeMs = {
    },
    members = {
      isSet = true,
    },
    members_deleted = {
      isSetDel = true,
      targetFieldName = "members",
    },
    squadId = {
    },
  },
  MusicOrderActivityAttr = {
    claimedTaskRewards = {
      isSet = true,
    },
    claimedTaskRewards_deleted = {
      isSetDel = true,
      targetFieldName = "claimedTaskRewards",
    },
    dailyInfo = {
      isMsg = true,
    },
    dayHistory = {
      isAry = true,
    },
    dayHistory_is_cleared = {
      isAryClear = true,
      targetFieldName = "dayHistory",
    },
    modeNoDropInfo = {
      isMap = true,
      keyName = "modeId",
    },
    modeNoDropInfo_deleted = {
      isMapDel = true,
      targetFieldName = "modeNoDropInfo",
    },
    orderCompleteNum = {
    },
    resetCountInfo = {
      isMap = true,
      keyName = "day",
    },
    resetCountInfo_deleted = {
      isMapDel = true,
      targetFieldName = "resetCountInfo",
    },
    WolfFinishOrderNum = {
    },
  },
  MusicOrderDailyInfo = {
    day = {
    },
    notes = {
      isMap = true,
      keyName = "itemId",
    },
    notes_deleted = {
      isMapDel = true,
      targetFieldName = "notes",
    },
    orders = {
      isMap = true,
      keyName = "orderId",
    },
    orders_deleted = {
      isMapDel = true,
      targetFieldName = "orders",
    },
    refreshTime = {
    },
    resetCount = {
    },
  },
  MusicOrderModeNoDropInfo = {
    modeId = {
    },
    noDropCount = {
    },
  },
  MusicOrderNoteInfo = {
    dropNum = {
    },
    isLimited = {
    },
    itemId = {
    },
  },
  MusicOrderOrderInfo = {
    orderId = {
    },
    state = {
    },
  },
  MusicOrderResetCountInfo = {
    day = {
    },
    expireTime = {
    },
    remainCount = {
    },
  },
  MyCookInfo = {
    createTime = {
    },
    hasCook = {
    },
  },
  MyFarmInfo = {
    createTime = {
    },
    farmID = {
    },
    hasFarm = {
    },
    initStage = {
    },
  },
  MyHouseInfo = {
    createTime = {
    },
    hasHouse = {
    },
  },
  MyRichBoardInfo = {
    createTime = {
    },
    hasBoard = {
    },
  },
  MyXiaoWoInfo = {
    bucket = {
    },
    createTime = {
    },
    editID = {
    },
    hasXiaoWo = {
    },
    instruction = {
    },
    lastGetWaterExpTime = {
    },
    lastWater = {
    },
    lastWaterDropTime = {
    },
    pubID = {
    },
    region = {
    },
    saveTime = {
    },
    templateId = {
    },
    ugcMapMetaInfo = {
      isMap = true,
      keyName = "index",
    },
    ugcMapMetaInfo_deleted = {
      isMapDel = true,
      targetFieldName = "ugcMapMetaInfo",
    },
    waterDrop = {
    },
    waterExpDay = {
    },
  },
  NewbieNPCFarmInfo = {
    firstEnterTime = {
    },
    step = {
    },
  },
  NewbieTaskInfo = {
    unlockDay = {
    },
  },
  NewCardCollectionRedDot = {
    sendRecord = {
      isSet = true,
    },
    sendRecord_deleted = {
      isSetDel = true,
      targetFieldName = "sendRecord",
    },
  },
  NewYearPilotInfo = {
    enabled = {
    },
  },
  NewYearSign = {
    commonArrayList = {
      isMsg = true,
    },
    reSignTimes = {
    },
    reSignUseTimes = {
    },
  },
  NicknameCheckNote = {
    checkReason = {
    },
    checkTimes = {
    },
    nextAvailableMs = {
    },
  },
  NpcFarmBasicInfo = {
    bePinned = {
    },
    canStealTime = {
    },
    ignoreStealTime = {
    },
    level = {
    },
    messageID = {
    },
    npcStage = {
    },
    remarkName = {
    },
  },
  NpcFarmInfo = {
    npcFarmBasicInfo = {
      isMsg = true,
    },
    npcFarmSchedule = {
      isMsg = true,
    },
  },
  NpcFarmSchedule = {
    fishRefresh = {
    },
    lastAnimalReadySec = {
    },
    lastCropReadySec = {
    },
    lastFishReadySec = {
    },
    lastMessageUpdate = {
    },
    lastRefreshSec = {
    },
    lastVisitSec = {
    },
    nextAnimalBeginSec = {
    },
    nextAnimalReadySec = {
    },
    nextCropBeginSec = {
    },
    nextCropReadySec = {
    },
    nextFertilizeSec = {
    },
    nextFishBeginSec = {
    },
    nextFishReadySec = {
    },
    stealCDSec = {
    },
  },
  NR3E8DBTaskInfo = {
    activityDegree = {
    },
    completed = {
    },
    completeTime = {
    },
    createTime = {
    },
    id = {
    },
    itemId = {
      isAry = true,
    },
    itemId_is_cleared = {
      isAryClear = true,
      targetFieldName = "itemId",
    },
    itemNum = {
      isAry = true,
    },
    itemNum_is_cleared = {
      isAryClear = true,
      targetFieldName = "itemNum",
    },
    poolId = {
    },
    rewarded = {
    },
    value = {
    },
    valueInternal = {
    },
    valueList = {
      isAry = true,
    },
    valueList_is_cleared = {
      isAryClear = true,
      targetFieldName = "valueList",
    },
    valueTotal = {
    },
  },
  NR3E8RichInfo = {
    cityLevel = {
    },
    enterRichTime = {
    },
    myRichBoardInfo = {
      isMsg = true,
    },
    richItem = {
      isMap = true,
      keyName = "k",
    },
    richItem_deleted = {
      isMapDel = true,
      targetFieldName = "richItem",
    },
    taskInfo = {
      isMsg = true,
    },
    visitRichBoardInfo = {
      isMsg = true,
    },
  },
  NR3E8TaskInfo = {
    generateTime = {
    },
    isNew = {
    },
    tasks = {
      isMap = true,
      keyName = "id",
    },
    tasks_deleted = {
      isMapDel = true,
      targetFieldName = "tasks",
    },
    weekActivity = {
    },
    weekActivityInfo = {
      isMap = true,
      keyName = "id",
    },
    weekActivityInfo_deleted = {
      isMapDel = true,
      targetFieldName = "weekActivityInfo",
    },
  },
  NR3E8WeekActivityInfo = {
    activityDegree = {
    },
    id = {
    },
    itemId = {
      isAry = true,
    },
    itemId_is_cleared = {
      isAryClear = true,
      targetFieldName = "itemId",
    },
    itemNum = {
      isAry = true,
    },
    itemNum_is_cleared = {
      isAryClear = true,
      targetFieldName = "itemNum",
    },
    rewarded = {
    },
  },
  OneDollarRaffleData = {
    awardCount = {
    },
    awardList = {
      isMap = true,
      keyName = "id",
    },
    awardList_deleted = {
      isMapDel = true,
      targetFieldName = "awardList",
    },
    isUpgrade = {
    },
    lastAwardTimeMs = {
    },
  },
  OptionalRewardInfo = {
    rewardIndex = {
    },
    taskId = {
    },
  },
  OutfitHistoryData = {
    collectState = {
    },
    index = {
    },
    outfitEquipId = {
    },
    sortId = {
    },
  },
  P2PChatGroup = {
    id = {
    },
    lastSeq = {
    },
    latestBattleTs = {
    },
    recvCount = {
    },
    sentCount = {
    },
    startSeq = {
    },
    status = {
    },
    updateTs = {
    },
  },
  P2PChatInfo = {
    beGreetedDailyCount = {
    },
    groups = {
      isMap = true,
      keyName = "id",
    },
    groups_deleted = {
      isMapDel = true,
      targetFieldName = "groups",
    },
    sayHiDailyCount = {
    },
  },
  PaidUnlockActivityData = {
    midasGot = {
      isAry = true,
    },
    midasGot_is_cleared = {
      isAryClear = true,
      targetFieldName = "midasGot",
    },
  },
  PakDownloadInfo = {
    downloaded = {
    },
    pakGroupId = {
    },
  },
  PartyInfo = {
    content = {
    },
    expireTime = {
    },
    hotSafeRatioLastUpdated = {
    },
    hotValueLastUpdated = {
    },
    image = {
      isMsg = true,
    },
    nicknameUpdated = {
    },
    open = {
    },
    playerInPartyLastUpdated = {
    },
    url = {
    },
    urlVersion = {
    },
    xiaowoVersionGroupLastUpdated = {
    },
  },
  PasswordCodeData = {
    createTime = {
    },
    passwordCode = {
    },
    taskId = {
    },
    usedPasswordCode = {
      isSet = true,
    },
    usedPasswordCode_deleted = {
      isSetDel = true,
      targetFieldName = "usedPasswordCode",
    },
  },
  PersonalityStateInfo = {
    expiredTimeMs = {
    },
    state = {
    },
    updateTimeMs = {
    },
  },
  PetClientCache = {
    k = {
    },
    v = {
    },
  },
  PetClothing = {
    petId = {
    },
    wearClothing = {
      isMap = true,
      keyName = "clothingType",
    },
    wearClothing_deleted = {
      isMapDel = true,
      targetFieldName = "wearClothing",
    },
  },
  PetClothingInfo = {
    ownedClothing = {
      isMap = true,
      keyName = "id",
    },
    ownedClothing_deleted = {
      isMapDel = true,
      targetFieldName = "ownedClothing",
    },
  },
  PetEvictRedDot = {
    friendId = {
    },
    gotTime = {
    },
  },
  PetFavorInfo = {
    hungryDurationSum = {
    },
    interactAddFavorSum = {
    },
    interactAddFavorSumLastResetTime = {
    },
    lastCheckHungryTime = {
    },
    lastFeedAddFavorTime = {
    },
  },
  PetFeedRedDot = {
    friendId = {
    },
    gotTime = {
    },
  },
  PetFertilizeInfo = {
    fertilizeHistoryMap = {
      isMap = true,
      keyName = "traceId",
    },
    fertilizeHistoryMap_deleted = {
      isMapDel = true,
      targetFieldName = "fertilizeHistoryMap",
    },
    fertilizeTraceIdCounter = {
    },
  },
  PetGiftInfo = {
    items = {
      isMap = true,
      keyName = "itemId",
    },
    items_deleted = {
      isMapDel = true,
      targetFieldName = "items",
    },
    lastGiftTime = {
    },
    prepared = {
    },
    todayGiftCount = {
    },
    todayGiftCountLastResetTime = {
    },
  },
  PetGiftItem = {
    itemId = {
    },
    itemNum = {
    },
  },
  PetOwnedClothing = {
    id = {
    },
    obtainTime = {
    },
  },
  PetSecurityInfo = {
    bannedEndTime = {
    },
  },
  PetWearClothing = {
    clothingId = {
    },
    clothingType = {
    },
  },
  PickFactionActivityData = {
    playerPickFactionData = {
      isMap = true,
      keyName = "index",
    },
    playerPickFactionData_deleted = {
      isMapDel = true,
      targetFieldName = "playerPickFactionData",
    },
  },
  PickFactionRecord = {
    getBigReward = {
    },
    index = {
    },
    pickFaction = {
    },
    settleFaction = {
    },
  },
  PicLikeCountInfo = {
    isLike = {
    },
    likeCount = {
    },
    picKey = {
    },
  },
  PicLikeInfo = {
    time = {
    },
    uid = {
    },
  },
  PixuiRedDotInfo = {
    clearMs = {
    },
    deleteMs = {
    },
    id = {
    },
  },
  PlacedObjectPosition = {
    xPos = {
    },
    xSpeed = {
    },
    xVec = {
    },
    yPos = {
    },
    ySpeed = {
    },
    yVec = {
    },
    zPos = {
    },
    zSpeed = {
    },
    zVec = {
    },
  },
  Placeholder = {
    total = {
    },
    units = {
      isMap = true,
      keyName = "id",
    },
    units_deleted = {
      isMapDel = true,
      targetFieldName = "units",
    },
  },
  PlaceholderUnit = {
    id = {
    },
    number = {
    },
    timestampMs = {
    },
  },
  PlatPrivilegesInfo = {
    expireTimeMs = {
    },
    isSendGift = {
    },
    platPrivileges = {
    },
    updateTime = {
    },
  },
  PlayerBlessBagInfo = {
    bagId = {
    },
    expireTimeMs = {
    },
    updateTime = {
    },
  },
  PlayerClubChallengeBattleInfo = {
    battleId = {
    },
    clubId = {
    },
    isValid = {
    },
    startTime = {
    },
  },
  PlayerClubChallengeReportInfo = {
    lastReportClubId = {
    },
    lastReportStarLight = {
    },
    reportCount = {
    },
    reportStarLight = {
    },
  },
  PlayerClubRecord = {
    cid = {
    },
    joinTimeMs = {
    },
    leaveTimeMs = {
    },
  },
  PlayerDepositInfo = {
    balance = {
    },
    boughtIds = {
      isSet = true,
    },
    boughtIds_deleted = {
      isSetDel = true,
      targetFieldName = "boughtIds",
    },
    id = {
    },
    lastBoughtTs = {
    },
    refreshTs = {
    },
  },
  PlayerEventData = {
    reportGameplayEventSerialId = {
      isMap = true,
      keyName = "serialId",
    },
    reportGameplayEventSerialId_deleted = {
      isMapDel = true,
      targetFieldName = "reportGameplayEventSerialId",
    },
    statistics = {
      isMap = true,
      keyName = "type",
    },
    statistics_deleted = {
      isMapDel = true,
      targetFieldName = "statistics",
    },
  },
  PlayerGameActionInfos = {
    dailyHugInfos = {
      isMap = true,
      keyName = "sceneType",
    },
    dailyHugInfos_deleted = {
      isMapDel = true,
      targetFieldName = "dailyHugInfos",
    },
    lastUpdateTime = {
    },
  },
  PlayerGamePlay = {
    confVersion = {
    },
    featureType = {
    },
    pakVersion = {
    },
  },
  PlayerGameTime = {
    type = {
    },
    value = {
    },
  },
  PlayerGrayRuleData = {
    grayRuleConfId = {
    },
    tagSucc = {
    },
    tagTime = {
    },
  },
  PlayerGrayTagsInfo = {
    infos = {
      isMap = true,
      keyName = "grayRuleConfId",
    },
    infos_deleted = {
      isMapDel = true,
      targetFieldName = "infos",
    },
  },
  PlayerHugOther = {
    hugCount = {
    },
    otherUid = {
    },
  },
  PlayerHugOthersInfo = {
    hugOthers = {
      isMap = true,
      keyName = "otherUid",
    },
    hugOthers_deleted = {
      isMapDel = true,
      targetFieldName = "hugOthers",
    },
    HugStrangerCount = {
    },
    HugStrangerNumber = {
    },
    sceneType = {
    },
  },
  PlayerIAAData = {
    forceGuideRefreshTs = {
    },
    forceGuideRequired = {
    },
    forceGuideStat = {
      isMsg = true,
    },
    toAppRewardId = {
    },
    toAppRewardTs = {
    },
  },
  PlayerIAAInfo = {
    dailyCount = {
    },
    id = {
    },
    totalCount = {
    },
    updateTs = {
    },
  },
  PlayerIAAStat = {
    battleStartCount = {
    },
    onlineMinutes = {
    },
  },
  PlayerIdipInfo = {
    dsReplayRecordCount = {
    },
  },
  PlayerLevelEstimation = {
    levelId = {
      isSet = true,
    },
    levelId_deleted = {
      isSetDel = true,
      targetFieldName = "levelId",
    },
  },
  PlayerPakPlayRecord = {
    LastPlayTime = {
    },
    pakId = {
    },
    weekPakPlayRecord = {
      isMap = true,
      keyName = "weekStartTime",
    },
    weekPakPlayRecord_deleted = {
      isMapDel = true,
      targetFieldName = "weekPakPlayRecord",
    },
  },
  PlayerProfileInfo = {
    accessToken = {
    },
    accountType = {
    },
    checkedNickname = {
    },
    checkedNicknameSec = {
    },
    inviteRecallUid = {
    },
    inviteRegisterUid = {
    },
    mobileGearLevel = {
    },
    nicknameCheckNote = {
      isMap = true,
      keyName = "checkReason",
    },
    nicknameCheckNote_deleted = {
      isMapDel = true,
      targetFieldName = "nicknameCheckNote",
    },
    originPlatNickname = {
    },
    prefabNickname = {
    },
    registerDeviceId = {
    },
    vipExp = {
    },
    wxMiniGameSessionKey = {
    },
  },
  PlayerPublicBasicInfo = {
    accountState = {
    },
    accountType = {
    },
    bindAccountInfos = {
      isMap = true,
      keyName = "accountType",
    },
    bindAccountInfos_deleted = {
      isMapDel = true,
      targetFieldName = "bindAccountInfos",
    },
    channelId = {
    },
    clientDeviceType = {
    },
    loginPlat = {
    },
    loginTimeMs = {
    },
    logoutTimeMs = {
    },
    netWork = {
    },
    osVersion = {
    },
    regChannelDis = {
    },
    registerRegionId = {
    },
    registerTimeMs = {
    },
    svrId = {
    },
    systemHardware = {
    },
    systemSoftware = {
    },
    trackId = {
    },
  },
  PlayerPublicBattleInfo = {
    battleId = {
    },
    battleSvrId = {
    },
    matchTypeId = {
    },
  },
  PlayerPublicEquipments = {
    activeSuitBook = {
      isSet = true,
    },
    activeSuitBook_deleted = {
      isSetDel = true,
      targetFieldName = "activeSuitBook",
    },
    animeDressOutline = {
    },
    backupDressUpInfos = {
      isSet = true,
    },
    backupDressUpInfos_deleted = {
      isSetDel = true,
      targetFieldName = "backupDressUpInfos",
    },
    displayBoardInfo = {
      isMsg = true,
    },
    dressCount = {
    },
    dressItemInfo = {
      isMap = true,
      keyName = "dressUpType",
    },
    dressItemInfo_deleted = {
      isMapDel = true,
      targetFieldName = "dressItemInfo",
    },
    dressUpDetailInfos = {
      isMap = true,
      keyName = "itemId",
    },
    dressUpDetailInfos_deleted = {
      isMapDel = true,
      targetFieldName = "dressUpDetailInfos",
    },
    dressUpInfos = {
      isSet = true,
    },
    dressUpInfos_deleted = {
      isSetDel = true,
      targetFieldName = "dressUpInfos",
    },
    dressUpValue = {
    },
    equipDressInfo = {
      isMap = true,
      keyName = "dressUpType",
    },
    equipDressInfo_deleted = {
      isMapDel = true,
      targetFieldName = "equipDressInfo",
    },
    equipItemInfo = {
      isMap = true,
      keyName = "itemType",
    },
    equipItemInfo_deleted = {
      isMapDel = true,
      targetFieldName = "equipItemInfo",
    },
    fashionScores = {
      isAry = true,
    },
    fashionScores_is_cleared = {
      isAryClear = true,
      targetFieldName = "fashionScores",
    },
    fashionValue = {
    },
    fashionValues = {
      isMap = true,
      keyName = "seasonId",
    },
    fashionValues_deleted = {
      isMapDel = true,
      targetFieldName = "fashionValues",
    },
    initSeasonFashionSuitTimeMs = {
    },
    profileTheme = {
    },
    qualifyDailyRankInfos = {
      isMap = true,
      keyName = "qualifyType",
    },
    qualifyDailyRankInfos_deleted = {
      isMapDel = true,
      targetFieldName = "qualifyDailyRankInfos",
    },
    readyBattleBagInfo = {
      isMap = true,
      keyName = "dressUpType",
    },
    readyBattleBagInfo_deleted = {
      isMapDel = true,
      targetFieldName = "readyBattleBagInfo",
    },
    teamShowTheme = {
    },
  },
  PlayerPublicFriendData = {
    intimateRelationUnlockExtraCnt = {
    },
  },
  PlayerPublicGameData = {
    abTestInfo = {
      isMap = true,
      keyName = "testType",
    },
    abTestInfo_deleted = {
      isMapDel = true,
      targetFieldName = "abTestInfo",
    },
    bpInfo = {
      isMap = true,
      keyName = "type",
    },
    bpInfo_deleted = {
      isMapDel = true,
      targetFieldName = "bpInfo",
    },
    commonlyUsedHeroId = {
    },
    communityChannelIconInfo = {
      isMsg = true,
    },
    danceHighestScore = {
    },
    detailInfo = {
      isMsg = true,
    },
    extraIntegralInfo = {
      isMap = true,
      keyName = "id",
    },
    extraIntegralInfo_deleted = {
      isMapDel = true,
      targetFieldName = "extraIntegralInfo",
    },
    farmSquadActivityLuckyFlag = {
    },
    intimateMotion = {
      isMap = true,
      keyName = "uid",
    },
    intimateMotion_deleted = {
      isMapDel = true,
      targetFieldName = "intimateMotion",
    },
    monthCardInfo = {
      isMap = true,
      keyName = "id",
    },
    monthCardInfo_deleted = {
      isMapDel = true,
      targetFieldName = "monthCardInfo",
    },
    permitInfo = {
      isMsg = true,
    },
    playerGameTimes = {
      isMap = true,
      keyName = "type",
    },
    playerGameTimes_deleted = {
      isMapDel = true,
      targetFieldName = "playerGameTimes",
    },
    playerGameTimesSeasonID = {
    },
    publicFriendData = {
      isMsg = true,
    },
    qualifyingInfo = {
      isMsg = true,
    },
    qualifyTypeInfo = {
      isMap = true,
      keyName = "qualifyType",
    },
    qualifyTypeInfo_deleted = {
      isMapDel = true,
      targetFieldName = "qualifyTypeInfo",
    },
    rankGeoInfo = {
      isMsg = true,
    },
    rankInfo = {
      isMap = true,
      keyName = "type",
    },
    rankInfo_deleted = {
      isMapDel = true,
      targetFieldName = "rankInfo",
    },
    showQualifyType = {
    },
    showSubHistoryMaxQualifyType = {
    },
    statCluster = {
      isMsg = true,
    },
    totalOnlineTime = {
    },
    tradingCardCollectionCardInfos = {
      isMsg = true,
    },
    tradingCardCollectionInfos = {
      isMsg = true,
    },
  },
  PlayerPublicGameSettings = {
    acquireEquipPopUpEquipSlot = {
    },
    allowRecommendInfoCollect = {
    },
    arenaCameraControlType = {
    },
    arenaIsAutoNormalAttack = {
    },
    arenaTargetSelectionStrategy = {
    },
    arenaVoiceType = {
    },
    benefitCardEnable = {
    },
    birthdayVisibleRange = {
    },
    chaseGameSetting = {
      isMsg = true,
    },
    clientLanguage = {
    },
    clientVersion64 = {
    },
    currentActionState = {
    },
    customRoomAutoStartWhenFull = {
    },
    demandSwitch = {
    },
    DSVersion64 = {
    },
    gamePlay = {
      isMap = true,
      keyName = "featureType",
    },
    gamePlay_deleted = {
      isMapDel = true,
      targetFieldName = "gamePlay",
    },
    gameProtectionMainSwitch = {
    },
    gameProtectionSpecificSwitch = {
      isMsg = true,
    },
    headPublicInfoType = {
    },
    hideArenaBattleHistory = {
    },
    hideBattleHistory = {
    },
    hideClubInfo = {
    },
    hideEntertainmentQualifyInfo = {
    },
    hideFashionScore = {
    },
    hideFriendReq = {
    },
    hideIntimateRelation = {
    },
    hideIntimateRelationTab = {
    },
    hideLobbyInvitation = {
    },
    hideLocation = {
    },
    hidePersonalityState = {
    },
    hidePersonalProfile = {
    },
    hidePlayerStatus = {
    },
    hidePlayerStatusSwitch = {
    },
    hidePlayerStatusTime = {
    },
    hidePlayerStatusWeekCnt = {
    },
    hideProfileToFriend = {
    },
    hideProfileToStranger = {
    },
    hideQQFriendReq = {
    },
    hideQualifyingInfo = {
    },
    hideReservation = {
    },
    hideRoomExtraInfo = {
    },
    hideRoomInvitation = {
    },
    hideSubHistoryMaxQualifyInfo = {
    },
    hideSuitBook = {
    },
    hideTitle = {
    },
    hideUgcInfo = {
    },
    hideWorldLobbyChat = {
    },
    hideWorldLobbyChatInit = {
    },
    intimateOnlineNotice = {
    },
    lobbyModeType = {
    },
    lobbyModeTypeForAbTest = {
      isMap = true,
      keyName = "abtestId",
    },
    lobbyModeTypeForAbTest_deleted = {
      isMapDel = true,
      targetFieldName = "lobbyModeTypeForAbTest",
    },
    needActionConfirm = {
    },
    notAllowRecommendFriends = {
    },
    notSearchedByUid = {
    },
    offAinpcChatPush = {
    },
    photoLibraryDescription = {
    },
    playerGameGearSetting = {
      isMsg = true,
    },
    privilegeLevel = {
    },
    privilegeSwitch = {
    },
    profileShowCollection = {
      isSet = true,
    },
    profileShowCollection_deleted = {
      isSetDel = true,
      targetFieldName = "profileShowCollection",
    },
    profileTopInfo = {
      isMap = true,
      keyName = "k",
    },
    profileTopInfo_deleted = {
      isMapDel = true,
      targetFieldName = "profileTopInfo",
    },
    qqChatSyncStatus = {
    },
    raffleDailyLimitInBlackList = {
    },
    raffleDailyLimitInIdipWhiteList = {
    },
    raffleUseTagDailyLimit = {
    },
    rejectStrangerChatMsg = {
    },
    seasonFashionShowStatus = {
    },
    showCups = {
    },
    showCustomActionSelectFriendInfo = {
    },
    showSeasonFashion = {
    },
    strangerFollow = {
    },
  },
  PlayerPublicHistoryData = {
    historyQualifyingInfo = {
      isMap = true,
      keyName = "season",
    },
    historyQualifyingInfo_deleted = {
      isMapDel = true,
      targetFieldName = "historyQualifyingInfo",
    },
    historyRankInfo = {
      isMap = true,
      keyName = "rankId",
    },
    historyRankInfo_deleted = {
      isMapDel = true,
      targetFieldName = "historyRankInfo",
    },
    hourChargeInfo = {
      isMap = true,
      keyName = "hourTime",
    },
    hourChargeInfo_deleted = {
      isMapDel = true,
      targetFieldName = "hourChargeInfo",
    },
    shocksRewardMap = {
      isMap = true,
      keyName = "qualifyingType",
    },
    shocksRewardMap_deleted = {
      isMapDel = true,
      targetFieldName = "shocksRewardMap",
    },
  },
  PlayerPublicLiveStatus = {
    battleJoinTs = {
    },
    battleMatchType = {
    },
    battleUgcId = {
    },
    lastKeepAliveTs = {
    },
    miniGameConsole = {
    },
    roomExtraInfo = {
      isMsg = true,
    },
    roomId = {
    },
    roomIsFull = {
    },
    roomStatus = {
    },
    status = {
    },
    statusDetails = {
      isMsg = true,
    },
  },
  PlayerPublicProfileInfo = {
    appearanceRoadShow = {
      isMap = true,
      keyName = "type",
    },
    appearanceRoadShow_deleted = {
      isMapDel = true,
      targetFieldName = "appearanceRoadShow",
    },
    atCook = {
    },
    atFarm = {
    },
    atHouse = {
    },
    atRich = {
    },
    atXiaowo = {
    },
    birthdayMonthDay = {
    },
    birthdayVisibleRange = {
    },
    catchphrase = {
    },
    channelId = {
    },
    clubIds = {
      isSet = true,
    },
    clubIds_deleted = {
      isSetDel = true,
      targetFieldName = "clubIds",
    },
    clubInfo = {
      isMap = true,
      keyName = "cid",
    },
    clubInfo_deleted = {
      isMapDel = true,
      targetFieldName = "clubInfo",
    },
    createRoleProfile = {
    },
    creatorId = {
    },
    cupsNum = {
    },
    cupsTotal = {
    },
    curCupsCycle = {
    },
    exp = {
    },
    farmReturning = {
      isMsg = true,
    },
    fashionLv = {
    },
    fashionSubLv = {
    },
    gender = {
    },
    homepageAction = {
      isMsg = true,
    },
    intimateRelationInfo = {
      isMsg = true,
    },
    kungFuPandaRacingCostTimeMs = {
    },
    labels = {
      isSet = true,
    },
    labels_deleted = {
      isSetDel = true,
      targetFieldName = "labels",
    },
    level = {
    },
    location = {
      isMsg = true,
    },
    nickname = {
    },
    openId = {
    },
    personalityState = {
      isMsg = true,
    },
    platAvatarInfo = {
    },
    platId = {
    },
    platNickName = {
    },
    platOpenId = {
    },
    platProvileges = {
      isMsg = true,
    },
    profile = {
    },
    returnExpiredSec = {
    },
    returning = {
    },
    shortUid = {
    },
    signature = {
    },
    stickFriends = {
      isMap = true,
      keyName = "scene",
    },
    stickFriends_deleted = {
      isMapDel = true,
      targetFieldName = "stickFriends",
    },
    ugcAuthDesc = {
    },
    ugcAuthType = {
    },
    ugcExp = {
    },
    ugcLevel = {
    },
    vipExp = {
    },
    vipLv = {
    },
  },
  PlayerPublicSceneData = {
    cookInfo = {
      isMsg = true,
    },
    farmInfo = {
      isMsg = true,
    },
    farmType = {
    },
    houseInfo = {
      isMsg = true,
    },
    lobbyInfo = {
      isMsg = true,
    },
    richInfo = {
      isMsg = true,
    },
    xiaoWoInfo = {
      isMsg = true,
    },
  },
  PlayerPublicSummaryInfo = {
    battleModeData = {
      isMap = true,
      keyName = "id",
    },
    battleModeData_deleted = {
      isMapDel = true,
      targetFieldName = "battleModeData",
    },
  },
  PlayerPushTopic = {
    handle = {
      isMap = true,
      keyName = "index",
    },
    handle_deleted = {
      isMapDel = true,
      targetFieldName = "handle",
    },
  },
  PlayerRaffleGroupInfo = {
    awardedPoints = {
      isSet = true,
    },
    awardedPoints_deleted = {
      isSetDel = true,
      targetFieldName = "awardedPoints",
    },
    awardedTimes = {
      isSet = true,
    },
    awardedTimes_deleted = {
      isSetDel = true,
      targetFieldName = "awardedTimes",
    },
    benefitIds = {
      isSet = true,
    },
    benefitIds_deleted = {
      isSetDel = true,
      targetFieldName = "benefitIds",
    },
    chosenReward = {
      isSet = true,
    },
    chosenReward_deleted = {
      isSetDel = true,
      targetFieldName = "chosenReward",
    },
    confirmedSlot = {
      isMap = true,
      keyName = "slotId",
    },
    confirmedSlot_deleted = {
      isMapDel = true,
      targetFieldName = "confirmedSlot",
    },
    costs = {
      isMap = true,
      keyName = "coinType",
    },
    costs_deleted = {
      isMapDel = true,
      targetFieldName = "costs",
    },
    cratesBounceCount = {
    },
    dailyCosts = {
      isMap = true,
      keyName = "coinType",
    },
    dailyCosts_deleted = {
      isMapDel = true,
      targetFieldName = "dailyCosts",
    },
    dailyTimes = {
    },
    extraCounters = {
      isMap = true,
      keyName = "k",
    },
    extraCounters_deleted = {
      isMapDel = true,
      targetFieldName = "extraCounters",
    },
    extraShowStr = {
    },
    id = {
    },
    inventory = {
      isMap = true,
      keyName = "uuid",
    },
    inventory_deleted = {
      isMapDel = true,
      targetFieldName = "inventory",
    },
    points = {
    },
    subItems = {
      isMap = true,
      keyName = "uuid",
    },
    subItems_deleted = {
      isMapDel = true,
      targetFieldName = "subItems",
    },
    substitutes = {
      isMap = true,
      keyName = "uuid",
    },
    substitutes_deleted = {
      isMapDel = true,
      targetFieldName = "substitutes",
    },
    times = {
    },
  },
  PlayerRaffleInfo = {
    benefitIds = {
      isSet = true,
    },
    benefitIds_deleted = {
      isSetDel = true,
      targetFieldName = "benefitIds",
    },
    chosenReward = {
      isSet = true,
    },
    chosenReward_deleted = {
      isSetDel = true,
      targetFieldName = "chosenReward",
    },
    dailyTimes = {
    },
    freeOneDraw = {
      isMsg = true,
    },
    guarantee = {
      isMap = true,
      keyName = "type",
    },
    guarantee_deleted = {
      isMapDel = true,
      targetFieldName = "guarantee",
    },
    id = {
    },
    lastRefreshTs = {
    },
    multiDraw = {
      isMsg = true,
    },
    oneDraw = {
      isMsg = true,
    },
    rewardGroups = {
      isMap = true,
      keyName = "groupId",
    },
    rewardGroups_deleted = {
      isMapDel = true,
      targetFieldName = "rewardGroups",
    },
    rewards = {
      isMap = true,
      keyName = "rewardId",
    },
    rewards_deleted = {
      isMapDel = true,
      targetFieldName = "rewards",
    },
    testWaterTimes = {
    },
    times = {
    },
  },
  PlayerRankData = {
    hideAllFromOthers = {
    },
    lastGeoChangeTs = {
    },
    lastGeoInfo = {
      isMsg = true,
    },
    rankReportStatus = {
      isMap = true,
      keyName = "rankId",
    },
    rankReportStatus_deleted = {
      isMapDel = true,
      targetFieldName = "rankReportStatus",
    },
    refreshQueueDb = {
      isMap = true,
      keyName = "type",
    },
    refreshQueueDb_deleted = {
      isMapDel = true,
      targetFieldName = "refreshQueueDb",
    },
    seasonCities = {
      isSet = true,
    },
    seasonCities_deleted = {
      isSetDel = true,
      targetFieldName = "seasonCities",
    },
    seasonProvinces = {
      isSet = true,
    },
    seasonProvinces_deleted = {
      isSetDel = true,
      targetFieldName = "seasonProvinces",
    },
    seasonTowns = {
      isSet = true,
    },
    seasonTowns_deleted = {
      isSetDel = true,
      targetFieldName = "seasonTowns",
    },
    settleInfos = {
      isMap = true,
      keyName = "rankId",
    },
    settleInfos_deleted = {
      isMapDel = true,
      targetFieldName = "settleInfos",
    },
    weeklyGeoModTimes = {
    },
  },
  PlayerRankGeoInfo = {
    city = {
    },
    cityStr = {
    },
    detail = {
    },
    isOpen = {
    },
    lastUpdateTs = {
    },
    nation = {
    },
    nationStr = {
    },
    province = {
    },
    provinceStr = {
    },
    region = {
    },
    town = {
    },
    townStr = {
    },
  },
  PlayerRankSettlement = {
    lastRewardedTs = {
    },
    participatedSeasons = {
      isSet = true,
    },
    participatedSeasons_deleted = {
      isSetDel = true,
      targetFieldName = "participatedSeasons",
    },
    rankId = {
    },
    rewardedSeasons = {
      isSet = true,
    },
    rewardedSeasons_deleted = {
      isSetDel = true,
      targetFieldName = "rewardedSeasons",
    },
  },
  PlayerRecruiteInfo = {
    activeTimeMs = {
    },
    identityCode = {
    },
    recruiter = {
      isMsg = true,
    },
    recruiteRaffles = {
      isMap = true,
      keyName = "id",
    },
    recruiteRaffles_deleted = {
      isMapDel = true,
      targetFieldName = "recruiteRaffles",
    },
    recruits = {
      isMap = true,
      keyName = "uid",
    },
    recruits_deleted = {
      isMapDel = true,
      targetFieldName = "recruits",
    },
    weeklyGotCoin = {
    },
    weeklyGotCoinByLogin = {
    },
    weeklyGotCoinByPlay = {
    },
  },
  PlayerSnsAcceptedRecord = {
    acceptTs = {
    },
    invitee = {
    },
  },
  PlayerSnsAcceptInfo = {
    accepted = {
      isMap = true,
      keyName = "uuid",
    },
    accepted_deleted = {
      isMapDel = true,
      targetFieldName = "accepted",
    },
    cfgId = {
    },
    lastData = {
      isMsg = true,
    },
    updateTs = {
    },
  },
  PlayerSnsInvitationActivityData = {
    activityId = {
    },
  },
  PlayerSnsInvitationBrief = {
    inviterId = {
    },
    ts = {
    },
    uuid = {
    },
  },
  PlayerSnsInvitationData = {
    activity = {
      isMsg = true,
    },
    raffle = {
      isMsg = true,
    },
  },
  PlayerSnsInvitationInfo = {
    acceptInfos = {
      isMap = true,
      keyName = "acceptTs",
    },
    acceptInfos_deleted = {
      isMapDel = true,
      targetFieldName = "acceptInfos",
    },
    cfgId = {
    },
    codeStr = {
    },
    data = {
      isMsg = true,
    },
    uuid = {
    },
  },
  PlayerSnsInvitationRaffleData = {
    poolId = {
    },
    raffleId = {
    },
  },
  PlayerStatisticInfo = {
    type = {
    },
    value = {
    },
  },
  PlayerStatusDetail = {
    startTimestamp = {
    },
    status = {
    },
  },
  PlayerStatusDetails = {
    onlineStatus = {
      isMsg = true,
    },
    roomStatus = {
      isMsg = true,
    },
    sceneStatus = {
      isMsg = true,
    },
    specialStatus = {
      isMsg = true,
    },
  },
  PlayerUgcLobbyInfo = {
    lobbyId = {
    },
    mapId = {
    },
    ugcId = {
    },
  },
  PlayTopicTaskInfo = {
    passCount = {
    },
    playCount = {
    },
    topicId = {
    },
  },
  PositionCollections = {
    positionCollections = {
      isMap = true,
      keyName = "position",
    },
    positionCollections_deleted = {
      isMapDel = true,
      targetFieldName = "positionCollections",
    },
  },
  PrayerCardActivityData = {
    friendDailyGive = {
      isMap = true,
      keyName = "uid",
    },
    friendDailyGive_deleted = {
      isMapDel = true,
      targetFieldName = "friendDailyGive",
    },
    giveNum = {
    },
    prayerCardGiveRecord = {
      isMap = true,
      keyName = "giveNum",
    },
    prayerCardGiveRecord_deleted = {
      isMapDel = true,
      targetFieldName = "prayerCardGiveRecord",
    },
    prayerCardInfo = {
      isMap = true,
      keyName = "prayerCardId",
    },
    prayerCardInfo_deleted = {
      isMapDel = true,
      targetFieldName = "prayerCardInfo",
    },
    rewardActivityData = {
      isMap = true,
      keyName = "activityId",
    },
    rewardActivityData_deleted = {
      isMapDel = true,
      targetFieldName = "rewardActivityData",
    },
  },
  PrayerCardFriendDailyGive = {
    giveEpochSecs = {
    },
    giveNum = {
    },
    uid = {
    },
  },
  PrayerCardGiveRecord = {
    giveEpochSecs = {
    },
    giveNum = {
    },
    prayerCardId = {
    },
    uid = {
    },
  },
  PrayerCardInfo = {
    acquireNum = {
    },
    isLooked = {
    },
    prayerCardId = {
    },
  },
  PrayerCardRewardActivityData = {
    activityId = {
    },
    rewardItemInfo = {
      isMap = true,
      keyName = "rewardItemId",
    },
    rewardItemInfo_deleted = {
      isMapDel = true,
      targetFieldName = "rewardItemInfo",
    },
  },
  PrayerCardRewardItemInfo = {
    rewardItemId = {
    },
    totalNum = {
    },
  },
  PrayInfo = {
    expireTimeMs = {
    },
    propertyIds = {
      isSet = true,
    },
    propertyIds_deleted = {
      isSetDel = true,
      targetFieldName = "propertyIds",
    },
    resultId = {
    },
    todayTopPrayQuality = {
    },
  },
  PublicChatInfo = {
    arenaCommunityChannel = {
      isMsg = true,
    },
    boundChatModuleList = {
      isSet = true,
    },
    boundChatModuleList_deleted = {
      isSetDel = true,
      targetFieldName = "boundChatModuleList",
    },
    chatModuleUpdateTimestampMs = {
    },
    chatRedDotInfo = {
      isMap = true,
      keyName = "formatKey",
    },
    chatRedDotInfo_deleted = {
      isMapDel = true,
      targetFieldName = "chatRedDotInfo",
    },
    communityChannelHotTopicInfo = {
      isMsg = true,
    },
    farmCommunityChannel = {
      isMsg = true,
    },
    newStarChat = {
      isMsg = true,
    },
    oneKeyClearNotReadTimeMs = {
    },
    P2PChat = {
      isMsg = true,
    },
    spCommunityChannel = {
      isMsg = true,
    },
    teamRecruitChat = {
      isMsg = true,
    },
    tradingCardCommunityChannel = {
      isMsg = true,
    },
    wolfKillCommunityChannel = {
      isMsg = true,
    },
    worldChat = {
      isMsg = true,
    },
  },
  PublicCookInfo = {
    cookId = {
    },
  },
  PublicFarmInfo = {
    farmCheckedLeave = {
    },
    farmId = {
    },
  },
  PublicHouseInfo = {
    houseId = {
    },
  },
  PublicLobbyInfo = {
    lobbyId = {
    },
    mapId = {
    },
    mapType = {
    },
  },
  PublicRichInfo = {
    boardId = {
    },
  },
  PublicXiaoWoInfo = {
    xiaoWoId = {
    },
  },
  PushTopicHandle = {
    index = {
    },
    partition = {
    },
    topic = {
    },
    upTimeSec = {
    },
  },
  QAInvestInfo = {
    id = {
    },
    investStatus = {
    },
  },
  QAInvestTag = {
    createdTime = {
    },
    id = {
    },
  },
  QingShuangTrialData = {
    fiveDimensions = {
      isAry = true,
    },
    fiveDimensions_is_cleared = {
      isAryClear = true,
      targetFieldName = "fiveDimensions",
    },
    receivedReward = {
      isAry = true,
    },
    receivedReward_is_cleared = {
      isAryClear = true,
      targetFieldName = "receivedReward",
    },
    trialScore = {
    },
  },
  QQApplicationInfo = {
    qqTeamInfo = {
      isMsg = true,
    },
  },
  QQTeamInfo = {
    roomCreateTaskId = {
    },
    roomTaskExpiredTime = {
    },
  },
  QualifyingBattleRecord = {
    allLevelFirst = {
    },
    battleNum = {
    },
    levelWinNum = {
      isAry = true,
    },
    levelWinNum_is_cleared = {
      isAryClear = true,
      targetFieldName = "levelWinNum",
    },
  },
  QualifyingDailyRankInfo = {
    expireMs = {
    },
    qualifyType = {
    },
    rankNo = {
    },
  },
  QualifyingDetailInfo = {
    battleRecord = {
      isMsg = true,
    },
    teamBattleRecord = {
      isMsg = true,
    },
    totalLevelNum = {
    },
    winNum = {
    },
  },
  QualifyingExtraIntegralInfo = {
    finishNum = {
    },
    id = {
    },
    updateTimeMs = {
    },
  },
  QualifyingInfo = {
    alreadySendMail = {
    },
    battleNum = {
    },
    continueWinTimes = {
    },
    dailyRankNo = {
    },
    dailyRankTag = {
    },
    degreeID = {
    },
    degreeStar = {
    },
    degreeType = {
    },
    degreeTypeInt = {
    },
    firstDegreeProtect = {
    },
    lastWinTime = {
    },
    latestRankNo = {
    },
    mailRewardId = {
      isSet = true,
    },
    mailRewardId_deleted = {
      isSetDel = true,
      targetFieldName = "mailRewardId",
    },
    maxContinueWinTimes = {
    },
    maxDedegreeType = {
    },
    maxDegreeID = {
    },
    maxIntegral = {
    },
    maxStar = {
    },
    protectedScore = {
    },
    qualifyingIntegral = {
    },
    season = {
    },
    showReward = {
    },
    topRankNo = {
    },
    winNum = {
    },
  },
  QualifyingTypeInfo = {
    historyQualifyInfo = {
      isMap = true,
      keyName = "season",
    },
    historyQualifyInfo_deleted = {
      isMapDel = true,
      targetFieldName = "historyQualifyInfo",
    },
    qualifyInfo = {
      isMsg = true,
    },
    qualifyType = {
    },
    seasonMail = {
      isMap = true,
      keyName = "season",
    },
    seasonMail_deleted = {
      isMapDel = true,
      targetFieldName = "seasonMail",
    },
  },
  QuizData = {
    isReward = {
    },
    questionList = {
      isMap = true,
      keyName = "id",
    },
    questionList_deleted = {
      isMapDel = true,
      targetFieldName = "questionList",
    },
  },
  QuizQuestionData = {
    answeredChoice = {
      isSet = true,
    },
    answeredChoice_deleted = {
      isSetDel = true,
      targetFieldName = "answeredChoice",
    },
    correct = {
    },
    id = {
    },
  },
  RacingRankRewardRecord = {
    rankDay = {
    },
    updateTimeMs = {
    },
  },
  RaffleBIDiscount = {
    discountAtDraw = {
      isSet = true,
    },
    discountAtDraw_deleted = {
      isSetDel = true,
      targetFieldName = "discountAtDraw",
    },
    discountNum = {
    },
    updateTs = {
    },
  },
  RaffleBISeq = {
    expTags = {
      isAry = true,
    },
    expTags_is_cleared = {
      isAryClear = true,
      targetFieldName = "expTags",
    },
    recid = {
    },
    refreshTs = {
    },
    seq = {
      isAry = true,
    },
    seq_is_cleared = {
      isAryClear = true,
      targetFieldName = "seq",
    },
  },
  RaffleCommonCost = {
    coinType = {
    },
    num = {
    },
  },
  RaffleCommonData = {
    freeTicket = {
      isMsg = true,
    },
    intSet = {
      isMsg = true,
    },
    intVal = {
    },
    longVal = {
    },
    poolGuarantee = {
      isMsg = true,
    },
    poolPurchase = {
      isMsg = true,
    },
    strVal = {
    },
  },
  RaffleCommonInfo = {
    dailyTimes = {
    },
    extraData = {
      isMap = true,
      keyName = "keyId",
    },
    extraData_deleted = {
      isMapDel = true,
      targetFieldName = "extraData",
    },
    lastTs = {
    },
    pools = {
      isMap = true,
      keyName = "poolId",
    },
    pools_deleted = {
      isMapDel = true,
      targetFieldName = "pools",
    },
    raffleId = {
    },
    times = {
    },
  },
  RaffleCommonKeyVal = {
    keyId = {
    },
    value = {
      isMsg = true,
    },
  },
  RaffleCost = {
    coinType = {
    },
    num = {
    },
  },
  RaffleFreeDiscount = {
    adNum = {
    },
    gainNum = {
    },
    gainTs = {
      isSet = true,
    },
    gainTs_deleted = {
      isSetDel = true,
      targetFieldName = "gainTs",
    },
    shareNum = {
    },
    totalUsedCount = {
    },
    updateTs = {
    },
    usedCount = {
    },
  },
  RaffleFreeTicket = {
    dailyTickets = {
    },
    lastGainTs = {
    },
    totalTickets = {
    },
  },
  RaffleGuaranteeRecord = {
    counter = {
    },
    deactivated = {
    },
    drawnGroupIds = {
      isSet = true,
    },
    drawnGroupIds_deleted = {
      isSetDel = true,
      targetFieldName = "drawnGroupIds",
    },
    skipTextId = {
      isSet = true,
    },
    skipTextId_deleted = {
      isSetDel = true,
      targetFieldName = "skipTextId",
    },
    subId = {
    },
    type = {
    },
  },
  RaffleInventoryItem = {
    billNo = {
    },
    fromPool = {
    },
    isGrand = {
    },
    itemId = {
    },
    itemNum = {
    },
    uuid = {
    },
  },
  RafflePoolCommonInfo = {
    dailyTimes = {
    },
    extraData = {
      isMap = true,
      keyName = "keyId",
    },
    extraData_deleted = {
      isMapDel = true,
      targetFieldName = "extraData",
    },
    lastTs = {
    },
    poolId = {
    },
    rewardGroups = {
      isMap = true,
      keyName = "groupId",
    },
    rewardGroups_deleted = {
      isMapDel = true,
      targetFieldName = "rewardGroups",
    },
    rewards = {
      isMap = true,
      keyName = "rewardId",
    },
    rewards_deleted = {
      isMapDel = true,
      targetFieldName = "rewards",
    },
    times = {
    },
  },
  RafflePoolGuaranteeCounter = {
    counter = {
    },
    deactivated = {
    },
    type = {
    },
  },
  RafflePoolGuaranteeCounters = {
    counters = {
      isMap = true,
      keyName = "type",
    },
    counters_deleted = {
      isMapDel = true,
      targetFieldName = "counters",
    },
  },
  RafflePoolPurchaseCounter = {
    dailyTimes = {
    },
    discountTimes = {
    },
    lastDiscountTs = {
    },
    lastTs = {
    },
    times = {
    },
    type = {
    },
  },
  RafflePoolPurchaseCounters = {
    counters = {
      isMap = true,
      keyName = "type",
    },
    counters_deleted = {
      isMapDel = true,
      targetFieldName = "counters",
    },
  },
  RafflePoolStash = {
    guarantee = {
      isMap = true,
      keyName = "type",
    },
    guarantee_deleted = {
      isMapDel = true,
      targetFieldName = "guarantee",
    },
    id = {
    },
    rewardGroups = {
      isMap = true,
      keyName = "groupId",
    },
    rewardGroups_deleted = {
      isMapDel = true,
      targetFieldName = "rewardGroups",
    },
    rewards = {
      isMap = true,
      keyName = "rewardId",
    },
    rewards_deleted = {
      isMapDel = true,
      targetFieldName = "rewards",
    },
  },
  RafflePurchaseRecord = {
    firstDiscountTs = {
    },
    freeTimesAcquired = {
    },
    freeTimesDelivered = {
    },
    freeTimesSourceId = {
    },
    lastPerDiscountTs = {
    },
    lastTestWaterTs = {
    },
    lastTmpDiscountTs = {
    },
    lastTs = {
    },
    perDiscountTimes = {
    },
    recordBIDiscount = {
      isMsg = true,
    },
    recordFreeDiscount = {
      isMsg = true,
    },
    testWaterTimes = {
    },
    times = {
    },
    tmpDiscountTimes = {
    },
  },
  RaffleRewardCommonInfo = {
    extraData = {
      isMap = true,
      keyName = "keyId",
    },
    extraData_deleted = {
      isMapDel = true,
      targetFieldName = "extraData",
    },
    lastAtDraw = {
    },
    rewardId = {
    },
    totalDrawnTimes = {
    },
  },
  RaffleRewardGroupCommonInfo = {
    extraData = {
      isMap = true,
      keyName = "keyId",
    },
    extraData_deleted = {
      isMapDel = true,
      targetFieldName = "extraData",
    },
    groupId = {
    },
    lastAtDraw = {
    },
    totalDrawnTimes = {
    },
  },
  RaffleRewardGroupRecord = {
    dailyDrawnTimes = {
    },
    drawnTimes = {
    },
    groupId = {
    },
    lastDrawAtCount = {
    },
    totalDrawnTimes = {
    },
  },
  RaffleRewardRecord = {
    drawnTimes = {
    },
    fragNum = {
    },
    inGroupDrawnTimes = {
    },
    lastDrawnTs = {
    },
    rewardId = {
    },
    totalDrawnTimes = {
    },
  },
  RaffleRewardStashItem = {
    atDraw = {
    },
    expireMs = {
    },
    expireType = {
    },
    fromPoolId = {
    },
    index = {
    },
    isGrand = {
    },
    itemId = {
    },
    itemNum = {
    },
    reason = {
    },
    subItemIds = {
      isAry = true,
    },
    subItemIds_is_cleared = {
      isAryClear = true,
      targetFieldName = "subItemIds",
    },
    subItemNums = {
      isAry = true,
    },
    subItemNums_is_cleared = {
      isAryClear = true,
      targetFieldName = "subItemNums",
    },
    subItemWeights = {
      isAry = true,
    },
    subItemWeights_is_cleared = {
      isAryClear = true,
      targetFieldName = "subItemWeights",
    },
  },
  RaffleRewardSubItem = {
    bought = {
    },
    coinNum = {
    },
    coinType = {
    },
    itemIds = {
      isAry = true,
    },
    itemIds_is_cleared = {
      isAryClear = true,
      targetFieldName = "itemIds",
    },
    itemNums = {
      isAry = true,
    },
    itemNums_is_cleared = {
      isAryClear = true,
      targetFieldName = "itemNums",
    },
    rewardId = {
      isAry = true,
    },
    rewardId_is_cleared = {
      isAryClear = true,
      targetFieldName = "rewardId",
    },
    subRewardId = {
    },
    uuid = {
    },
  },
  RaffleRewardSubstitute = {
    afterItemId = {
    },
    afterItemNum = {
    },
    atDraw = {
    },
    beforeItemId = {
    },
    beforeItemNum = {
    },
    gainTs = {
    },
    uuid = {
    },
  },
  RaffleSlot = {
    lastTs = {
    },
    rewardIds = {
      isSet = true,
    },
    rewardIds_deleted = {
      isSetDel = true,
      targetFieldName = "rewardIds",
    },
    slotId = {
    },
  },
  RaffleStash = {
    billNo = {
    },
    extraGainItems = {
      isMap = true,
      keyName = "index",
    },
    extraGainItems_deleted = {
      isMapDel = true,
      targetFieldName = "extraGainItems",
    },
    gainItems = {
      isMap = true,
      keyName = "index",
    },
    gainItems_deleted = {
      isMapDel = true,
      targetFieldName = "gainItems",
    },
    points = {
    },
    poolStash = {
      isMap = true,
      keyName = "id",
    },
    poolStash_deleted = {
      isMapDel = true,
      targetFieldName = "poolStash",
    },
    positionId = {
    },
    raffleId = {
    },
    slot = {
      isMap = true,
      keyName = "slotId",
    },
    slot_deleted = {
      isMapDel = true,
      targetFieldName = "slot",
    },
    stashId = {
    },
    subGainItems = {
      isMap = true,
      keyName = "index",
    },
    subGainItems_deleted = {
      isMapDel = true,
      targetFieldName = "subGainItems",
    },
    subItems = {
      isMap = true,
      keyName = "uuid",
    },
    subItems_deleted = {
      isMapDel = true,
      targetFieldName = "subItems",
    },
  },
  RandomCollections = {
    collections = {
      isSet = true,
    },
    collections_deleted = {
      isSetDel = true,
      targetFieldName = "collections",
    },
    isRandom = {
    },
    singleCollection = {
    },
  },
  RankHistory = {
    items = {
      isMap = true,
      keyName = "seasonId",
    },
    items_deleted = {
      isMapDel = true,
      targetFieldName = "items",
    },
    rankId = {
    },
    refreshTs = {
    },
  },
  RankHistoryItem = {
    refreshTs = {
    },
    rewardRankNo = {
    },
    rewardReason = {
    },
    rewardTs = {
    },
    scores = {
      isAry = true,
    },
    scores_is_cleared = {
      isAryClear = true,
      targetFieldName = "scores",
    },
    seasonId = {
    },
  },
  RankInfoItem = {
    hide = {
    },
    scoreFields = {
      isAry = true,
    },
    scoreFields_is_cleared = {
      isAryClear = true,
      targetFieldName = "scoreFields",
    },
    subItems = {
      isMap = true,
      keyName = "subTypeId",
    },
    subItems_deleted = {
      isMapDel = true,
      targetFieldName = "subItems",
    },
    type = {
    },
    updateSeason = {
    },
    updateTimeMs = {
    },
  },
  RankInfoReportStatus = {
    bannedUntilTs = {
    },
    city = {
    },
    geoStatus = {
    },
    globalStatus = {
    },
    nation = {
    },
    province = {
    },
    rankId = {
    },
    town = {
    },
  },
  RankInfoSubItem = {
    retry = {
    },
    score = {
      isAry = true,
    },
    score_is_cleared = {
      isAryClear = true,
      targetFieldName = "score",
    },
    subTypeId = {
    },
  },
  RecentIntimacyData = {
    recentIntimacy = {
      isAry = true,
    },
    recentIntimacy_is_cleared = {
      isAryClear = true,
      targetFieldName = "recentIntimacy",
    },
    uid = {
    },
  },
  RechargeInfo = {
    firstChargeNtfTimes = {
    },
    vipRewardReceivedLvSet = {
      isSet = true,
    },
    vipRewardReceivedLvSet_deleted = {
      isSetDel = true,
      targetFieldName = "vipRewardReceivedLvSet",
    },
  },
  RecommendFriendInfo = {
    isUnRecommendFriend = {
    },
    recommendFriend = {
      isSet = true,
    },
    recommendFriend_deleted = {
      isSetDel = true,
      targetFieldName = "recommendFriend",
    },
    removeRecommendFriend = {
      isSet = true,
    },
    removeRecommendFriend_deleted = {
      isSetDel = true,
      targetFieldName = "removeRecommendFriend",
    },
  },
  RecommendMatchType = {
    alreadySettled = {
    },
    conditionGroup = {
      isMsg = true,
    },
    expireTimeMs = {
    },
    recommendId = {
    },
    refreshInfo = {
      isMsg = true,
    },
    status = {
    },
    tasks = {
      isMap = true,
      keyName = "taskId",
    },
    tasks_deleted = {
      isMapDel = true,
      targetFieldName = "tasks",
    },
  },
  RecommendMatchTypeTask = {
    condition = {
      isMsg = true,
    },
    status = {
    },
    taskId = {
    },
  },
  RecommendRefreshInfo = {
    completedCount = {
    },
    refreshedCount = {
    },
    refreshTimeMs = {
    },
  },
  RecruiteEntry = {
    charName = {
    },
    openid = {
    },
    peerLoginDays = {
    },
    peerLoginTime = {
    },
    picUrl = {
    },
    platId = {
    },
    platName = {
    },
    selfLoginReward = {
    },
    selfSyncTime = {
    },
    signingTime = {
    },
    type = {
    },
    uid = {
    },
  },
  RecruiteRaffleInfo = {
    drawnTs = {
    },
    hasAddress = {
    },
    id = {
    },
    rewardId = {
    },
  },
  RecScenePackageInfo = {
    dailyRecCnt = {
    },
    lastRecTimeMs = {
    },
    weeklyRecCnt = {
    },
  },
  RedEnvelopeRainActInfo = {
    actId = {
    },
    actReceivedCnt = {
    },
    curDayReceivedCnt = {
      isMsg = true,
    },
    curTurnReceivedCnt = {
      isMsg = true,
    },
    isEverShowedRedPoint = {
    },
    openedCnt = {
    },
    shareReceivedInfo = {
      isMsg = true,
    },
  },
  RedEnvelopeRainActivities = {
    activities = {
      isMap = true,
      keyName = "actId",
    },
    activities_deleted = {
      isMapDel = true,
      targetFieldName = "activities",
    },
  },
  RedEnvelopeRainReceivedCnt = {
    cnt = {
    },
    id = {
    },
  },
  RedEnvelopRainShareInfo = {
    clickExtraOpp = {
    },
    curDayExtraReceivedCnt = {
    },
    curTurnExtraReceivedCnt = {
    },
    dayId = {
    },
    extraOpenedCnt = {
    },
    extraReceivedCnt = {
    },
    shareExtraOpp = {
    },
  },
  RedPacket = {
    createTimeMs = {
    },
    packetId = {
    },
    position = {
      isMsg = true,
    },
    receiveCount = {
    },
    senderName = {
    },
    senderUid = {
    },
    totalCount = {
    },
    uuid = {
    },
  },
  RedPacketActivity = {
    isSentListDirty = {
    },
    recvCount = {
    },
    recvList = {
      isMap = true,
      keyName = "packetUuid",
    },
    recvList_deleted = {
      isMapDel = true,
      targetFieldName = "recvList",
    },
    sentList = {
      isMap = true,
      keyName = "packetUuid",
    },
    sentList_deleted = {
      isMapDel = true,
      targetFieldName = "sentList",
    },
  },
  RedPacketRecvDetail = {
    packetId = {
    },
    packetUuid = {
    },
    placeId = {
    },
    placeType = {
    },
    replyMsg = {
    },
    rewards = {
      isMap = true,
      keyName = "id",
    },
    rewards_deleted = {
      isMapDel = true,
      targetFieldName = "rewards",
    },
    senderUid = {
    },
    ts = {
    },
  },
  RedPacketSentDetail = {
    details = {
      isMap = true,
      keyName = "id",
    },
    details_deleted = {
      isMapDel = true,
      targetFieldName = "details",
    },
    isDirty = {
    },
    packetId = {
    },
    packetUuid = {
    },
    recvCount = {
    },
    senderUid = {
    },
    totalCount = {
    },
    ts = {
    },
  },
  RedPacketSentRecvDetail = {
    id = {
    },
    placeId = {
    },
    placeType = {
    },
    receiverUid = {
    },
    replyMsg = {
    },
    rewards = {
      isMap = true,
      keyName = "id",
    },
    rewards_deleted = {
      isMapDel = true,
      targetFieldName = "rewards",
    },
    ts = {
    },
  },
  RelationInfo = {
    modRelationInfo = {
      isMap = true,
      keyName = "uid",
    },
    modRelationInfo_deleted = {
      isMapDel = true,
      targetFieldName = "modRelationInfo",
    },
    receiveApplyInfo = {
      isMap = true,
      keyName = "uid",
    },
    receiveApplyInfo_deleted = {
      isMapDel = true,
      targetFieldName = "receiveApplyInfo",
    },
    relationType = {
    },
    removeInfo = {
      isMap = true,
      keyName = "uid",
    },
    removeInfo_deleted = {
      isMapDel = true,
      targetFieldName = "removeInfo",
    },
    sendApplyInfo = {
      isMap = true,
      keyName = "uid",
    },
    sendApplyInfo_deleted = {
      isMapDel = true,
      targetFieldName = "sendApplyInfo",
    },
  },
  RelationMapInfo = {
    allFriendInteractAttr = {
      isMsg = true,
    },
    allIntimateRelationAttr = {
      isMsg = true,
    },
    dailyIntimacy = {
      isMap = true,
      keyName = "uid",
    },
    dailyIntimacy_deleted = {
      isMapDel = true,
      targetFieldName = "dailyIntimacy",
    },
    intimateGuideAttr = {
      isMsg = true,
    },
    intimateNoticeAttr = {
      isMsg = true,
    },
    inviteeInfos = {
      isMap = true,
      keyName = "inviteeUid",
    },
    inviteeInfos_deleted = {
      isMapDel = true,
      targetFieldName = "inviteeInfos",
    },
    luckyFriendAttr = {
      isMsg = true,
    },
    newFollowerCount = {
    },
    platFriendUid = {
      isAry = true,
    },
    platFriendUid_is_cleared = {
      isAryClear = true,
      targetFieldName = "platFriendUid",
    },
    recallInviteeInfos = {
      isMap = true,
      keyName = "inviteeUid",
    },
    recallInviteeInfos_deleted = {
      isMapDel = true,
      targetFieldName = "recallInviteeInfos",
    },
    recentIntimacy = {
      isMap = true,
      keyName = "uid",
    },
    recentIntimacy_deleted = {
      isMapDel = true,
      targetFieldName = "recentIntimacy",
    },
    relationInfo = {
      isMap = true,
      keyName = "relationType",
    },
    relationInfo_deleted = {
      isMapDel = true,
      targetFieldName = "relationInfo",
    },
    sendGoldCoinUid = {
      isAry = true,
    },
    sendGoldCoinUid_is_cleared = {
      isAryClear = true,
      targetFieldName = "sendGoldCoinUid",
    },
  },
  RelationPlayerNameData = {
    friendNickname = {
    },
    name = {
    },
    remarkName = {
    },
  },
  RestaurantThemedFood = {
    foodId = {
    },
    num = {
    },
  },
  RestaurantThemedNpc = {
    collectAllEpochMillis = {
    },
    foodIdList = {
      isMap = true,
      keyName = "foodId",
    },
    foodIdList_deleted = {
      isMapDel = true,
      targetFieldName = "foodIdList",
    },
    npcId = {
    },
    servingNum = {
    },
  },
  RestaurantThemedReceiveHistory = {
    foodId = {
    },
    uid = {
      isSet = true,
    },
    uid_deleted = {
      isSetDel = true,
      targetFieldName = "uid",
    },
  },
  RestaurantThemedReceiveRecord = {
    epochMillis = {
    },
    foodId = {
    },
    npcId = {
    },
    sharerUid = {
    },
  },
  RestaurantThemedShareRecord = {
    epochMillis = {
    },
    foodId = {
    },
    id = {
    },
    npcId = {
    },
    receiverUid = {
    },
  },
  ReturnActivity = {
    activeFarmBuffIds = {
      isAry = true,
    },
    activeFarmBuffIds_is_cleared = {
      isAryClear = true,
      targetFieldName = "activeFarmBuffIds",
    },
    activeLevel = {
    },
    beginDayOffset = {
    },
    beginTime = {
    },
    calendarRewardReceived = {
    },
    chargeGiftActivityId = {
    },
    chargeGiftTicketGot = {
    },
    chargeSignInActivityId = {
    },
    chargeSignInTicketGot = {
    },
    customGift = {
    },
    dailyReward = {
      isMsg = true,
    },
    dayLoginCount = {
    },
    endTime = {
    },
    exclusiveNewsRecommend = {
      isAry = true,
    },
    exclusiveNewsRecommend_is_cleared = {
      isAryClear = true,
      targetFieldName = "exclusiveNewsRecommend",
    },
    firstGameActivityId = {
    },
    InterfaceVersion = {
    },
    jumpToSignInAfterRound = {
    },
    loginFreeActivityId = {
    },
    lossDays = {
    },
    optionalRewardIndex = {
    },
    privilege = {
      isMsg = true,
    },
    privilegeActivityId = {
    },
    privilegeEndTime = {
    },
    returnBookActivityId = {
    },
    rewardOptionalTask = {
      isMap = true,
      keyName = "taskId",
    },
    rewardOptionalTask_deleted = {
      isMapDel = true,
      targetFieldName = "rewardOptionalTask",
    },
    signInActivityId = {
    },
    startMatchGiftId = {
    },
    taskActivityId = {
    },
    userBaitIndex = {
    },
    userConfId = {
    },
  },
  ReturnActivityDailyReward = {
    activityId = {
    },
    checkInCount = {
    },
    exclusiveRecommend = {
      isAry = true,
    },
    exclusiveRecommend_is_cleared = {
      isAryClear = true,
      targetFieldName = "exclusiveRecommend",
    },
    exclusiveRecommendExcept = {
      isSet = true,
    },
    exclusiveRecommendExcept_deleted = {
      isSetDel = true,
      targetFieldName = "exclusiveRecommendExcept",
    },
    firstReward = {
    },
    taskGroupIndex = {
    },
  },
  ReturnActivityPrivilege = {
    dailyDoubleRewardCnt = {
    },
    fireGotNumWeekly = {
    },
    lossRewardType = {
    },
  },
  ReturnActivityRewardOptionalTask = {
    itemId = {
      isAry = true,
    },
    itemId_is_cleared = {
      isAryClear = true,
      targetFieldName = "itemId",
    },
    itemNum = {
      isAry = true,
    },
    itemNum_is_cleared = {
      isAryClear = true,
      targetFieldName = "itemNum",
    },
    taskId = {
    },
  },
  ReturningActInfo = {
    activityId = {
    },
    endTime = {
    },
    startTime = {
    },
  },
  ReturningInfoDb = {
    returnActivity = {
      isMsg = true,
    },
    returningTs = {
    },
  },
  ReturnTaskInfo = {
    lastEnterFarmTime = {
    },
    lastGameTime = {
    },
  },
  RewardCompensateInfo = {
    taskToType = {
      isMap = true,
      keyName = "id",
    },
    taskToType_deleted = {
      isMapDel = true,
      targetFieldName = "taskToType",
    },
    typeToTasks = {
      isMap = true,
      keyName = "type",
    },
    typeToTasks_deleted = {
      isMapDel = true,
      targetFieldName = "typeToTasks",
    },
  },
  RewardCompensateTask = {
    id = {
    },
    type = {
    },
  },
  RewardCompensateTaskStatus = {
    deleteMs = {
    },
    expireMs = {
    },
    htasks = {
      isSet = true,
    },
    htasks_deleted = {
      isSetDel = true,
      targetFieldName = "htasks",
    },
    rewards = {
      isMap = true,
      keyName = "id",
    },
    rewards_deleted = {
      isMapDel = true,
      targetFieldName = "rewards",
    },
    rewardTasks = {
      isSet = true,
    },
    rewardTasks_deleted = {
      isSetDel = true,
      targetFieldName = "rewardTasks",
    },
    tasks = {
      isSet = true,
    },
    tasks_deleted = {
      isSetDel = true,
      targetFieldName = "tasks",
    },
    type = {
    },
  },
  RewardComponent = {
    auto = {
    },
    costItem = {
      isMap = true,
      keyName = "itemId",
    },
    costItem_deleted = {
      isMapDel = true,
      targetFieldName = "costItem",
    },
    rewardId = {
    },
    status = {
    },
  },
  RewardCostItem = {
    itemId = {
    },
    num = {
    },
  },
  RewardItemInfo = {
    expireTimeMs = {
    },
    expireType = {
    },
    id = {
    },
    num = {
    },
  },
  RewardNtfInfo = {
    cnt = {
    },
    id = {
    },
    rewards = {
      isMap = true,
      keyName = "id",
    },
    rewards_deleted = {
      isMapDel = true,
      targetFieldName = "rewards",
    },
  },
  RewardStatusInfo = {
    rewardId = {
    },
    rewardStatus = {
    },
  },
  RoguelikeCommonProp = {
    propsID = {
    },
    stackNumber = {
    },
  },
  RoguelikeCustomQQMusicInfo = {
    musicId = {
    },
    songId = {
    },
  },
  RoguelikeEndLessSaveInfo = {
    battleID = {
    },
    battleType = {
    },
    damage = {
    },
    ex = {
    },
    gold = {
    },
    helpCount = {
    },
    killCount = {
    },
    lastLevelNumber = {
    },
    lastTaskLevel = {
    },
    matchID = {
    },
    nowHp = {
    },
    propsList = {
      isMap = true,
      keyName = "propsID",
    },
    propsList_deleted = {
      isMapDel = true,
      targetFieldName = "propsList",
    },
    roleLevel = {
    },
    specialBuffList = {
      isMap = true,
      keyName = "buffID",
    },
    specialBuffList_deleted = {
      isMapDel = true,
      targetFieldName = "specialBuffList",
    },
    sumGold = {
    },
  },
  RoguelikeEndlessSavePropsInfo = {
    propsCount = {
    },
    propsID = {
    },
  },
  RoguelikeEndlessSaveSpecialBuffInfo = {
    buffID = {
    },
    stackCount = {
    },
  },
  RoguelikeExtraInfo = {
    endLessSaveRows = {
      isMsg = true,
    },
    markData = {
      isMsg = true,
    },
    monsterRows = {
      isMap = true,
      keyName = "monsterID",
    },
    monsterRows_deleted = {
      isMapDel = true,
      targetFieldName = "monsterRows",
    },
    passLevelRows = {
      isMsg = true,
    },
    propRows = {
      isMap = true,
      keyName = "propID",
    },
    propRows_deleted = {
      isMapDel = true,
      targetFieldName = "propRows",
    },
    weaponRows = {
      isMap = true,
      keyName = "weaponID",
    },
    weaponRows_deleted = {
      isMapDel = true,
      targetFieldName = "weaponRows",
    },
  },
  RoguelikeGetPropsData = {
    getNumber = {
    },
    propsID = {
    },
  },
  RoguelikeInfo = {
    endlessFirstWeaponId = {
    },
    endlessSecondWeaponId = {
    },
    extraInfo = {
      isMsg = true,
    },
    rankData = {
      isMsg = true,
    },
    seasonData = {
      isMsg = true,
    },
    skillId = {
    },
    talentData = {
      isMsg = true,
    },
    taskData = {
      isMsg = true,
    },
    weaponId = {
    },
  },
  RoguelikeKillMonsterData = {
    killNumber = {
    },
    monsterID = {
    },
  },
  RoguelikeMark = {
    baseEffectId = {
    },
    breakThroughLevel = {
    },
    entryInfo = {
      isMap = true,
      keyName = "entryId",
    },
    entryInfo_deleted = {
      isMapDel = true,
      targetFieldName = "entryInfo",
    },
    id = {
    },
    isLock = {
    },
    level = {
    },
    rarity = {
    },
    shapeId = {
    },
    specialEffectId = {
    },
  },
  RoguelikeMarkBoard = {
    boardName = {
    },
    id = {
    },
    marksInBoard = {
      isMap = true,
      keyName = "markId",
    },
    marksInBoard_deleted = {
      isMapDel = true,
      targetFieldName = "marksInBoard",
    },
    shapeId = {
    },
  },
  RoguelikeMarkData = {
    currMarkBoardId = {
    },
    DropPreference = {
    },
    markBoards = {
      isMap = true,
      keyName = "id",
    },
    markBoards_deleted = {
      isMapDel = true,
      targetFieldName = "markBoards",
    },
    markBoardTemplates = {
      isSet = true,
    },
    markBoardTemplates_deleted = {
      isSetDel = true,
      targetFieldName = "markBoardTemplates",
    },
    marks = {
      isMap = true,
      keyName = "id",
    },
    marks_deleted = {
      isMapDel = true,
      targetFieldName = "marks",
    },
    totalMaterials = {
    },
  },
  RoguelikeMarkEntry = {
    entryId = {
    },
    entryLevel = {
    },
    isMainEntry = {
    },
  },
  RoguelikeMarkInBoardInfo = {
    columnIndex = {
    },
    markId = {
    },
    rotateType = {
    },
    rowIndex = {
    },
  },
  RoguelikeMonsterInfo = {
    isUnlock = {
    },
    killTimes = {
    },
    monsterID = {
    },
    unlockTimeStamp = {
    },
  },
  RoguelikePassLevelInfo = {
    easyPassNumber = {
    },
    endlessMaxLevelNumber = {
    },
    hardPassNumber = {
    },
    isNewRecord = {
    },
    lastEndlessPassLevel = {
    },
    normalPassNumber = {
    },
  },
  RoguelikePropInfo = {
    acquireTimes = {
    },
    isUnlock = {
    },
    propID = {
    },
    unlockProgress = {
    },
    unlockThreshold = {
    },
    unlockTimeStamp = {
    },
  },
  RoguelikeRankData = {
    records = {
      isMap = true,
      keyName = "settlementTimeMs",
    },
    records_deleted = {
      isMapDel = true,
      targetFieldName = "records",
    },
    seasonId = {
    },
  },
  RoguelikeRankRecord = {
    passLevels = {
    },
    settlementTimeMs = {
    },
  },
  RoguelikeSeasonData = {
    curMilestoneScore = {
    },
    dailyTaskData = {
      isMsg = true,
    },
    hadDrawRewardLevels = {
      isSet = true,
    },
    hadDrawRewardLevels_deleted = {
      isSetDel = true,
      targetFieldName = "hadDrawRewardLevels",
    },
    seasonId = {
    },
    seasonTaskData = {
      isMsg = true,
    },
    weekTaskData = {
      isMsg = true,
    },
  },
  RoguelikeTalentData = {
    costPropInfo = {
      isMsg = true,
    },
    unlockTalentIds = {
      isSet = true,
    },
    unlockTalentIds_deleted = {
      isSetDel = true,
      targetFieldName = "unlockTalentIds",
    },
  },
  RoguelikeTask = {
    id = {
    },
    progress = {
    },
    state = {
    },
  },
  RoguelikeTaskData = {
    manuallyRefreshTimes = {
    },
    preRefreshTime = {
    },
    refreshTime = {
    },
    tasks = {
      isMap = true,
      keyName = "id",
    },
    tasks_deleted = {
      isMapDel = true,
      targetFieldName = "tasks",
    },
  },
  RoguelikeUnlockTalent = {
    talentId = {
    },
  },
  RoguelikeUserDBInfo = {
    qqMusicCustomSongs = {
      isMap = true,
      keyName = "musicId",
    },
    qqMusicCustomSongs_deleted = {
      isMapDel = true,
      targetFieldName = "qqMusicCustomSongs",
    },
    qqMusicDefaultSongBlackList = {
      isMap = true,
      keyName = "musicId",
    },
    qqMusicDefaultSongBlackList_deleted = {
      isMapDel = true,
      targetFieldName = "qqMusicDefaultSongBlackList",
    },
  },
  RoguelikeUseSkillData = {
    skillID = {
    },
    userNumber = {
    },
  },
  RoguelikeWeaponDamageData = {
    damageNumber = {
    },
    damageType = {
    },
  },
  RoguelikeWeaponInfo = {
    costGold = {
    },
    criticalNumber = {
    },
    damageData = {
      isMap = true,
      keyName = "damageType",
    },
    damageData_deleted = {
      isMapDel = true,
      targetFieldName = "damageData",
    },
    gambleNumber = {
    },
    getGold = {
    },
    getProps = {
      isMap = true,
      keyName = "propsID",
    },
    getProps_deleted = {
      isMapDel = true,
      targetFieldName = "getProps",
    },
    killMonster = {
      isMap = true,
      keyName = "monsterID",
    },
    killMonster_deleted = {
      isMapDel = true,
      targetFieldName = "killMonster",
    },
    passLevel = {
    },
    takeDamage = {
    },
    useSkill = {
      isMap = true,
      keyName = "skillID",
    },
    useSkill_deleted = {
      isMapDel = true,
      targetFieldName = "useSkill",
    },
    weaponID = {
    },
    weaponReload = {
    },
    weaponShoot = {
    },
  },
  RoleChatInfo = {
    createTime = {
    },
    lastLoginTime = {
    },
    level = {
    },
    roleId = {
    },
    roleIndex = {
    },
    spWorldChatInfo = {
      isMap = true,
      keyName = "starPId",
    },
    spWorldChatInfo_deleted = {
      isMapDel = true,
      targetFieldName = "spWorldChatInfo",
    },
  },
  RoomExtraInfo = {
    curMemberNum = {
    },
    maxMemberNum = {
    },
    playId = {
    },
    roomType = {
    },
  },
  RootAttr = {
    cocUserAttr = {
      isMsg = true,
    },
    cocUserAttr_deleted = {
      isFieldDel = true,
      targetFieldName = "cocUserAttr",
    },
    userAttr = {
      isMsg = true,
    },
  },
  SafetyCheck = {
    isNeedCheckProfileBeforMatch = {
    },
    isUpdateOnline = {
    },
  },
  SceneCollection = {
    id = {
    },
    num = {
    },
  },
  SceneInfo = {
    interactAction = {
      isMsg = true,
    },
    mapid = {
    },
    sceneid = {
    },
  },
  SceneInteractActionInfo = {
    actionId = {
    },
    objectUid = {
    },
    status = {
    },
    timeoutTs = {
    },
    type = {
    },
  },
  ScenePackageInfo = {
    buyedCommodityId = {
    },
    buyTimeMs = {
    },
    conditionGroup = {
      isMsg = true,
    },
    expTag = {
    },
    id = {
    },
    pushTimeMs = {
    },
    recid = {
    },
  },
  ScoreGuideData = {
    activityStatus = {
    },
    conditionGroup = {
      isMsg = true,
    },
    nextGuideTime = {
    },
    totalGuideTimes = {
    },
  },
  ScratchOffTicketInfo = {
    baseRewards = {
      isMap = true,
      keyName = "id",
    },
    baseRewards_deleted = {
      isMapDel = true,
      targetFieldName = "baseRewards",
    },
    higherRewards = {
      isMap = true,
      keyName = "id",
    },
    higherRewards_deleted = {
      isMapDel = true,
      targetFieldName = "higherRewards",
    },
    id = {
    },
    isGetBaseRewards = {
    },
    isGetHigherRewards = {
    },
  },
  ScratchOffTicketsActivity = {
    baseRewardRandomCount = {
    },
    higherRewardRandomCount = {
    },
    isOpenHigher = {
    },
    lastScratchTimeMs = {
    },
    randomRecord = {
      isMap = true,
      keyName = "giftId",
    },
    randomRecord_deleted = {
      isMapDel = true,
      targetFieldName = "randomRecord",
    },
    ticketCount = {
    },
    ticketList = {
      isMap = true,
      keyName = "id",
    },
    ticketList_deleted = {
      isMapDel = true,
      targetFieldName = "ticketList",
    },
  },
  SeasonFashion = {
    battleData = {
      isMsg = true,
    },
    equipBook = {
      isMap = true,
      keyName = "itemType",
    },
    equipBook_deleted = {
      isMapDel = true,
      targetFieldName = "equipBook",
    },
    fashionValue = {
    },
    seasonId = {
    },
  },
  SeasonFashionBattleData = {
    battleDataDetail = {
      isMap = true,
      keyName = "dataId",
    },
    battleDataDetail_deleted = {
      isMapDel = true,
      targetFieldName = "battleDataDetail",
    },
    battleMetaDataDetail = {
      isMap = true,
      keyName = "dataId",
    },
    battleMetaDataDetail_deleted = {
      isMapDel = true,
      targetFieldName = "battleMetaDataDetail",
    },
    modeCupsData = {
      isMap = true,
      keyName = "id",
    },
    modeCupsData_deleted = {
      isMapDel = true,
      targetFieldName = "modeCupsData",
    },
    playerGameTimes = {
      isMap = true,
      keyName = "type",
    },
    playerGameTimes_deleted = {
      isMapDel = true,
      targetFieldName = "playerGameTimes",
    },
    qualifyingMaxDegreeInfo = {
      isMap = true,
      keyName = "qualifyType",
    },
    qualifyingMaxDegreeInfo_deleted = {
      isMapDel = true,
      targetFieldName = "qualifyingMaxDegreeInfo",
    },
  },
  SeasonFashionBattleDataDetail = {
    dataId = {
    },
    dataValue = {
    },
  },
  SeasonFashionEquipBook = {
    itemId = {
      isSet = true,
    },
    itemId_deleted = {
      isSetDel = true,
      targetFieldName = "itemId",
    },
    itemType = {
    },
  },
  SeasonFashionQualifyMaxDegreeInfo = {
    degreeID = {
    },
    degreeStar = {
    },
    degreeType = {
    },
    qualifyingIntegral = {
    },
    qualifyType = {
    },
  },
  SeasonInfo = {
    dresses = {
      isSet = true,
    },
    dresses_deleted = {
      isSetDel = true,
      targetFieldName = "dresses",
    },
    seasonId = {
    },
  },
  SeasonMail = {
    degreeId = {
    },
    degreeTypeInt = {
    },
    oldDegreeId = {
    },
    oldDegreeTypeInt = {
    },
    oldMaxDegreeId = {
    },
    oldMaxDegreeTypeInt = {
    },
    oldSeason = {
    },
    season = {
    },
  },
  SeasonReview = {
    beginRecordSeasonId = {
    },
    seasonId = {
    },
    seasonReviewEventProgressData = {
      isMap = true,
      keyName = "eventType",
    },
    seasonReviewEventProgressData_deleted = {
      isMapDel = true,
      targetFieldName = "seasonReviewEventProgressData",
    },
    seasonReviewRedPointInfo = {
      isMap = true,
      keyName = "seasonId",
    },
    seasonReviewRedPointInfo_deleted = {
      isMapDel = true,
      targetFieldName = "seasonReviewRedPointInfo",
    },
  },
  SeasonReviewEventProgressData = {
    eventType = {
    },
    eventUpdateTime = {
    },
    eventValue = {
    },
  },
  SeasonReviewRedPointInfo = {
    seasonId = {
    },
    showRedPoint = {
    },
  },
  SecondaryPassword = {
    forceCloseTimeMs = {
    },
    isOpen = {
    },
    password = {
    },
    passwordLess = {
    },
    passwordLessEndTimeMs = {
    },
    token = {
    },
    tokenExpireTimeMs = {
    },
  },
  SelfFavourInfo = {
    favourTimeMs = {
    },
    id = {
    },
  },
  SelfMessageSlip = {
    lastSlipTimeMs = {
    },
    selfFavourSlipMap = {
      isMap = true,
      keyName = "id",
    },
    selfFavourSlipMap_deleted = {
      isMapDel = true,
      targetFieldName = "selfFavourSlipMap",
    },
    selfSlipId = {
      isSet = true,
    },
    selfSlipId_deleted = {
      isSetDel = true,
      targetFieldName = "selfSlipId",
    },
    slipRedDotMap = {
      isMap = true,
      keyName = "id",
    },
    slipRedDotMap_deleted = {
      isMapDel = true,
      targetFieldName = "slipRedDotMap",
    },
  },
  SelfTradeInfo = {
    expireTimeMs = {
    },
    id = {
    },
  },
  Seller = {
    money = {
    },
    uid = {
    },
  },
  ShareActiveActivity = {
    state = {
    },
  },
  ShareChatTime = {
    time = {
    },
    tradeId = {
    },
  },
  ShareGiftInfo = {
    shareTypeGiftInfo = {
      isMap = true,
      keyName = "type",
    },
    shareTypeGiftInfo_deleted = {
      isMapDel = true,
      targetFieldName = "shareTypeGiftInfo",
    },
  },
  ShareLimitInfo = {
    allRefreshTs = {
    },
    allRewardCount = {
    },
    allShareCount = {
    },
    dayRefreshTs = {
    },
    dayRewardCount = {
    },
    dayShareCount = {
    },
    lastRewardRefreshTs = {
    },
    lastShareRefreshTs = {
    },
    limitId = {
    },
    rewardCount = {
    },
    shareCount = {
    },
    weekRefreshTs = {
    },
    weekRewardCount = {
    },
    weekShareCount = {
    },
  },
  ShareTypeGiftInfo = {
    lastRewardRefreshTs = {
    },
    lastShareRefreshTs = {
    },
    rewardCount = {
    },
    shareCount = {
    },
    shareLimitInfo = {
      isMap = true,
      keyName = "limitId",
    },
    shareLimitInfo_deleted = {
      isMapDel = true,
      targetFieldName = "shareLimitInfo",
    },
    type = {
    },
  },
  ShocksDegreeIDInfo = {
    degreeID = {
    },
    status = {
    },
  },
  ShocksRewardInfo = {
    degreeRewardMap = {
      isMap = true,
      keyName = "degreeID",
    },
    degreeRewardMap_deleted = {
      isMapDel = true,
      targetFieldName = "degreeRewardMap",
    },
    qualifyingType = {
    },
  },
  SingleStageInfo = {
    battleId = {
    },
    blueTopicId = {
      isSet = true,
    },
    blueTopicId_deleted = {
      isSetDel = true,
      targetFieldName = "blueTopicId",
    },
    blueTopicTxt = {
      isSet = true,
    },
    blueTopicTxt_deleted = {
      isSetDel = true,
      targetFieldName = "blueTopicTxt",
    },
    fromCollectionId = {
    },
    goldTopicId = {
      isSet = true,
    },
    goldTopicId_deleted = {
      isSetDel = true,
      targetFieldName = "goldTopicId",
    },
    goldTopicTxt = {
      isSet = true,
    },
    goldTopicTxt_deleted = {
      isSetDel = true,
      targetFieldName = "goldTopicTxt",
    },
    hasStarted = {
    },
    isBestRecord = {
    },
    isSettlement = {
    },
    logicMapSource = {
    },
    mapId = {
    },
    mapSource = {
    },
    singleType = {
    },
    startTime = {
    },
  },
  SnsShareReward = {
    lastRewardTime = {
    },
    sceneType = {
    },
  },
  SpecialBattleData = {
    isOneRound = {
    },
  },
  SpecialCropInfo = {
    vileplumeInfo = {
      isMsg = true,
    },
  },
  SpecialFace = {
    dailyShowCountLastDay = {
    },
    expireTime = {
    },
    faceCount = {
    },
    faceId = {
    },
    lastShowTime = {
    },
    todayNotShowTime = {
    },
  },
  SpecialFurniture = {
    cropMachineInfos = {
      isMap = true,
      keyName = "id",
    },
    cropMachineInfos_deleted = {
      isMapDel = true,
      targetFieldName = "cropMachineInfos",
    },
  },
  SpecReward = {
    itemInfo = {
      isMap = true,
      keyName = "id",
    },
    itemInfo_deleted = {
      isMapDel = true,
      targetFieldName = "itemInfo",
    },
    status = {
    },
    type = {
    },
  },
  SpecRewardInfo = {
    info = {
      isMap = true,
      keyName = "type",
    },
    info_deleted = {
      isMapDel = true,
      targetFieldName = "info",
    },
    snsShareInfo = {
      isMap = true,
      keyName = "sceneType",
    },
    snsShareInfo_deleted = {
      isMapDel = true,
      targetFieldName = "snsShareInfo",
    },
  },
  SPEquipAddionEffect = {
    attrOpType = {
    },
    effecterType = {
    },
    effID = {
    },
    effKey = {
    },
    effType = {
    },
    effValue = {
    },
  },
  SPEquipmentPlanData = {
    planId = {
    },
    planItem = {
      isMap = true,
      keyName = "slotIndex",
    },
    planItem_deleted = {
      isMapDel = true,
      targetFieldName = "planItem",
    },
    planName = {
    },
  },
  SPEquipmentPlanItem = {
    backpackType = {
    },
    instId = {
    },
    slotIndex = {
    },
  },
  SPEquipmentPlanPlayerInfoList = {
    currPlanId = {
    },
    planList = {
      isMap = true,
      keyName = "planId",
    },
    planList_deleted = {
      isMapDel = true,
      targetFieldName = "planList",
    },
  },
  SpIconInfo = {
    cupID = {
    },
    petID = {
    },
  },
  SpringBlessingCollectionAttr = {
    blessingCard = {
      isMap = true,
      keyName = "cardId",
    },
    blessingCard_deleted = {
      isMapDel = true,
      targetFieldName = "blessingCard",
    },
    dayIndex = {
    },
    getCDKeyCntAttr = {
      isMap = true,
      keyName = "sponsorId",
    },
    getCDKeyCntAttr_deleted = {
      isMapDel = true,
      targetFieldName = "getCDKeyCntAttr",
    },
    getTaskRewardTimeMs = {
    },
    giveCardCnt = {
    },
    refreshTimeMs = {
    },
    shareTimeMs = {
    },
    sponsorLotteryCnt = {
    },
    stallLotteryCnt = {
    },
  },
  SpringBlessingCollectionCardInfo = {
    cardId = {
    },
    rewardId = {
    },
    sourceId = {
    },
    sourceType = {
    },
    status = {
    },
  },
  SpringBlessingGetCdKeyAttr = {
    getCDKeyCnt = {
    },
    lotteryCardCnt = {
    },
    sponsorId = {
    },
  },
  SpringPrayActivityAttr = {
    todayPray = {
    },
    useLuckyBuff = {
    },
  },
  SpringSlipAssistRecord = {
    assistTimeMs = {
    },
    assistType = {
    },
    assistUid = {
    },
    beAssistUid = {
    },
    recordId = {
    },
  },
  SpringSlipData = {
    assistRecord = {
      isMap = true,
      keyName = "recordId",
    },
    assistRecord_deleted = {
      isMapDel = true,
      targetFieldName = "assistRecord",
    },
    beAssistedRecord = {
      isMap = true,
      keyName = "recordId",
    },
    beAssistedRecord_deleted = {
      isMapDel = true,
      targetFieldName = "beAssistedRecord",
    },
    giveRecord = {
      isMap = true,
      keyName = "recordId",
    },
    giveRecord_deleted = {
      isMapDel = true,
      targetFieldName = "giveRecord",
    },
    limitInfo = {
      isMap = true,
      keyName = "id",
    },
    limitInfo_deleted = {
      isMapDel = true,
      targetFieldName = "limitInfo",
    },
    normalAssistRequest = {
    },
    receiveRecord = {
      isMap = true,
      keyName = "recordId",
    },
    receiveRecord_deleted = {
      isMapDel = true,
      targetFieldName = "receiveRecord",
    },
    rewardInfo = {
      isMap = true,
      keyName = "itemId",
    },
    rewardInfo_deleted = {
      isMapDel = true,
      targetFieldName = "rewardInfo",
    },
    roundNum = {
    },
  },
  SpringSlipRewardInfo = {
    itemId = {
    },
    itemNum = {
    },
  },
  SpringSlipRewardLimit = {
    id = {
    },
    rewardNum = {
    },
  },
  SpringSlipTradeRecord = {
    giveUid = {
    },
    receiveUid = {
    },
    recordId = {
    },
    slipId = {
    },
    tradeTimeMs = {
    },
    tradeType = {
    },
  },
  SPSendMailOptionalParams = {
    optAttachments = {
      isMsg = true,
    },
    optBelongToAccount = {
    },
    optHintIds = {
      isAry = true,
    },
    optHintIds_is_cleared = {
      isAryClear = true,
      targetFieldName = "optHintIds",
    },
    optIsImportant = {
    },
    optMailArgs = {
      isMsg = true,
    },
    optNoticeTitle = {
    },
    optNoticeType = {
    },
    optSenderId = {
    },
    optSenderType = {
    },
    optSharedAttachment = {
    },
  },
  SpWorldChatInfo = {
    createTime = {
    },
    guildChatGroupKey = {
      isMsg = true,
    },
    starPId = {
    },
    starPWorldName = {
    },
    worldChatGroupKey = {
      isMsg = true,
    },
  },
  SquadActivityHistoryData = {
    activityId = {
    },
    lastTimestamp = {
    },
    memberUidList = {
      isSet = true,
    },
    memberUidList_deleted = {
      isSetDel = true,
      targetFieldName = "memberUidList",
    },
    playerInvited = {
      isSet = true,
    },
    playerInvited_deleted = {
      isSetDel = true,
      targetFieldName = "playerInvited",
    },
  },
  SquadActivityHistoryPlusData = {
    activityId = {
    },
    groupType = {
    },
    lastTimestamp = {
    },
    memberUidList = {
      isSet = true,
    },
    memberUidList_deleted = {
      isSetDel = true,
      targetFieldName = "memberUidList",
    },
    playerInvited = {
      isSet = true,
    },
    playerInvited_deleted = {
      isSetDel = true,
      targetFieldName = "playerInvited",
    },
  },
  SquadItemCntInfo = {
    cnt = {
    },
    itemId = {
    },
  },
  SquadItemInfo = {
    dug = {
    },
    dugBy = {
    },
    dugTimestamp = {
    },
    index = {
    },
    itemId = {
    },
  },
  SquadMember = {
    areaId = {
    },
    dressUpInfos = {
      isSet = true,
    },
    dressUpInfos_deleted = {
      isSetDel = true,
      targetFieldName = "dressUpInfos",
    },
    gender = {
    },
    joinTimeMs = {
    },
    lastUpdateTimeMs = {
    },
    level = {
    },
    memberSquadData = {
      isMsg = true,
    },
    nickname = {
    },
    openid = {
    },
    platId = {
    },
    point = {
    },
    profile = {
    },
    uid = {
    },
  },
  StarPAbility = {
    cd = {
    },
    extendcount = {
    },
    hash = {
    },
    id = {
    },
    instigator = {
    },
    instigatorinfo = {
    },
    ownerinfo = {
    },
    slotId = {
    },
  },
  StarPAccountBriefData = {
    commonInfo = {
      isMap = true,
      keyName = "k",
    },
    commonInfo_deleted = {
      isMapDel = true,
      targetFieldName = "commonInfo",
    },
    finishGuideTaskID = {
    },
    petTeamFirstPosInfo = {
      isMsg = true,
    },
  },
  StarPAccountData = {
    achievementData = {
      isMsg = true,
    },
    backPackIds = {
      isMap = true,
      keyName = "type",
    },
    backPackIds_deleted = {
      isMapDel = true,
      targetFieldName = "backPackIds",
    },
    backpackSizeInfo = {
      isMap = true,
      keyName = "backpackType",
    },
    backpackSizeInfo_deleted = {
      isMapDel = true,
      targetFieldName = "backpackSizeInfo",
    },
  },
  StarPAccountPower = {
    curPower = {
    },
    lastRefreshTime = {
    },
  },
  StarPAchievementData = {
    achievementScore = {
    },
    achievementTrophy = {
      isAry = true,
    },
    achievementTrophy_is_cleared = {
      isAryClear = true,
      targetFieldName = "achievementTrophy",
    },
    adventureLevel = {
    },
    isFinishAnyAchievement = {
    },
    isHideAchievementProfile = {
    },
    recentAchievements = {
      isMap = true,
      keyName = "id",
    },
    recentAchievements_deleted = {
      isMapDel = true,
      targetFieldName = "recentAchievements",
    },
  },
  StarPAdventureBaseInfo = {
    gachaCoinNum = {
    },
    getDayRewardTime = {
    },
    shopCoinNum = {
    },
  },
  StarPAIInfo = {
    baseWorkSpeed = {
    },
    behaviorType = {
      isAry = true,
    },
    behaviorType_is_cleared = {
      isAryClear = true,
      targetFieldName = "behaviorType",
    },
    bIsInPastureWork = {
    },
    carriedItemId = {
    },
    carryNum = {
    },
    closedSOCAIBehaviorTypeTable = {
      isMap = true,
      keyName = "behaviorType",
    },
    closedSOCAIBehaviorTypeTable_deleted = {
      isMapDel = true,
      targetFieldName = "closedSOCAIBehaviorTypeTable",
    },
    dispatchInfo = {
      isMsg = true,
    },
    hag = {
    },
    hp = {
    },
    itemInstId = {
    },
    maxHag = {
    },
    maxHp = {
    },
    maxSan = {
    },
    monsterHeight = {
    },
    monsterId = {
    },
    monsterRadius = {
    },
    ownerPlayerUid = {
    },
    pastureBuildingID = {
    },
    pos = {
      isMsg = true,
    },
    san = {
    },
    sanDecRate = {
    },
    satietyDesRate = {
    },
    strollSpeed = {
    },
    uid = {
    },
  },
  StarPAllPlayerContactRecord = {
    records = {
      isMap = true,
      keyName = "id",
    },
    records_deleted = {
      isMapDel = true,
      targetFieldName = "records",
    },
    uid = {
    },
  },
  StarPAllStatueData = {
    statueDatas = {
      isMap = true,
      keyName = "uid",
    },
    statueDatas_deleted = {
      isMapDel = true,
      targetFieldName = "statueDatas",
    },
  },
  StarPApply = {
    applyTime = {
    },
    roleId = {
    },
    starPWorldId = {
    },
    uid = {
    },
  },
  StarPAssistMaterialInfo = {
    materialId = {
    },
    materialNumLimit = {
    },
    userInfo = {
      isMap = true,
      keyName = "uid",
    },
    userInfo_deleted = {
      isMapDel = true,
      targetFieldName = "userInfo",
    },
  },
  StarPAssistMaterialSimpleInfo = {
    materialId = {
    },
    materialNum = {
    },
    materialNumLimit = {
    },
  },
  StarPAssistOrderGeneralDBInfo = {
    assistMaterialInfo = {
      isMap = true,
      keyName = "materialId",
    },
    assistMaterialInfo_deleted = {
      isMapDel = true,
      targetFieldName = "assistMaterialInfo",
    },
  },
  StarPAssistOrderInfo = {
    assistLikedUids = {
      isSet = true,
    },
    assistLikedUids_deleted = {
      isSetDel = true,
      targetFieldName = "assistLikedUids",
    },
    assistMaterialInfo = {
      isMap = true,
      keyName = "materialId",
    },
    assistMaterialInfo_deleted = {
      isMapDel = true,
      targetFieldName = "assistMaterialInfo",
    },
    orderId = {
    },
    orderStatus = {
    },
    orderSubmitMs = {
    },
    uid = {
    },
  },
  StarPAssistOrderSimpleInfo = {
    assistMaterialInfo = {
      isMap = true,
      keyName = "materialId",
    },
    assistMaterialInfo_deleted = {
      isMapDel = true,
      targetFieldName = "assistMaterialInfo",
    },
    orderId = {
    },
    orderSubmitMs = {
    },
  },
  StarPAssistUserDetailInfo = {
    assistMs = {
    },
    assistNum = {
    },
    status = {
    },
  },
  StarPAssistUserInfo = {
    detailInfo = {
      isMap = true,
      keyName = "assistMs",
    },
    detailInfo_deleted = {
      isMapDel = true,
      targetFieldName = "detailInfo",
    },
    uid = {
    },
  },
  StarPAssitItemInfo = {
    assistNum = {
    },
    itemId = {
    },
  },
  StarPAttr = {
    applyBanInfo = {
      isMap = true,
      keyName = "uid",
    },
    applyBanInfo_deleted = {
      isMapDel = true,
      targetFieldName = "applyBanInfo",
    },
    guildInfo = {
      isMsg = true,
    },
    invitedRecodeList = {
      isMap = true,
      keyName = "inviteeUid",
    },
    invitedRecodeList_deleted = {
      isMapDel = true,
      targetFieldName = "invitedRecodeList",
    },
    lastLostAdminUID = {
    },
    prepareOccupy = {
      isMap = true,
      keyName = "k",
    },
    prepareOccupy_deleted = {
      isMapDel = true,
      targetFieldName = "prepareOccupy",
    },
    starPApply = {
      isMap = true,
      keyName = "starPWorldId",
    },
    starPApply_deleted = {
      isMapDel = true,
      targetFieldName = "starPApply",
    },
    starPBanInfo = {
      isMap = true,
      keyName = "uid",
    },
    starPBanInfo_deleted = {
      isMapDel = true,
      targetFieldName = "starPBanInfo",
    },
    starPBasicInfo = {
      isMsg = true,
    },
    starPDelInfo = {
      isMap = true,
      keyName = "uid",
    },
    starPDelInfo_deleted = {
      isMapDel = true,
      targetFieldName = "starPDelInfo",
    },
    starPDsInfo = {
      isMsg = true,
    },
    starPMemberInfo = {
      isMsg = true,
    },
    starPOwnerInfo = {
      isMsg = true,
    },
    starPPreDelInfo = {
      isMap = true,
      keyName = "uid",
    },
    starPPreDelInfo_deleted = {
      isMapDel = true,
      targetFieldName = "starPPreDelInfo",
    },
    starPStarInfo = {
      isMsg = true,
    },
    starPStatisticsInfo = {
      isMsg = true,
    },
    starPUidMemberInfo = {
      isMsg = true,
    },
    totalInvitedCount = {
    },
    visitorMemberInfo = {
      isMsg = true,
    },
  },
  StarPAwardInfo = {
    isActivated = {
    },
    level = {
    },
  },
  StarPBackpack = {
    backpackMaxSize = {
    },
    backpackType = {
    },
    ownerSpUid = {
    },
    ownerUid = {
    },
    spuidForKey = {
    },
  },
  StarPBackPackId = {
    backPackId = {
    },
    type = {
    },
  },
  StarpBackpackSizeAdd = {
    sizePairs = {
      isMap = true,
      keyName = "subKey",
    },
    sizePairs_deleted = {
      isMapDel = true,
      targetFieldName = "sizePairs",
    },
    systemType = {
    },
  },
  StarpBackpackSizeInfo = {
    backpackType = {
    },
    systemAdd = {
      isMap = true,
      keyName = "systemType",
    },
    systemAdd_deleted = {
      isMapDel = true,
      targetFieldName = "systemAdd",
    },
  },
  StarpBackpackSizePair = {
    addSize = {
    },
    subKey = {
    },
  },
  StarPBanInfo = {
    banExpTime = {
    },
    createTime = {
    },
    uid = {
    },
  },
  StarPBaseGroupApplicationData = {
    baseGroupApplicationData = {
      isMsg = true,
    },
    baseGroupApplicationType = {
    },
  },
  StarPBaseGroupApplicationDataUnion = {
    groupApplication = {
      isMsg = true,
    },
    guildApplication = {
      isMsg = true,
    },
  },
  StarPBaseGroupApplicationUnion = {
    groupApplicationList = {
      isMsg = true,
    },
    guildApplicationList = {
      isMsg = true,
    },
  },
  StarPBaseGroupDataInfo = {
    baseGroupData = {
      isMsg = true,
    },
    baseGroupType = {
    },
  },
  StarPBaseGroupDataInfoUnion = {
    groupData = {
      isMsg = true,
    },
    guildData = {
      isMsg = true,
    },
  },
  StarPBaseGroupInvitationData = {
    baseGroupInvitatioData = {
      isMsg = true,
    },
    baseGroupInvitationType = {
    },
  },
  StarPBaseGroupInvitationDataUnion = {
    groupInvitation = {
      isMsg = true,
    },
    guildInvitation = {
      isMsg = true,
    },
  },
  StarPBaseGroupInvitationUnion = {
    groupInvitationList = {
      isMsg = true,
    },
    guildInvitationList = {
      isMsg = true,
    },
  },
  StarPBaseGroupMemberDataInfo = {
    baseGroupMemberData = {
      isMsg = true,
    },
    baseGroupMemberType = {
    },
    billNo = {
    },
  },
  StarPBaseGroupMemberDataInfoUnion = {
    groupMemberData = {
      isMsg = true,
    },
    guildMemberData = {
      isMsg = true,
    },
  },
  StarPBaseGroupSimpleInfo = {
    baseGroupSimpleData = {
      isMsg = true,
    },
    baseGroupSimpleType = {
    },
  },
  StarPBaseGroupSimpleInfoUnion = {
    groupSimpleData = {
      isMsg = true,
    },
    guildSimpleData = {
      isMsg = true,
    },
  },
  StarPBaseInfo = {
    channelId = {
    },
    clientPakVersion = {
    },
    clientVersion = {
    },
    lastEnterStarPGroupWorldId = {
    },
    lastEnterStarPWorldId = {
    },
    lastRoleId = {
    },
    moeIsOpenLocation = {
    },
    moeLevel = {
    },
    moeLocationLat = {
    },
    moeLocationLon = {
    },
    moeNickName = {
    },
    onlineStatus = {
    },
    openId = {
    },
    platId = {
    },
  },
  StarPBasePve = {
    basePve = {
      isMap = true,
      keyName = "pWorldId",
    },
    basePve_deleted = {
      isMapDel = true,
      targetFieldName = "basePve",
    },
  },
  StarPBasicInfo = {
    avatar = {
    },
    avatarUrl = {
    },
    createRoleId = {
    },
    createTime = {
    },
    createUid = {
    },
    desc = {
    },
    hidden = {
    },
    isGrey = {
    },
    isNotFirstEnter = {
    },
    isOfficial = {
    },
    joinType = {
    },
    lastDailyRefreshTimeMs = {
    },
    lastWeekRefreshTimeMs = {
    },
    name = {
    },
    officialType = {
    },
    passwd = {
    },
    saveTime = {
    },
    starPWorldId = {
    },
    TabTestGroupId = {
    },
    tag = {
      isAry = true,
    },
    tag_is_cleared = {
      isAryClear = true,
      targetFieldName = "tag",
    },
    worldLevel = {
    },
  },
  StarPBlessDropInfo = {
    dropCount = {
    },
    dropId = {
    },
  },
  StarPBlessRecord = {
    time = {
    },
    uid = {
    },
  },
  StarPBlessStatueData = {
    blessCounts = {
    },
    blessedGuilds = {
      isSet = true,
    },
    blessedGuilds_deleted = {
      isSetDel = true,
      targetFieldName = "blessedGuilds",
    },
    specialRewards = {
      isMap = true,
      keyName = "dropId",
    },
    specialRewards_deleted = {
      isMapDel = true,
      targetFieldName = "specialRewards",
    },
    wishCounts = {
    },
  },
  StarPBreedEggsParentsHistory = {
    rateType = {
    },
    rateValue = {
      isAry = true,
    },
    rateValue_is_cleared = {
      isAryClear = true,
      targetFieldName = "rateValue",
    },
  },
  StarPBreedEggsParentsHistoryInfo = {
    historys = {
      isMap = true,
      keyName = "rateType",
    },
    historys_deleted = {
      isMapDel = true,
      targetFieldName = "historys",
    },
  },
  StarPBuff = {
    extendcount = {
    },
    id = {
    },
    instigatorinfo = {
    },
    layer = {
    },
    ownerinfo = {
    },
    remaintime = {
    },
  },
  StarPBuildInteractData = {
    buildingId = {
    },
    isInteracted = {
    },
  },
  StarPCardDetail = {
    group = {
      isMsg = true,
    },
    guild = {
      isMsg = true,
    },
    petTrade = {
      isMsg = true,
    },
    world = {
      isMsg = true,
    },
  },
  StarPCardDetailGroup = {
    desc = {
    },
    iconId = {
    },
    id = {
    },
    joinType = {
    },
    level = {
    },
    memberNum = {
    },
    name = {
    },
  },
  StarPCardDetailGuild = {
    id = {
    },
    leaderUid = {
    },
    memberNum = {
    },
    name = {
    },
    starpId = {
    },
  },
  StarPCardDetailPetTrade = {
    tradeNotes = {
    },
    tradePetIds = {
      isSet = true,
    },
    tradePetIds_deleted = {
      isSetDel = true,
      targetFieldName = "tradePetIds",
    },
    wishPetId = {
    },
  },
  StarPCardDetailWorld = {
    avatar = {
    },
    desc = {
    },
    id = {
    },
    level = {
    },
    maxMemberCount = {
    },
    memberCount = {
    },
    name = {
    },
  },
  StarPCardInfo = {
    deleteTime = {
    },
    detail = {
      isMsg = true,
    },
    expireTime = {
    },
    id = {
    },
    receiver = {
    },
    secretkey = {
    },
    type = {
    },
    uid = {
    },
  },
  StarPCatchBoss = {
    bossInfo = {
      isMap = true,
      keyName = "typeId",
    },
    bossInfo_deleted = {
      isMapDel = true,
      targetFieldName = "bossInfo",
    },
  },
  StarPCatchPetDayInfo = {
    catchPetInfo = {
      isMap = true,
      keyName = "petTypeId",
    },
    catchPetInfo_deleted = {
      isMapDel = true,
      targetFieldName = "catchPetInfo",
    },
    recentCatchTime = {
    },
  },
  StarPCatchPetInfo = {
    cnt = {
    },
    petTypeId = {
    },
  },
  StarPCatchPetOrder = {
    lastOrder = {
    },
    petTypeId = {
    },
  },
  StarPCatchPetOrderLast = {
    lastPetTypeRecord = {
      isMap = true,
      keyName = "petTypeId",
    },
    lastPetTypeRecord_deleted = {
      isMapDel = true,
      targetFieldName = "lastPetTypeRecord",
    },
  },
  StarPChatInfo = {
    curRoleId = {
    },
    curStarPId = {
    },
    groupChatInfo = {
      isMsg = true,
    },
    roleChatInfo = {
      isMap = true,
      keyName = "roleId",
    },
    roleChatInfo_deleted = {
      isMapDel = true,
      targetFieldName = "roleChatInfo",
    },
    spIconInfo = {
      isMsg = true,
    },
    starPVisitChatGroup = {
      isMsg = true,
    },
    starPWorldChatGroup = {
      isMap = true,
      keyName = "starPId",
    },
    starPWorldChatGroup_deleted = {
      isMapDel = true,
      targetFieldName = "starPWorldChatGroup",
    },
  },
  StarPClimbTower = {
    type = {
    },
  },
  StarPCommonBasePve = {
    commonBasePve = {
      isMap = true,
      keyName = "pWorldId",
    },
    commonBasePve_deleted = {
      isMapDel = true,
      targetFieldName = "commonBasePve",
    },
  },
  StarPContactGiftRecord = {
    time = {
    },
    uid = {
    },
  },
  StarPContactRecordUnion = {
    blessRecord = {
      isMsg = true,
    },
    giftRecord = {
      isMsg = true,
    },
    socInteractRecord = {
      isMsg = true,
    },
    visitRecord = {
      isMsg = true,
    },
  },
  StarPCrop = {
    lastUpdateTime = {
    },
    seed = {
      isMsg = true,
    },
    stolenCount = {
    },
    stolenCropList = {
      isMap = true,
      keyName = "itemId",
    },
    stolenCropList_deleted = {
      isMapDel = true,
      targetFieldName = "stolenCropList",
    },
    stolenUIDs = {
      isSet = true,
    },
    stolenUIDs_deleted = {
      isSetDel = true,
      targetFieldName = "stolenUIDs",
    },
    totalItems = {
      isMap = true,
      keyName = "itemId",
    },
    totalItems_deleted = {
      isMapDel = true,
      targetFieldName = "totalItems",
    },
  },
  StarPDefaultDressUpInfo = {
    defaultdressUpCfgId = {
    },
    grantDefaultdressUp = {
    },
  },
  StarPDelInfo = {
    createTime = {
    },
    delExpTime = {
    },
    delReason = {
    },
    uid = {
    },
  },
  StarPDormancyBreedInfo = {
    fatherInstId = {
    },
    finishTime = {
    },
    motherInstId = {
    },
    speed = {
    },
    uid = {
    },
  },
  StarPDressUpInfo = {
    defaultdressUpCfgId = {
    },
    dressUpCfgId = {
    },
    grantDefaultdressUp = {
    },
    lastUpdateTime = {
    },
    openDressUpRoom = {
    },
  },
  StarPDropGuaranteeBagInfo = {
    bagId = {
    },
    itemsInfo = {
      isMap = true,
      keyName = "itemId",
    },
    itemsInfo_deleted = {
      isMapDel = true,
      targetFieldName = "itemsInfo",
    },
  },
  StarPDropGuaranteeBagInfoItem = {
    expectCnt = {
    },
    itemId = {
    },
    missCnt = {
    },
  },
  StarPDropGuaranteeInfo = {
    dropBagInfo = {
      isMap = true,
      keyName = "bagId",
    },
    dropBagInfo_deleted = {
      isMapDel = true,
      targetFieldName = "dropBagInfo",
    },
  },
  StarPDropSpawnFromParam = {
    From = {
    },
    FromId = {
    },
  },
  StarPDsAddr = {
    aliasId = {
    },
    compatID = {
    },
    createTime = {
    },
    curStateStartTime = {
    },
    desModInfo = {
    },
    dsAddr = {
    },
    dsaServiceId = {
    },
    dsaServiceName = {
    },
    dsAuthToken = {
    },
    eventTime = {
    },
    eventType = {
    },
    fleetId = {
    },
    gamesessID = {
    },
    hasStarted = {
    },
    isLoal = {
    },
    lastDsHeartbeatTime = {
    },
    logFile = {
    },
    maxVersionCompatId = {
    },
    migrateDsSessionId = {
    },
    migrateSnapshot = {
      isMsg = true,
    },
    needRecovery = {
    },
    pakVersion = {
    },
    previousCreateTime = {
    },
    previousDsAddr = {
    },
    previousDsaInstanceID = {
    },
    previousDsHeartbeatTime = {
    },
    previousDsSessionId = {
    },
    previousVersionCompatId = {
    },
    queryFail = {
    },
    sessionCpu = {
    },
    sessionMemory = {
    },
    state = {
    },
    stopGameGamesessID = {
    },
    stopGameTime = {
    },
    triggerAutoRecoverySec = {
    },
    visitorCount = {
    },
  },
  StarPDsCommonDbInfo = {
    changedCommonDsDbAttr = {
    },
    commonDsDbAttr = {
    },
    dataVersion = {
    },
    dsVersion = {
    },
    key1 = {
    },
    key2 = {
    },
    key3 = {
    },
    key4 = {
    },
    key5 = {
    },
    key6 = {
    },
    key7 = {
    },
    key8 = {
    },
  },
  StarPDsCommonDbInfoResult = {
    commonDbKey = {
      isMsg = true,
    },
    result = {
    },
  },
  StarPDsCommonDbKey = {
    key1 = {
    },
    key2 = {
    },
    key3 = {
    },
    key4 = {
    },
    key5 = {
    },
    key6 = {
    },
    key7 = {
    },
    key8 = {
    },
  },
  StarPDsFightData = {
    abilities = {
      isMap = true,
      keyName = "id",
    },
    abilities_deleted = {
      isMapDel = true,
      targetFieldName = "abilities",
    },
    buffs = {
      isMap = true,
      keyName = "id",
    },
    buffs_deleted = {
      isMapDel = true,
      targetFieldName = "buffs",
    },
    cachedabilities = {
      isMap = true,
      keyName = "id",
    },
    cachedabilities_deleted = {
      isMapDel = true,
      targetFieldName = "cachedabilities",
    },
    cachedbuffs = {
      isMap = true,
      keyName = "id",
    },
    cachedbuffs_deleted = {
      isMapDel = true,
      targetFieldName = "cachedbuffs",
    },
    curWeaponIndex = {
    },
    starPOneAbilityCDInfo = {
      isMap = true,
      keyName = "abilityId",
    },
    starPOneAbilityCDInfo_deleted = {
      isMapDel = true,
      targetFieldName = "starPOneAbilityCDInfo",
    },
    starPOneBuffInfo = {
      isMap = true,
      keyName = "buffId",
    },
    starPOneBuffInfo_deleted = {
      isMapDel = true,
      targetFieldName = "starPOneBuffInfo",
    },
  },
  StarPDSFriendIntimacyInfo = {
    intimacyType = {
    },
    num = {
    },
    timestamp = {
    },
  },
  StarPDSFriendIntimacyMap = {
    friendIntimacyMap = {
      isMap = true,
      keyName = "intimacyType",
    },
    friendIntimacyMap_deleted = {
      isMapDel = true,
      targetFieldName = "friendIntimacyMap",
    },
  },
  StarPDsGroupApplication = {
    applierRoleId = {
    },
    applierUid = {
    },
    applyTime = {
    },
    isRead = {
    },
    operateTime = {
    },
    operatorRoleId = {
    },
    operatorUid = {
    },
    result = {
    },
    totalPetsCount = {
    },
    totalPetsDexCount = {
    },
  },
  StarPDsGroupApplicationData = {
    applications = {
      isMap = true,
      keyName = "applierUid",
    },
    applications_deleted = {
      isMapDel = true,
      targetFieldName = "applications",
    },
  },
  StarPDsGroupDBInvasionData = {
    invasionExp = {
    },
    invasionHappening = {
    },
    invasionLastTriggerTime = {
    },
    invasionLevel = {
    },
    invasionNextClearTime = {
    },
    invasionTodayCount = {
    },
  },
  StarPDsGroupDBTradeStationData = {
    tradeStationExp = {
    },
    tradeStationLevel = {
    },
  },
  StarPDsGroupDBUserData = {
    applicationData = {
      isMsg = true,
    },
    createTime = {
    },
    destroyTime = {
    },
    formerLeaderRoleId = {
    },
    formerLeaderUId = {
    },
    groupHeadIcon = {
    },
    groupId = {
    },
    groupName = {
    },
    groupStatus = {
    },
    groupType = {
    },
    gvgRank = {
    },
    invasionData = {
      isMsg = true,
    },
    invitationData = {
      isMsg = true,
    },
    isApprovalRequired = {
    },
    lastRefreshTime = {
    },
    leaderRoleId = {
    },
    leaderUId = {
    },
    member = {
      isAry = true,
    },
    member_is_cleared = {
      isAryClear = true,
      targetFieldName = "member",
    },
    memberRoleIds = {
      isAry = true,
    },
    memberRoleIds_is_cleared = {
      isAryClear = true,
      targetFieldName = "memberRoleIds",
    },
    members = {
      isMap = true,
      keyName = "uid",
    },
    members_deleted = {
      isMapDel = true,
      targetFieldName = "members",
    },
    memberUids = {
      isAry = true,
    },
    memberUids_is_cleared = {
      isAryClear = true,
      targetFieldName = "memberUids",
    },
    nextRefreshTime = {
    },
    strongPointLevel = {
    },
    terminalData = {
      isMsg = true,
    },
    terminalLevel = {
    },
    tradeStationData = {
      isMsg = true,
    },
  },
  StarPDsGroupInvitation = {
    id = {
    },
    inviteeRoleId = {
    },
    inviteeUid = {
    },
    inviterRoleId = {
    },
    inviterUid = {
    },
    inviteStatus = {
    },
    inviteTime = {
    },
    isRead = {
    },
    refusalCDTime = {
    },
  },
  StarPDsGroupInvitationData = {
    invitations = {
      isMap = true,
      keyName = "id",
    },
    invitations_deleted = {
      isMapDel = true,
      targetFieldName = "invitations",
    },
  },
  StarPDsGroupMemberDBPetInfo = {
    level = {
    },
    petId = {
    },
    petInstId = {
    },
    petTypeId = {
    },
  },
  StarPDsGroupMemberDBUserData = {
    groupId = {
    },
    isGroupFuncOpen = {
    },
    joinTime = {
    },
    lastActiveTime = {
    },
    lastApplyTime = {
    },
    level = {
    },
    name = {
    },
    petTeamData = {
      isMap = true,
      keyName = "petInstId",
    },
    petTeamData_deleted = {
      isMapDel = true,
      targetFieldName = "petTeamData",
    },
    roleId = {
    },
    status = {
    },
    terminalLevel = {
    },
    title = {
    },
    tradeStationData = {
      isMsg = true,
    },
    uid = {
    },
    weeklyApplyCount = {
    },
  },
  StarPDsGroupNews = {
    category = {
    },
    content = {
    },
    datetime = {
    },
    displayArgs = {
      isAry = true,
    },
    displayArgs_is_cleared = {
      isAryClear = true,
      targetFieldName = "displayArgs",
    },
    id = {
    },
    title = {
    },
    type = {
    },
  },
  StarPDsGroupNewsData = {
    groupId = {
    },
    groupNews = {
      isMap = true,
      keyName = "id",
    },
    groupNews_deleted = {
      isMapDel = true,
      targetFieldName = "groupNews",
    },
  },
  StarPDsGroupPlayerData = {
    contributeInfo = {
      isMap = true,
      keyName = "groupId",
    },
    contributeInfo_deleted = {
      isMapDel = true,
      targetFieldName = "contributeInfo",
    },
    contriPointToday = {
    },
    currentGroupId = {
    },
    lastContriPointRefreshTime = {
    },
    lastGachaTime = {
    },
    title = {
    },
  },
  StarPDsGroupTerminalData = {
    mainTerminalUId = {
    },
    terminals = {
      isMap = true,
      keyName = "buildingUId",
    },
    terminals_deleted = {
      isMapDel = true,
      targetFieldName = "terminals",
    },
  },
  StarPDsGroupTerminalKey = {
    buildingUId = {
    },
  },
  StarPDsGuildApplication = {
    applierRoleId = {
    },
    applierUid = {
    },
    applyTime = {
    },
    isRead = {
    },
    operateTime = {
    },
    operatorRoleId = {
    },
    operatorUid = {
    },
    result = {
    },
    totalPetsCount = {
    },
    totalPetsDexCount = {
    },
  },
  StarPDsGuildApplicationData = {
    applications = {
      isMap = true,
      keyName = "applierUid",
    },
    applications_deleted = {
      isMapDel = true,
      targetFieldName = "applications",
    },
  },
  StarpDsGuildCopied = {
    allGuildCopied = {
      isMap = true,
      keyName = "guildId",
    },
    allGuildCopied_deleted = {
      isMapDel = true,
      targetFieldName = "allGuildCopied",
    },
  },
  StarPDsGuildDBInvasionData = {
    invasionExp = {
    },
    invasionHappening = {
    },
    invasionLastTriggerTime = {
    },
    invasionLevel = {
    },
    invasionNextClearTime = {
    },
    invasionTodayCount = {
    },
  },
  StarPDsGuildDBTradeStationData = {
    todayGetExp = {
    },
    tradeStationExp = {
    },
    tradeStationLevel = {
    },
  },
  StarPDsGuildDBUserData = {
    applicationData = {
      isMsg = true,
    },
    createTime = {
    },
    destroyTime = {
    },
    efficiencyRate = {
    },
    formerLeaderRoleId = {
    },
    formerLeaderUId = {
    },
    giftData = {
      isMap = true,
      keyName = "recordId",
    },
    giftData_deleted = {
      isMapDel = true,
      targetFieldName = "giftData",
    },
    guildHeadIcon = {
    },
    guildId = {
    },
    guildName = {
    },
    guildStatus = {
    },
    guildType = {
    },
    gvgRank = {
    },
    invasionData = {
      isMsg = true,
    },
    invitationData = {
      isMsg = true,
    },
    isApprovalRequired = {
    },
    lastDailyRefreshTime = {
    },
    lastRefreshTime = {
    },
    leaderRoleId = {
    },
    leaderUId = {
    },
    member = {
      isAry = true,
    },
    member_is_cleared = {
      isAryClear = true,
      targetFieldName = "member",
    },
    memberRoleIds = {
      isAry = true,
    },
    memberRoleIds_is_cleared = {
      isAryClear = true,
      targetFieldName = "memberRoleIds",
    },
    members = {
      isMap = true,
      keyName = "uid",
    },
    members_deleted = {
      isMapDel = true,
      targetFieldName = "members",
    },
    memberUids = {
      isAry = true,
    },
    memberUids_is_cleared = {
      isAryClear = true,
      targetFieldName = "memberUids",
    },
    nextDailyRefreshTime = {
    },
    nextRefreshTime = {
    },
    seedBoxData = {
      isMsg = true,
    },
    strongPointLevel = {
    },
    terminalData = {
      isMsg = true,
    },
    terminalLevel = {
    },
    tradeStationData = {
      isMsg = true,
    },
    wishStatueData = {
      isMsg = true,
    },
  },
  StarPDsGuildGVGData = {
    cups = {
    },
    destoryTime = {
      isMsg = true,
    },
    grade = {
    },
    military = {
    },
    protectionTime = {
      isMsg = true,
    },
  },
  StarPDsGuildGVGOffline = {
    state = {
      isAry = true,
    },
    state_is_cleared = {
      isAryClear = true,
      targetFieldName = "state",
    },
    times = {
      isAry = true,
    },
    times_is_cleared = {
      isAryClear = true,
      targetFieldName = "times",
    },
  },
  StarPDsGuildInvitation = {
    id = {
    },
    inviteeRoleId = {
    },
    inviteeUid = {
    },
    inviterRoleId = {
    },
    inviterUid = {
    },
    inviteStatus = {
    },
    inviteTime = {
    },
    isRead = {
    },
    refusalCDTime = {
    },
  },
  StarPDsGuildInvitationData = {
    invitations = {
      isMap = true,
      keyName = "id",
    },
    invitations_deleted = {
      isMapDel = true,
      targetFieldName = "invitations",
    },
  },
  StarPDsGuildMemberDBPetInfo = {
    level = {
    },
    petId = {
    },
    petInstId = {
    },
    petTypeId = {
    },
  },
  StarPDsGuildMemberDBUserData = {
    guildId = {
    },
    isGuildFuncOpen = {
    },
    joinTime = {
    },
    lastActiveTime = {
    },
    lastApplyTime = {
    },
    level = {
    },
    name = {
    },
    petTeamData = {
      isMap = true,
      keyName = "petInstId",
    },
    petTeamData_deleted = {
      isMapDel = true,
      targetFieldName = "petTeamData",
    },
    roleId = {
    },
    status = {
    },
    terminalLevel = {
    },
    title = {
    },
    tradeStationData = {
      isMsg = true,
    },
    uid = {
    },
    weeklyApplyCount = {
    },
  },
  StarPDsGuildNews = {
    category = {
    },
    content = {
    },
    datetime = {
    },
    displayArgs = {
      isAry = true,
    },
    displayArgs_is_cleared = {
      isAryClear = true,
      targetFieldName = "displayArgs",
    },
    id = {
    },
    title = {
    },
    type = {
    },
  },
  StarPDsGuildNewsData = {
    guildId = {
    },
    guildNews = {
      isMap = true,
      keyName = "id",
    },
    guildNews_deleted = {
      isMapDel = true,
      targetFieldName = "guildNews",
    },
  },
  StarpDsGuildOneCopied = {
    guildId = {
    },
    guildStatus = {
    },
    nextReconciliationTime = {
    },
  },
  StarPDsGuildTerminalData = {
    mainTerminalUId = {
    },
    terminals = {
      isMap = true,
      keyName = "buildingUId",
    },
    terminals_deleted = {
      isMapDel = true,
      targetFieldName = "terminals",
    },
    virtualTerminalUId = {
    },
  },
  StarPDsGuildTerminalKey = {
    buildingUId = {
    },
  },
  StarPDsInfo = {
    dsAddr = {
      isMsg = true,
    },
  },
  StarPDsMapPosCommonDBUserData = {
    commonId = {
    },
    disappearTime = {
    },
    mapPosDBType = {
    },
    posId = {
    },
    posInfo = {
      isMsg = true,
    },
    starPDsMapPosCommonDBUserDataUnion = {
      isMsg = true,
    },
    starPId = {
    },
  },
  StarPDsMapPosCommonDBUserDataUnion = {
    starPMapPosBuilding = {
      isMsg = true,
    },
    starPMapPosBuildingListInfo = {
      isMsg = true,
    },
    starPMapPosDropItem = {
      isMsg = true,
    },
    starPMapPosPoi = {
      isMsg = true,
    },
  },
  StarPDsMapPosDataInfo = {
    posData = {
      isMsg = true,
    },
    storeType = {
    },
  },
  StarPDsMapPosDataInfoUnion = {
    noFloatPosData = {
      isMsg = true,
    },
    pbPosData = {
      isMsg = true,
    },
  },
  StarPDsNoFloatPosData = {
    pitch = {
    },
    posX = {
    },
    posY = {
    },
    posZ = {
    },
    roll = {
    },
    yaw = {
    },
  },
  StarPDSNotifyGroupData = {
    contributeInfo = {
      isMsg = true,
    },
    groupId = {
    },
    level = {
    },
    version = {
    },
  },
  StarPDsPBMapPosData = {
    posInfo = {
    },
  },
  StarPDsPersonalBaseData = {
    globalGuildId = {
    },
    guildId = {
    },
    isGuildFuncOpen = {
    },
    level = {
    },
    maxTerminalLevel = {
    },
    petTeamData = {
      isMap = true,
      keyName = "petInstId",
    },
    petTeamData_deleted = {
      isMapDel = true,
      targetFieldName = "petTeamData",
    },
    seedBoxData = {
      isMsg = true,
    },
    terminalData = {
      isMsg = true,
    },
    terminalLevel = {
    },
    uid = {
    },
  },
  StarPDsPlayerCommonDBUserData = {
    starPDsPlayerCommonDBUserDataUnion = {
      isMsg = true,
    },
    type = {
    },
  },
  StarPDsPlayerCommonDBUserDataUnion = {
    starPBasePveData = {
      isMsg = true,
    },
    starPClimbTower = {
      isMsg = true,
    },
    starPDropGuaranteeInfo = {
      isMsg = true,
    },
    starPOfflineTaskInfo = {
      isMsg = true,
    },
    starPPlayerBaseInfo = {
      isMsg = true,
    },
    starPPlayerBit = {
      isMsg = true,
    },
    starPPlayerCatchPet = {
      isMsg = true,
    },
    starPPlayerEnergyInfo = {
      isMsg = true,
    },
    starPPlayerPetFeedInfo = {
      isMsg = true,
    },
    starPPlayerRoleAttr = {
      isMsg = true,
    },
    starPPlayerSetting = {
      isMsg = true,
    },
    starPPlayerShopInfos = {
      isMsg = true,
    },
    starPPlayerTalentData = {
      isMsg = true,
    },
    starPPlayerTaskBaseInfo = {
      isMsg = true,
    },
    starPPlayerTaskGroupInfo = {
      isMsg = true,
    },
    starPPlayerTaskInfo = {
      isMsg = true,
    },
    starPPlayerTech = {
      isMsg = true,
    },
    starPPlayMapInfo = {
      isMsg = true,
    },
    starPTerminalLevel = {
      isMsg = true,
    },
  },
  StarPDsPlayerShopBuybackItemInfo = {
    coinType = {
    },
    id = {
    },
    itemId = {
    },
    soldCoin = {
    },
    soldNum = {
    },
    userData = {
      isMsg = true,
    },
  },
  StarPDsPlayerShopBuybackItemTimeInfo = {
    itemID = {
    },
    itemInstId = {
    },
    lastSoldTime = {
    },
  },
  StarPDsPlayerShopInfo = {
    buybackItemInfo = {
      isMap = true,
      keyName = "id",
    },
    buybackItemInfo_deleted = {
      isMapDel = true,
      targetFieldName = "buybackItemInfo",
    },
    buybackItemTimeInfo = {
      isMap = true,
      keyName = "itemID",
    },
    buybackItemTimeInfo_deleted = {
      isMapDel = true,
      targetFieldName = "buybackItemTimeInfo",
    },
    id = {
    },
    itemInfo = {
      isMap = true,
      keyName = "itemId",
    },
    itemInfo_deleted = {
      isMapDel = true,
      targetFieldName = "itemInfo",
    },
    npcId = {
    },
    refreshTimestamp = {
    },
    shopDeatilsID = {
    },
    starPId = {
    },
  },
  StarPDsPlayerShopInfos = {
    playerShopInfo = {
      isMap = true,
      keyName = "id",
    },
    playerShopInfo_deleted = {
      isMapDel = true,
      targetFieldName = "playerShopInfo",
    },
  },
  StarPDsPlayerShopItemInfo = {
    boughtNum = {
    },
    itemId = {
    },
    userData = {
      isMsg = true,
    },
  },
  StarPDsSeedBoxData = {
    bAutoPlan = {
    },
    bAutoSaveSeed = {
    },
  },
  StarPDsSelfTerminalData = {
    mainTerminalUId = {
    },
    terminals = {
      isMap = true,
      keyName = "buildingUId",
    },
    terminals_deleted = {
      isMapDel = true,
      targetFieldName = "terminals",
    },
    virtualTerminalUId = {
    },
  },
  StarPDsSelfTerminalKey = {
    buildingUId = {
    },
  },
  StarPDSTimeOffset = {
    addTime = {
    },
  },
  StarPDsWorldBoxSettingData = {
    allowSaveItemTypes = {
      isAry = true,
    },
    allowSaveItemTypes_is_cleared = {
      isAryClear = true,
      targetFieldName = "allowSaveItemTypes",
    },
  },
  StarPDsWorldCommonDBUserData = {
    starPDsWorldCommonDBUserDataUnion = {
      isMsg = true,
    },
    type = {
    },
  },
  StarPDsWorldCommonDBUserDataUnion = {
    starPCommonBasePve = {
      isMsg = true,
    },
    starPTrader = {
      isMsg = true,
    },
  },
  StarPEggUserData = {
    attackPotential = {
      isAry = true,
    },
    attackPotential_is_cleared = {
      isAryClear = true,
      targetFieldName = "attackPotential",
    },
    breedPlayerUID = {
    },
    buildingUID = {
    },
    defensePotential = {
      isAry = true,
    },
    defensePotential_is_cleared = {
      isAryClear = true,
      targetFieldName = "defensePotential",
    },
    eggId = {
    },
    fatherType = {
    },
    flashEggType = {
    },
    grid = {
    },
    healthPotential = {
      isAry = true,
    },
    healthPotential_is_cleared = {
      isAryClear = true,
      targetFieldName = "healthPotential",
    },
    motherType = {
    },
    passiveSkill = {
      isAry = true,
    },
    passiveSkill_is_cleared = {
      isAryClear = true,
      targetFieldName = "passiveSkill",
    },
    petId = {
    },
  },
  StarPEquipCreateInfo = {
    equipType = {
    },
    orderUID = {
    },
    playerUid = {
    },
    state = {
    },
  },
  StarPEquipUnlockInfo = {
    equipId = {
    },
    unlockTime = {
    },
  },
  StarPEquipUserData = {
    attachedSkillId = {
    },
    demageValue = {
    },
    mainEffectList = {
      isMap = true,
      keyName = "effKey",
    },
    mainEffectList_deleted = {
      isMapDel = true,
      targetFieldName = "mainEffectList",
    },
    randEffectList = {
      isMap = true,
      keyName = "effKey",
    },
    randEffectList_deleted = {
      isMapDel = true,
      targetFieldName = "randEffectList",
    },
  },
  StarPField = {
    buildID = {
    },
    croplandType = {
    },
    lastOpTimeMs = {
    },
    lastOpType = {
    },
    starPCrop = {
      isMsg = true,
    },
    starPFieldBuildingInfo = {
      isMsg = true,
    },
  },
  StarPFriendInteractData = {
    friendUid = {
    },
    interactTimes = {
    },
  },
  StarPFriendIntimacyActionDailyInfo = {
    actionType = {
    },
    dailyNum = {
    },
  },
  StarPFriendIntimacyInfo = {
    actionDailyInfo = {
      isMap = true,
      keyName = "actionType",
    },
    actionDailyInfo_deleted = {
      isMapDel = true,
      targetFieldName = "actionDailyInfo",
    },
    friendUid = {
    },
    todayIntimacyNum = {
    },
  },
  StarPGeoPoint = {
    lat = {
    },
    lon = {
    },
  },
  StarPGiftData = {
    sendGiftCounts = {
    },
  },
  StarPGiftRecord = {
    friendUid = {
    },
    giverUid = {
    },
    itemCount = {
    },
    itemId = {
    },
    message = {
    },
    packingId = {
    },
    recieverUid = {
    },
    recieveTime = {
    },
    recordId = {
    },
    sendTime = {
    },
    status = {
    },
  },
  StarPGlobalAchieveData = {
    achieveId = {
    },
    finishCnt = {
    },
    firstFinishCnt = {
    },
  },
  StarPGodStatusBaseInfo = {
    attrType = {
    },
    value = {
    },
  },
  StarPGroupApplicationData = {
    applierGuildId = {
    },
    applierStarPId = {
    },
    applierUid = {
    },
    applyGroupId = {
    },
    applyTime = {
    },
    operateTime = {
    },
    result = {
    },
  },
  StarPGroupApplicationList = {
    applications = {
      isMap = true,
      keyName = "applierUid",
    },
    applications_deleted = {
      isMapDel = true,
      targetFieldName = "applications",
    },
  },
  StarPGroupChatInfo = {
    chatGroupKey = {
      isMsg = true,
    },
    createTime = {
    },
    groupId = {
    },
    groupName = {
    },
    title = {
    },
  },
  StarPGroupContributeInfo = {
    contributePointAtDayRefresh = {
    },
    contributePointTotal = {
    },
    lastDayRefreshTime = {
    },
  },
  StarPGroupData = {
    applicationList = {
      isMsg = true,
    },
    contributeInfo = {
      isMsg = true,
    },
    createTime = {
    },
    describe = {
    },
    groupId = {
    },
    headIcon = {
    },
    invitationsList = {
      isMsg = true,
    },
    joinType = {
    },
    lastUpdateEsTime = {
    },
    leaderUid = {
    },
    level = {
    },
    levelUpTimeList = {
      isMsg = true,
    },
    name = {
    },
    publishType = {
    },
    starPDsInfo = {
      isMsg = true,
    },
    status = {
    },
    version = {
    },
  },
  StarPGroupInvitationData = {
    id = {
    },
    inviteeUid = {
    },
    inviteGroupId = {
    },
    inviterUid = {
    },
    inviteTime = {
    },
  },
  StarPGroupInvitationList = {
    invitations = {
      isMap = true,
      keyName = "id",
    },
    invitations_deleted = {
      isMapDel = true,
      targetFieldName = "invitations",
    },
  },
  StarPGroupLevelUpTimeData = {
    level = {
    },
    time = {
    },
  },
  StarPGroupLevelUpTimeList = {
    levelUpTimeData = {
      isMap = true,
      keyName = "level",
    },
    levelUpTimeData_deleted = {
      isMapDel = true,
      targetFieldName = "levelUpTimeData",
    },
  },
  StarPGroupMemberData = {
    contributePoint = {
    },
    dressUpInfo = {
      isMsg = true,
    },
    dsGroupID = {
    },
    enterDsRoleId = {
    },
    enterTimeStamp = {
    },
    groupId = {
    },
    isLoaded = {
    },
    joinGroupTime = {
    },
    lastActiveTime = {
    },
    lastApplyTime = {
    },
    lastHeartBeatTimeStamp = {
    },
    lastUpdateTime = {
    },
    materialAssistCnt = {
    },
    petTradeCnt = {
    },
    playerPublicEquipments = {
      isMsg = true,
    },
    playerPublicProfileInfo = {
      isMsg = true,
    },
    preExitTimeStamp = {
    },
    signInTime = {
    },
    status = {
    },
    title = {
    },
    uid = {
    },
    version = {
    },
    weeklyApplyCount = {
    },
  },
  StarPGroupPlayerContributeInfoMap = {
    contriPoint = {
    },
    groupId = {
    },
  },
  StarPGroupSimpleData = {
    groupId = {
    },
    groupName = {
    },
  },
  StarPGsCommonDbInfo = {
    gsCommonDbInfoUnion = {
      isMsg = true,
    },
    gsCommonDbKey = {
      isMsg = true,
    },
  },
  StarPGsCommonDbInfoUnion = {
    adventureBaseInfo = {
      isMsg = true,
    },
    mapStarpShop = {
      isMsg = true,
    },
    starPPetTradeFocusInfo = {
      isMsg = true,
    },
    starPPlayerIntimacyInfo = {
      isMsg = true,
    },
    starPPlayerLotteryInfo = {
      isMsg = true,
    },
    StarPSocInteractionRatio = {
      isMsg = true,
    },
  },
  StarPGsCommonDbKey = {
    moduleType = {
    },
    roleId = {
    },
    uId = {
    },
  },
  StarPGuideCheckResources = {
    finish = {
    },
    guideID = {
    },
  },
  StarPGuideSave = {
    finish = {
    },
    finishTime = {
    },
    guideID = {
    },
  },
  StarPGuildActiveData = {
    activePoint = {
    },
  },
  StarPGuildApplicationData = {
    applierUid = {
    },
    applyGuildId = {
    },
    applyTime = {
    },
    member = {
      isMsg = true,
    },
    operateTime = {
    },
    result = {
    },
  },
  StarPGuildApplicationList = {
    applications = {
      isMap = true,
      keyName = "applierUid",
    },
    applications_deleted = {
      isMapDel = true,
      targetFieldName = "applications",
    },
  },
  StarPGuildData = {
    activeData = {
      isMsg = true,
    },
    applicationList = {
      isMsg = true,
    },
    applications = {
      isMsg = true,
    },
    efficiencyRate = {
    },
    guildId = {
    },
    invitations = {
      isMsg = true,
    },
    invitationsList = {
      isMsg = true,
    },
    isApprovalRequired = {
    },
    leaderUid = {
    },
    level = {
    },
    mainTerminalUId = {
    },
    name = {
    },
    starpId = {
    },
  },
  StarpGuildDrop = {
    guildId = {
    },
  },
  StarPGuildInvitationData = {
    id = {
    },
    inviteeUid = {
    },
    inviteGuildId = {
    },
    inviterUid = {
    },
    inviteTime = {
    },
    result = {
    },
  },
  StarPGuildInvitationList = {
    invitations = {
      isMap = true,
      keyName = "id",
    },
    invitations_deleted = {
      isMapDel = true,
      targetFieldName = "invitations",
    },
  },
  StarPGuildMemberData = {
    guildId = {
    },
    joinTime = {
    },
    lastActiveTime = {
    },
    lastPlayerStatusTime = {
    },
    name = {
    },
    playerStatus = {
    },
    reportActivePoint = {
    },
    reportActiveTime = {
    },
    title = {
    },
    uid = {
    },
  },
  StarPGuildSimpleData = {
    guildId = {
    },
    guildName = {
    },
  },
  StarpGuildTermItemCopied = {
    itemId = {
    },
    itemInstId = {
    },
    itemNum = {
    },
    itemType = {
    },
    obtainTime = {
    },
    ownerSpuid = {
    },
    userData = {
      isMsg = true,
    },
  },
  StarpGuildTermItemCopiedList = {
    allItems = {
      isMap = true,
      keyName = "itemInstId",
    },
    allItems_deleted = {
      isMapDel = true,
      targetFieldName = "allItems",
    },
    guildBillno = {
    },
    guildId = {
    },
  },
  StarPGuildWishStatueData = {
    blessedCount = {
    },
    extraBuff = {
    },
    luckBuff = {
    },
    updateTime = {
    },
  },
  StarPHarvestItem = {
    itemId = {
    },
    itemNum = {
    },
  },
  StarPHatchPetInfo = {
    elementType = {
    },
  },
  StarPHatchPetRecord = {
    hatchPetRecord = {
      isMap = true,
      keyName = "elementType",
    },
    hatchPetRecord_deleted = {
      isMapDel = true,
      targetFieldName = "hatchPetRecord",
    },
  },
  StarPHelperInfoList = {
    helperTriggerInfoList = {
      isMap = true,
      keyName = "promptId",
    },
    helperTriggerInfoList_deleted = {
      isMapDel = true,
      targetFieldName = "helperTriggerInfoList",
    },
  },
  StarPHelperTriggerInfo = {
    promptId = {
    },
    triggerCount = {
    },
  },
  StarPInfo = {
    backup = {
    },
    banTime = {
    },
    cancelCount = {
    },
    cancelTime = {
    },
    CDTime = {
    },
    chatChannel = {
      isMap = true,
      keyName = "index",
    },
    chatChannel_deleted = {
      isMapDel = true,
      targetFieldName = "chatChannel",
    },
    currStarPId = {
    },
    hasEnteredShip = {
    },
    indexCnt = {
    },
    isTouchedStarPMode = {
    },
    lastCreateRoleMs = {
    },
    lastCreateWorldMs = {
    },
    lastStarPId = {
    },
    pvpDailyData = {
      isMsg = true,
    },
    pvpData = {
      isMsg = true,
    },
    qualifyTypeInfo = {
      isMap = true,
      keyName = "qualifyType",
    },
    qualifyTypeInfo_deleted = {
      isMapDel = true,
      targetFieldName = "qualifyTypeInfo",
    },
  },
  StarPInteractPoint = {
    buildingInfos = {
      isMap = true,
      keyName = "buildingId",
    },
    buildingInfos_deleted = {
      isMapDel = true,
      targetFieldName = "buildingInfos",
    },
    mainTerminalId = {
    },
  },
  StarPInvitedHistory = {
    invitedHistoryItemMap = {
      isMap = true,
      keyName = "inviteeUid",
    },
    invitedHistoryItemMap_deleted = {
      isMapDel = true,
      targetFieldName = "invitedHistoryItemMap",
    },
  },
  StarPInvitedHistoryItem = {
    inviteeUid = {
    },
    inviteTimeSec = {
    },
  },
  StarPInvitedMapInfo = {
    invitedMemberMap = {
      isMap = true,
      keyName = "starPWorldId",
    },
    invitedMemberMap_deleted = {
      isMapDel = true,
      targetFieldName = "invitedMemberMap",
    },
  },
  StarPInvitedRecord = {
    inviteeUid = {
    },
    inviteTimeSec = {
    },
    inviteUid = {
    },
  },
  StarPInvitedRecordList = {
    invitedRecodeList = {
      isMap = true,
      keyName = "inviteeUid",
    },
    invitedRecodeList_deleted = {
      isMapDel = true,
      targetFieldName = "invitedRecodeList",
    },
    starPWorldId = {
    },
  },
  StarPInviteMeMapInfo = {
    invitedMeMap = {
      isMap = true,
      keyName = "starPWorldId",
    },
    invitedMeMap_deleted = {
      isMapDel = true,
      targetFieldName = "invitedMeMap",
    },
  },
  StarPInviteMeRecord = {
    inviteMemberPubInfo = {
      isMsg = true,
    },
    inviteTimeSec = {
    },
    inviteUid = {
    },
    starPBasicInfo = {
      isMsg = true,
    },
  },
  StarPInviteMeRecordList = {
    inviteMeRecodeMap = {
      isMap = true,
      keyName = "inviteUid",
    },
    inviteMeRecodeMap_deleted = {
      isMapDel = true,
      targetFieldName = "inviteMeRecodeMap",
    },
    starPWorldId = {
    },
  },
  StarPItem = {
    backpackId = {
    },
    bindPlayerUid = {
    },
    grid = {
    },
    isLock = {
    },
    itemNum = {
    },
    itemType = {
    },
    obtainTime = {
    },
    ownerId = {
    },
    ownerSpuid = {
    },
    starPItemDropInfo = {
      isMsg = true,
    },
    userData = {
      isMsg = true,
    },
  },
  StarPItemBrief = {
    itemId = {
    },
    num = {
    },
  },
  StarPItemDropInfo = {
    starpGuildDrop = {
      isMsg = true,
    },
    starpPersonalDrop = {
      isMsg = true,
    },
    starpTeamDrop = {
      isMsg = true,
    },
  },
  StarPItemIdx = {
    itemType = {
    },
    starPId = {
    },
  },
  StarPItemUserDataUnion = {
    starPEggUserData = {
      isMsg = true,
    },
    starPEquipUserData = {
      isMsg = true,
    },
    starPPet = {
      isMsg = true,
    },
    starPPetUserData = {
      isMsg = true,
    },
    starPSimpleItemUserData = {
      isMsg = true,
    },
    starPWeaponUserData = {
      isMsg = true,
    },
  },
  StarPKeyValue = {
    attrKey = {
    },
    attrValue = {
    },
  },
  StarPLoginSession = {
    accountMaxLevel = {
    },
    cliPakVer = {
    },
    cliVer = {
    },
    dressUpInfo = {
      isMsg = true,
    },
    featureData = {
      isMsg = true,
    },
    forceCreateDS = {
    },
    forceEnter = {
    },
    friendUid = {
    },
    groupID = {
    },
    identity = {
    },
    isLocal = {
    },
    localDsId = {
    },
    location = {
      isMsg = true,
    },
    mainTerminalUID = {
    },
    needInherit = {
    },
    originalStarPWorldId = {
    },
    playerOpenId = {
    },
    playerPublicEquipments = {
      isMsg = true,
    },
    playerPublicProfileInfo = {
      isMsg = true,
    },
    roleId = {
    },
    tabTestGroupId = {
    },
    teamId = {
    },
    visitor = {
    },
    visits = {
    },
  },
  StarPMail = {
    argsValues = {
      isMsg = true,
    },
    attachments = {
      isMsg = true,
    },
    cfgId = {
    },
    customContent = {
    },
    customTitle = {
    },
    expireTime = {
    },
    extraData = {
      isMsg = true,
    },
    hintId = {
    },
    isReceived = {
    },
    mailId = {
    },
    mailType = {
    },
    ownerId = {
    },
    senderId = {
    },
    senderName = {
    },
    senderType = {
    },
    sendTime = {
    },
    sourceType = {
    },
    starPId = {
    },
    status = {
    },
  },
  StarPMailArgsValues = {
    argsValues = {
      isMap = true,
      keyName = "seq",
    },
    argsValues_deleted = {
      isMapDel = true,
      targetFieldName = "argsValues",
    },
  },
  StarPMailArgValue = {
    name = {
    },
    seq = {
    },
    value = {
    },
  },
  StarPMailAttachments = {
    attachments = {
      isMap = true,
      keyName = "seq",
    },
    attachments_deleted = {
      isMapDel = true,
      targetFieldName = "attachments",
    },
  },
  StarPMailExtraData = {
    deleteStarPIds = {
      isAry = true,
    },
    deleteStarPIds_is_cleared = {
      isAryClear = true,
      targetFieldName = "deleteStarPIds",
    },
    mailScope = {
    },
    onlyReceivedOnce = {
    },
    ownerStarPIds = {
      isAry = true,
    },
    ownerStarPIds_is_cleared = {
      isAryClear = true,
      targetFieldName = "ownerStarPIds",
    },
    readStarPIds = {
      isAry = true,
    },
    readStarPIds_is_cleared = {
      isAryClear = true,
      targetFieldName = "readStarPIds",
    },
    receivedInfo = {
      isMap = true,
      keyName = "roleId",
    },
    receivedInfo_deleted = {
      isMapDel = true,
      targetFieldName = "receivedInfo",
    },
    receivedStarPIds = {
      isAry = true,
    },
    receivedStarPIds_is_cleared = {
      isAryClear = true,
      targetFieldName = "receivedStarPIds",
    },
  },
  StarPMailExtraInfoUnion = {
    notification = {
      isMsg = true,
    },
  },
  StarPMailItem = {
    itemId = {
    },
    itemNum = {
    },
    itemUserData = {
      isMsg = true,
    },
    seq = {
    },
  },
  StarPMailKey = {
    mailId = {
    },
    ownerId = {
    },
    starPId = {
    },
  },
  StarPMailState = {
    mailCount = {
    },
    mailType = {
    },
    unlessCount = {
    },
    unreadCount = {
    },
    unreceivedCount = {
    },
  },
  StarPMapIconInfo = {
    CommonId = {
    },
    ItemId = {
    },
    MapIconId = {
    },
    posInfo = {
      isMsg = true,
    },
    roleUId = {
    },
    starPId = {
    },
  },
  StarPMapPosBuilding = {
    beginDelayRemoveTimeStamp = {
    },
    BoxSettingInfo = {
    },
    buildProgCurValue = {
    },
    buildProgIncSpeed = {
    },
    buildRateOfProgress = {
    },
    configId = {
    },
    DefaultSeedId = {
    },
    dirX = {
    },
    dirY = {
    },
    dirZ = {
    },
    eggNum = {
    },
    equipCreatingList = {
      isMap = true,
      keyName = "orderUID",
    },
    equipCreatingList_deleted = {
      isMapDel = true,
      targetFieldName = "equipCreatingList",
    },
    farmPhase = {
    },
    FatherUID = {
    },
    FinishTime = {
    },
    foodId = {
    },
    foodPhase = {
    },
    foodTaskAllNum = {
    },
    foodTaskFinishNum = {
    },
    funcCounterCurValue = {
    },
    funcCounterTotal = {
    },
    funcIsInteracting = {
    },
    funcProgCurValue = {
    },
    FuncProgCurValue2 = {
    },
    funcProgIncSpeed = {
    },
    funcProgTotal = {
    },
    FuncProgTotal2 = {
    },
    funcSlot1PetUid = {
    },
    funcSlot2PetUid = {
    },
    funcSlot3PetUid = {
    },
    funcSlot4PetUid = {
    },
    funcSlot5PetUid = {
    },
    guildId = {
    },
    hp = {
    },
    id = {
    },
    IncubatorIsFinish = {
    },
    lastUpdateTime = {
    },
    layerInGraph = {
    },
    LightingStatus = {
    },
    manufacturingFormularId = {
    },
    MotherUID = {
    },
    OrderList = {
    },
    ownedGuildlUid = {
    },
    placementPlayer = {
    },
    placementTime = {
    },
    posX = {
    },
    posY = {
    },
    posZ = {
    },
    power = {
    },
    ProgCurValue2 = {
    },
    ProgTotal2 = {
    },
    seedId = {
    },
    StartTime = {
    },
    Status = {
    },
    TerminalTaskStatus = {
    },
    terminalUid = {
    },
    workSetPlayerUID = {
    },
    worldBoxUid = {
    },
  },
  StarPMapPosBuildingListInfo = {
    starPMapPosBuildingList = {
      isMap = true,
      keyName = "id",
    },
    starPMapPosBuildingList_deleted = {
      isMapDel = true,
      targetFieldName = "starPMapPosBuildingList",
    },
  },
  StarPMapPosDropItem = {
    AIActorUID = {
    },
    AttachAIType = {
    },
    CapsuleHalfHeight = {
    },
    EnableCollision = {
    },
    grid = {
    },
    isLock = {
    },
    IsOccupiedBySOCAI = {
    },
    itemId = {
    },
    itemInstId = {
    },
    itemNum = {
    },
    itemType = {
    },
    NetUpdateFrequency = {
    },
    obtainTime = {
    },
    period = {
    },
    sourcePointID = {
    },
    spawnFromParam = {
      isMsg = true,
    },
    starPItemDropInfo = {
      isMsg = true,
    },
    starPItemPickType = {
    },
    userData = {
      isMsg = true,
    },
  },
  StarPMapPosPoi = {
    userDataList = {
      isMap = true,
      keyName = "commonId",
    },
    userDataList_deleted = {
      isMapDel = true,
      targetFieldName = "userDataList",
    },
  },
  StarPMapPosPoiOneData = {
    buffer = {
    },
    commonId = {
    },
    updateTime = {
    },
  },
  StarPMapTag = {
    id = {
    },
    posX = {
    },
    posY = {
    },
    type = {
    },
  },
  StarPMember = {
    accountMaxLevel = {
    },
    dressUpInfo = {
      isMsg = true,
    },
    dsGroupID = {
    },
    dsState = {
    },
    enterTimeStamp = {
    },
    exitTimeStamp = {
    },
    friendUid = {
    },
    identity = {
    },
    inheritData = {
      isMsg = true,
    },
    isInheriting = {
    },
    isLoaded = {
    },
    lastDayEnterTimeStamp = {
    },
    lastHeartBeatTimeStamp = {
    },
    lastStarPWorldId = {
    },
    lastUpdateTime = {
    },
    leaveTimeStamp = {
    },
    level = {
    },
    mainTerminalUID = {
    },
    onlineState = {
    },
    originalStarPWorldId = {
    },
    playerPublicEquipments = {
      isMsg = true,
    },
    playerPublicProfileInfo = {
      isMsg = true,
    },
    preExitTimeStamp = {
    },
    professionID = {
    },
    recruitId = {
    },
    roleId = {
    },
    starPWorldIds = {
      isSet = true,
    },
    starPWorldIds_deleted = {
      isSetDel = true,
      targetFieldName = "starPWorldIds",
    },
    uid = {
    },
    version = {
    },
  },
  StarPMemberInfo = {
    members = {
      isMap = true,
      keyName = "roleId",
    },
    members_deleted = {
      isMapDel = true,
      targetFieldName = "members",
    },
  },
  StarPMemberPublicBrief = {
    dressItemInfo = {
      isMap = true,
      keyName = "dressUpType",
    },
    dressItemInfo_deleted = {
      isMapDel = true,
      targetFieldName = "dressItemInfo",
    },
    enterTimeStamp = {
    },
    exitTimeStamp = {
    },
    gender = {
    },
    nickname = {
    },
    onlineState = {
    },
    profile = {
    },
    roleId = {
    },
    roleLevel = {
    },
    state = {
    },
    uid = {
    },
  },
  StarPMemberPublicBriefInfo = {
    members = {
      isMap = true,
      keyName = "uid",
    },
    members_deleted = {
      isMapDel = true,
      targetFieldName = "members",
    },
  },
  StarPMigrateSnapshot = {
    migrateEventTime = {
    },
    migrateExpireTime = {
    },
    migrateLastCheckTime = {
    },
    migrateParam1 = {
    },
    migrateParam2 = {
    },
    migrateParam3 = {
    },
    migrateType = {
    },
  },
  StarPNearbyWorld = {
    dist = {
    },
    worldId = {
    },
  },
  StarPNotificationInfo = {
    notificationType = {
    },
  },
  StarPOfflineMailTaskData = {
    content = {
    },
    expireDay = {
    },
    hintId = {
    },
    itemId = {
    },
    itemNum = {
    },
    mailScope = {
    },
    mailType = {
    },
    roleId = {
    },
    sender = {
    },
    title = {
    },
  },
  StarPOfflineSOCClosedBehavior = {
    behaviorType = {
    },
    isOpen = {
    },
  },
  StarPOfflineSOCData = {
    itemData = {
      isMap = true,
      keyName = "itemId",
    },
    itemData_deleted = {
      isMapDel = true,
      targetFieldName = "itemData",
    },
    lastOfflineTime = {
    },
  },
  StarPOfflineSOCDispatchInfo = {
    endDispatchHAG = {
    },
    endDispatchHP = {
    },
    endDispatchSAN = {
    },
    endDispatchTime = {
    },
    sOCAITaskType = {
    },
    targetType = {
    },
    targetUID = {
    },
    time = {
    },
  },
  StarPOfflineSOCItemData = {
    itemId = {
    },
    rateInfo = {
      isMap = true,
      keyName = "rateType",
    },
    rateInfo_deleted = {
      isMapDel = true,
      targetFieldName = "rateInfo",
    },
  },
  StarPOfflineSOCItemRateInfo = {
    rateType = {
    },
    rateValue = {
    },
  },
  StarPOfflineTaskDataUnion = {
    content = {
    },
    mailData = {
      isMsg = true,
    },
  },
  StarPOfflineTaskInfo = {
    content = {
      isMsg = true,
    },
    type = {
    },
  },
  StarPOneAbilityCDInfo = {
    abilityId = {
    },
    beginTime = {
    },
    finishTime = {
    },
    ownerId = {
    },
    ownerType = {
    },
  },
  StarPOneBasePve = {
    bitStatus = {
    },
    lastTreasureBoxOpenTimeStamp = {
    },
    nextBattleTime = {
    },
    pWorldId = {
    },
  },
  StarPOneBattlePet = {
    petInstId = {
    },
  },
  StarPOneBehaviorData = {
    cropId = {
    },
    operateTime = {
    },
    visitorBehavior = {
    },
    visitorNickname = {
    },
    visitorUid = {
    },
  },
  StarPOneBuffInfo = {
    buffCount = {
    },
    buffId = {
    },
    buffLastTime = {
    },
    buffStartTime = {
    },
    dstId = {
    },
    dstType = {
    },
    srcId = {
    },
    srcType = {
    },
  },
  StarPOneCommonBasePve = {
    curPlayerNum = {
    },
    curRandomLevelId = {
    },
    isFinish = {
    },
    isFirstFinish = {
    },
    lastRefreshTime = {
    },
    pWorldId = {
    },
  },
  StarPOneContactRecord = {
    id = {
    },
    recordData = {
      isMsg = true,
    },
    type = {
    },
  },
  StarPOnePlayerSocInteractionRatioData = {
    awardRatio = {
    },
    uid = {
    },
  },
  StarPOneTeamPve = {
    entryId = {
    },
    isActive = {
    },
    passPworldId = {
    },
    unlockPworldId = {
    },
  },
  StarPOneTeamPveStatus = {
    bitStatus = {
    },
    lastTreasureBoxOpenTimeStamp = {
    },
    pWorldId = {
    },
  },
  StarPOwnerInfo = {
    isOpenLocation = {
    },
    lat = {
    },
    lon = {
    },
    nickName = {
    },
    openTime = {
    },
    partyOpen = {
    },
    uid = {
    },
  },
  StarPPassiveSkil = {
    skillId = {
    },
  },
  StarpPersonalDrop = {
    uid = {
    },
  },
  StarPPet = {
    abilities = {
      isMap = true,
      keyName = "id",
    },
    abilities_deleted = {
      isMapDel = true,
      targetFieldName = "abilities",
    },
    activeSkill = {
      isMap = true,
      keyName = "skillId",
    },
    activeSkill_deleted = {
      isMapDel = true,
      targetFieldName = "activeSkill",
    },
    attrInfo = {
      isMsg = true,
    },
    buffs = {
      isMap = true,
      keyName = "id",
    },
    buffs_deleted = {
      isMapDel = true,
      targetFieldName = "buffs",
    },
    passiveSkill = {
      isMap = true,
      keyName = "skillId",
    },
    passiveSkill_deleted = {
      isMapDel = true,
      targetFieldName = "passiveSkill",
    },
    petId = {
    },
    statusList = {
      isMap = true,
      keyName = "statusId",
    },
    statusList_deleted = {
      isMapDel = true,
      targetFieldName = "statusList",
    },
  },
  StarPPetAmulet = {
    amuletId = {
    },
    exp = {
    },
    level = {
    },
    petTypeId = {
    },
  },
  StarPPetAttrInfo = {
    affinity = {
    },
    atk = {
    },
    atkSp = {
    },
    attackPhPotential = {
    },
    attackSpPotential = {
    },
    baseStats = {
    },
    bossType = {
    },
    captureBallId = {
    },
    def = {
    },
    defensePhPotential = {
    },
    defenseSpPotential = {
    },
    defSp = {
    },
    exp = {
    },
    feedCnt = {
    },
    foodIntake = {
    },
    fromType = {
    },
    healthPoints = {
    },
    healthPotential = {
    },
    height = {
    },
    hunger = {
    },
    interactCnt = {
    },
    isFriendSkillUpgraded = {
    },
    isFriendWeapon = {
    },
    isMale = {
    },
    isRidden = {
    },
    iValues = {
    },
    level = {
    },
    maxHp = {
    },
    name = {
    },
    needSaveToDB = {
    },
    pos = {
      isMsg = true,
    },
    rarePassiveType = {
    },
    reviveTime = {
    },
    Rot = {
      isMsg = true,
    },
    sanityPoints = {
    },
    socSkill = {
    },
    starExp = {
    },
    starLevel = {
    },
    weight = {
    },
    workRate = {
    },
    workSpeedPotential = {
    },
  },
  StarPPetDexInfo = {
    createTime = {
    },
    petTypeId = {
    },
    RarityState = {
    },
    state = {
    },
  },
  StarPPetTeamFirstPosInfo = {
    levelInfo = {
    },
    petType = {
    },
  },
  StarPPetTradeFocusInfo = {
    focusUids = {
      isSet = true,
    },
    focusUids_deleted = {
      isSetDel = true,
      targetFieldName = "focusUids",
    },
  },
  StarPPetTradeWishInfo = {
    tradeNotes = {
    },
    tradePetIds = {
      isSet = true,
    },
    tradePetIds_deleted = {
      isSetDel = true,
      targetFieldName = "tradePetIds",
    },
    wishPetId = {
    },
  },
  StarPPetUserData = {
    starPPet = {
      isMsg = true,
    },
  },
  StarPPlayerAchievement = {
    conds = {
      isMap = true,
      keyName = "idx",
    },
    conds_deleted = {
      isMapDel = true,
      targetFieldName = "conds",
    },
    finishTime = {
    },
    id = {
    },
    state = {
    },
  },
  StarPPlayerAssistOrderInfo = {
    dailyAssistOrderSubmitCount = {
    },
    orderMap = {
      isMap = true,
      keyName = "orderId",
    },
    orderMap_deleted = {
      isMapDel = true,
      targetFieldName = "orderMap",
    },
    resetTimeMs = {
    },
    uid = {
    },
  },
  StarPPlayerAttr = {
    apply = {
      isMap = true,
      keyName = "starPWorldId",
    },
    apply_deleted = {
      isMapDel = true,
      targetFieldName = "apply",
    },
    assistOrderMap = {
      isMap = true,
      keyName = "orderId",
    },
    assistOrderMap_deleted = {
      isMapDel = true,
      targetFieldName = "assistOrderMap",
    },
    baseInfo = {
      isMsg = true,
    },
    collectStarPWorldIds = {
      isSet = true,
    },
    collectStarPWorldIds_deleted = {
      isSetDel = true,
      targetFieldName = "collectStarPWorldIds",
    },
    curRoleId = {
    },
    defaultDressUpInfo = {
      isMsg = true,
    },
    formationInfo = {
      isMsg = true,
    },
    grey = {
    },
    groupGetChatGroupPetEgg = {
    },
    groupInfo = {
      isMsg = true,
    },
    groupJoinChatGroup = {
    },
    guideStep = {
    },
    invitedHistory = {
      isMsg = true,
    },
    invitedMeMap = {
      isMsg = true,
    },
    invitedMemberMap = {
      isMsg = true,
    },
    lastCreateGuildTimeMs = {
    },
    lastDailyRefreshTimeMs = {
    },
    lastSelfDelUserTimeMs = {
    },
    lastWeekRefreshTimeMs = {
    },
    lastWorldId = {
    },
    materialAssistCnt = {
    },
    maxTerminalLevel = {
    },
    ownerStarPWorldIds = {
      isSet = true,
    },
    ownerStarPWorldIds_deleted = {
      isSetDel = true,
      targetFieldName = "ownerStarPWorldIds",
    },
    petTradeWishInfo = {
      isMsg = true,
    },
    playerMaxLevel = {
    },
    playerStarPWorlds = {
      isMap = true,
      keyName = "starPWorldId",
    },
    playerStarPWorlds_deleted = {
      isMapDel = true,
      targetFieldName = "playerStarPWorlds",
    },
    statisticsInfo = {
      isMsg = true,
    },
    user = {
      isMap = true,
      keyName = "roleId",
    },
    user_deleted = {
      isMapDel = true,
      targetFieldName = "user",
    },
    userPet = {
      isMsg = true,
    },
  },
  StarPPlayerBackCDStatusInfo = {
    cdTimeStamp = {
    },
  },
  StarPPlayerBaseInfo = {
    backPackIds = {
      isMap = true,
      keyName = "type",
    },
    backPackIds_deleted = {
      isMapDel = true,
      targetFieldName = "backPackIds",
    },
    backpackSizeInfo = {
      isMap = true,
      keyName = "backpackType",
    },
    backpackSizeInfo_deleted = {
      isMapDel = true,
      targetFieldName = "backpackSizeInfo",
    },
    battleTalentID = {
    },
    curPackageId = {
    },
    curSelectPetInstId = {
    },
    guildId = {
    },
    lastPos = {
      isMsg = true,
    },
    playerInfo = {
      isMsg = true,
    },
    professionID = {
    },
    registTime = {
    },
    terminalLevel = {
    },
    todayFirstLoginTime = {
    },
    totalLoginDays = {
    },
    totalOnlineTime = {
    },
    worldbackPackIds = {
      isMap = true,
      keyName = "starpId",
    },
    worldbackPackIds_deleted = {
      isMapDel = true,
      targetFieldName = "worldbackPackIds",
    },
    worldPlayerInfoMap = {
      isMap = true,
      keyName = "starPId",
    },
    worldPlayerInfoMap_deleted = {
      isMapDel = true,
      targetFieldName = "worldPlayerInfoMap",
    },
  },
  StarPPlayerBit = {
    bitInfo = {
      isMap = true,
      keyName = "bitType",
    },
    bitInfo_deleted = {
      isMapDel = true,
      targetFieldName = "bitInfo",
    },
    byteInfo = {
      isMap = true,
      keyName = "k",
    },
    byteInfo_deleted = {
      isMapDel = true,
      targetFieldName = "byteInfo",
    },
  },
  StarPPlayerBitInfo = {
    bitDetail = {
      isSet = true,
    },
    bitDetail_deleted = {
      isSetDel = true,
      targetFieldName = "bitDetail",
    },
    bitType = {
    },
  },
  StarPPlayerBriefData = {
    commonInfo = {
      isMap = true,
      keyName = "k",
    },
    commonInfo_deleted = {
      isMapDel = true,
      targetFieldName = "commonInfo",
    },
    finalRewardStatusGrowthPath = {
    },
    lastOpenTreasureChestTime = {
    },
    openedTreasureChestNum = {
    },
  },
  StarPPlayerCatchBossInfo = {
    id = {
    },
    monsterType = {
    },
    typeId = {
    },
  },
  StarPPlayerCatchPet = {
    catchPetInfo = {
      isMap = true,
      keyName = "petTypeId",
    },
    catchPetInfo_deleted = {
      isMapDel = true,
      targetFieldName = "catchPetInfo",
    },
  },
  StarPPlayerCatchPetInfo = {
    cnt = {
    },
    petTypeId = {
    },
  },
  StarPPlayerCondition = {
    collects = {
      isAry = true,
    },
    collects_is_cleared = {
      isAryClear = true,
      targetFieldName = "collects",
    },
    condType = {
    },
    idx = {
    },
    progress = {
    },
    state = {
    },
  },
  StarPPlayerContactRecord = {
    records = {
      isMap = true,
      keyName = "uid",
    },
    records_deleted = {
      isMapDel = true,
      targetFieldName = "records",
    },
  },
  StarPPlayerEnergyInfo = {
    curEnergy = {
    },
    lastRefreshTime = {
    },
    lastUpdateLv = {
    },
  },
  StarPPlayerFriendSkillData = {
    friendEnergy = {
    },
    isEnergyExhaustion = {
    },
    lastFriendSkillEndTime = {
    },
    petId = {
    },
  },
  StarPPlayerFunctionControl = {
    conds = {
      isMap = true,
      keyName = "idx",
    },
    conds_deleted = {
      isMapDel = true,
      targetFieldName = "conds",
    },
    id = {
    },
    newState = {
    },
    openSwitch = {
    },
    state = {
    },
    stateTime = {
    },
  },
  StarPPlayerGroupInfo = {
    groupId = {
    },
    groupName = {
    },
    joinGroupTime = {
    },
    title = {
    },
  },
  StarPPlayerGrowthPathGroup = {
    finishState = {
    },
    groupID = {
    },
    ifViewed = {
    },
    rewardState = {
    },
  },
  StarPPlayerGrowthPathMission = {
    completeConds = {
      isMap = true,
      keyName = "idx",
    },
    completeConds_deleted = {
      isMapDel = true,
      targetFieldName = "completeConds",
    },
    finishState = {
    },
    missionID = {
    },
    rewardState = {
    },
  },
  StarPPlayerGrowthPathSignInReward = {
    id = {
    },
    rewardState = {
    },
  },
  StarPPlayerGrowthPathSignInRewardInfo = {
    lastSignInTime = {
    },
    rewards = {
      isMap = true,
      keyName = "id",
    },
    rewards_deleted = {
      isMapDel = true,
      targetFieldName = "rewards",
    },
    totalSignInDays = {
    },
  },
  StarPPlayerIllustratedAwardInfo = {
    spAwardInfos = {
      isMap = true,
      keyName = "level",
    },
    spAwardInfos_deleted = {
      isMapDel = true,
      targetFieldName = "spAwardInfos",
    },
  },
  StarPPlayerInfo = {
    lastMailCheckId = {
    },
    pworldCatchBossInfo = {
      isMsg = true,
    },
    pworldEntranceId = {
    },
    pworldId = {
    },
    pworldInstId = {
    },
    pworldPos = {
      isMsg = true,
    },
    pworldRot = {
      isMsg = true,
    },
    pworldType = {
    },
    stateInfo = {
      isMap = true,
      keyName = "k",
    },
    stateInfo_deleted = {
      isMapDel = true,
      targetFieldName = "stateInfo",
    },
    worldPos = {
      isMsg = true,
    },
    worldRot = {
      isMsg = true,
    },
  },
  StarPPlayerIntimacyGeneralDBInfo = {
    intimacyExp = {
    },
    recentInteractMs = {
    },
    recentUpdateMs = {
    },
  },
  StarPPlayerIntimacyInfo = {
    friendInfos = {
      isMap = true,
      keyName = "friendUid",
    },
    friendInfos_deleted = {
      isMapDel = true,
      targetFieldName = "friendInfos",
    },
    resetTimeMs = {
    },
    uid = {
    },
  },
  StarPPlayerLastLevel = {
    lastLevel = {
    },
  },
  StarPPlayerLineConditionInfos = {
    condition = {
      isMsg = true,
    },
  },
  StarPPlayerLotteryGuaranteeInfo = {
    guaranteeType = {
    },
    rewardContent = {
      isAry = true,
    },
    rewardContent_is_cleared = {
      isAryClear = true,
      targetFieldName = "rewardContent",
    },
    rewardType = {
      isAry = true,
    },
    rewardType_is_cleared = {
      isAryClear = true,
      targetFieldName = "rewardType",
    },
    totalCnt = {
    },
  },
  StarPPlayerLotteryInfo = {
    guaranteeInfo = {
      isMap = true,
      keyName = "guaranteeType",
    },
    guaranteeInfo_deleted = {
      isMapDel = true,
      targetFieldName = "guaranteeInfo",
    },
    lotteryPoolId = {
    },
    lotteryRecords = {
      isMap = true,
      keyName = "curLotteryCnt",
    },
    lotteryRecords_deleted = {
      isMapDel = true,
      targetFieldName = "lotteryRecords",
    },
    periodCnt = {
    },
    totalCnt = {
    },
    uid = {
    },
  },
  StarPPlayerLotteryRecord = {
    curLotteryCnt = {
    },
    rewardId = {
    },
  },
  StarPPlayerOrderDbInfo = {
    commonFlushCounts = {
    },
    commonOrders = {
      isMap = true,
      keyName = "orderId",
    },
    commonOrders_deleted = {
      isMapDel = true,
      targetFieldName = "commonOrders",
    },
    rareFlushCounts = {
    },
    rareOrders = {
      isMap = true,
      keyName = "orderId",
    },
    rareOrders_deleted = {
      isMapDel = true,
      targetFieldName = "rareOrders",
    },
    specialOrderIDs = {
      isAry = true,
    },
    specialOrderIDs_is_cleared = {
      isAryClear = true,
      targetFieldName = "specialOrderIDs",
    },
  },
  StarPPlayerPetFeedInfo = {
    feedFoods = {
      isSet = true,
    },
    feedFoods_deleted = {
      isSetDel = true,
      targetFieldName = "feedFoods",
    },
    petId = {
    },
    uid = {
    },
  },
  StarPPlayerPoiState = {
    stateInfo = {
      isMap = true,
      keyName = "k",
    },
    stateInfo_deleted = {
      isMapDel = true,
      targetFieldName = "stateInfo",
    },
  },
  StarPPlayerPShop = {
    shops = {
      isMap = true,
      keyName = "shopType",
    },
    shops_deleted = {
      isMapDel = true,
      targetFieldName = "shops",
    },
    uid = {
    },
  },
  StarPPlayerPveData = {
    basePveData = {
      isMsg = true,
    },
    rLInfo = {
      isMsg = true,
    },
    teammates = {
      isMap = true,
      keyName = "uid",
    },
    teammates_deleted = {
      isMapDel = true,
      targetFieldName = "teammates",
    },
    teamPveData = {
      isMsg = true,
    },
  },
  StarPPlayerPvpDailyData = {
    dailyFirstBattle = {
    },
    dailyFirstBattleStatus = {
    },
    dailyFirstWin = {
    },
    dailyFirstWinStatus = {
    },
    dailyLoseCount = {
    },
    dailyTieCount = {
    },
    dailyWinCount = {
    },
    lastDailyRefreshTime = {
    },
  },
  StarPPlayerRoleAttr = {
    attrMap = {
      isMap = true,
      keyName = "attrKey",
    },
    attrMap_deleted = {
      isMapDel = true,
      targetFieldName = "attrMap",
    },
  },
  StarPPlayerSetting = {
    settingInfo = {
      isMap = true,
      keyName = "k",
    },
    settingInfo_deleted = {
      isMapDel = true,
      targetFieldName = "settingInfo",
    },
  },
  StarPPlayerSettingPC = {
    settingInfo = {
      isMap = true,
      keyName = "k",
    },
    settingInfo_deleted = {
      isMapDel = true,
      targetFieldName = "settingInfo",
    },
  },
  StarPPlayerSocInteractionRatioData = {
    lastRefreshTime = {
    },
    ratioDatas = {
      isMap = true,
      keyName = "uid",
    },
    ratioDatas_deleted = {
      isMapDel = true,
      targetFieldName = "ratioDatas",
    },
  },
  StarPPlayerStatisticsInfo = {
    catchPet = {
      isSet = true,
    },
    catchPet_deleted = {
      isSetDel = true,
      targetFieldName = "catchPet",
    },
    catchPetNum = {
    },
    showPet = {
      isSet = true,
    },
    showPet_deleted = {
      isSetDel = true,
      targetFieldName = "showPet",
    },
    teamFirstPet = {
    },
    updateTime = {
    },
  },
  StarPPlayerStoryLineInfo = {
    storyLineInfo = {
      isMap = true,
      keyName = "k",
    },
    storyLineInfo_deleted = {
      isMapDel = true,
      targetFieldName = "storyLineInfo",
    },
  },
  StarPPlayerTalentApplyPoint = {
    attrType = {
    },
    value = {
    },
  },
  StarPPlayerTalentData = {
    curProjectId = {
    },
    talentProjectList = {
      isMap = true,
      keyName = "projectId",
    },
    talentProjectList_deleted = {
      isMapDel = true,
      targetFieldName = "talentProjectList",
    },
  },
  StarPPlayerTalentProject = {
    applyPoint = {
      isMap = true,
      keyName = "attrType",
    },
    applyPoint_deleted = {
      isMapDel = true,
      targetFieldName = "applyPoint",
    },
    projectId = {
    },
  },
  StarPPlayerTask = {
    acceptConds = {
      isMap = true,
      keyName = "idx",
    },
    acceptConds_deleted = {
      isMapDel = true,
      targetFieldName = "acceptConds",
    },
    acceptTime = {
    },
    conds = {
      isMap = true,
      keyName = "idx",
    },
    conds_deleted = {
      isMapDel = true,
      targetFieldName = "conds",
    },
    finishTime = {
    },
    state = {
    },
    taskId = {
    },
  },
  StarPPlayerTaskBaseInfo = {
    activeRewardId = {
    },
    belongWorldId = {
    },
    curTracingId = {
    },
    temporaryTaskType = {
    },
    temporaryTaskUserData = {
    },
  },
  StarPPlayerTaskGroup = {
    groupId = {
    },
    isActive = {
    },
    lastRefreshTime = {
    },
  },
  StarPPlayerTaskGroupInfo = {
    taskGroup = {
      isMap = true,
      keyName = "groupId",
    },
    taskGroup_deleted = {
      isMapDel = true,
      targetFieldName = "taskGroup",
    },
  },
  StarPPlayerTaskInfo = {
    task = {
      isMap = true,
      keyName = "taskId",
    },
    task_deleted = {
      isMapDel = true,
      targetFieldName = "task",
    },
  },
  StarPPlayerTeamPveData = {
    selectPetInfos = {
      isMap = true,
      keyName = "petInstId",
    },
    selectPetInfos_deleted = {
      isMapDel = true,
      targetFieldName = "selectPetInfos",
    },
    teamId = {
    },
    teamPves = {
      isMap = true,
      keyName = "entryId",
    },
    teamPves_deleted = {
      isMapDel = true,
      targetFieldName = "teamPves",
    },
    worldStatus = {
      isMap = true,
      keyName = "pWorldId",
    },
    worldStatus_deleted = {
      isMapDel = true,
      targetFieldName = "worldStatus",
    },
  },
  StarPPlayerTech = {
    techStatus = {
      isMap = true,
      keyName = "techId",
    },
    techStatus_deleted = {
      isMapDel = true,
      targetFieldName = "techStatus",
    },
  },
  StarPPlayerWorldBaseInfo = {
    createTime = {
    },
    role = {
    },
    starPWorldId = {
    },
    starPWorldName = {
    },
    state = {
    },
  },
  StarPPlayMapBlockInfo = {
    bitInfo = {
      isAry = true,
    },
    bitInfo_is_cleared = {
      isAryClear = true,
      targetFieldName = "bitInfo",
    },
    sz = {
    },
  },
  StarPPlayMapInfo = {
    blockId = {
      isAry = true,
    },
    blockId_is_cleared = {
      isAryClear = true,
      targetFieldName = "blockId",
    },
    tag = {
      isMap = true,
      keyName = "id",
    },
    tag_deleted = {
      isMapDel = true,
      targetFieldName = "tag",
    },
  },
  StarPPos = {
    posX = {
    },
    posY = {
    },
    posZ = {
    },
  },
  StarPPrepareMigrateDsInfo = {
    dsAddr = {
    },
  },
  StarPPublicInfo = {
    avatar = {
    },
    avatarUrl = {
    },
    backup = {
    },
    createTime = {
    },
    desc = {
    },
    geoLocation = {
      isMsg = true,
    },
    hidden = {
    },
    image = {
    },
    isGrey = {
    },
    isOfficial = {
    },
    isOpenLocation = {
    },
    joinType = {
    },
    location = {
      isMsg = true,
    },
    member = {
      isAry = true,
    },
    member_is_cleared = {
      isAryClear = true,
      targetFieldName = "member",
    },
    memberFullRate = {
    },
    officialKey = {
    },
    officialType = {
    },
    open = {
    },
    pets = {
      isMap = true,
      keyName = "k",
    },
    pets_deleted = {
      isMapDel = true,
      targetFieldName = "pets",
    },
    roomName = {
    },
    starPWorldId = {
    },
    tags = {
      isAry = true,
    },
    tags_is_cleared = {
      isAryClear = true,
      targetFieldName = "tags",
    },
    uid = {
    },
    uidNickName = {
    },
    updateTime = {
    },
    worldLevel = {
    },
  },
  StarPPveFormationPetDetailInfo = {
    item = {
      isMsg = true,
    },
    petInfo = {
      isMsg = true,
    },
  },
  StarPPveFormationPetInfo = {
    backpackId = {
    },
    backpackType = {
    },
    petId = {
    },
    petInstId = {
    },
    seqId = {
    },
    starPId = {
    },
    type = {
    },
  },
  StarPPvpFormationInfo = {
    formationId = {
    },
    petInfo = {
      isMap = true,
      keyName = "seqId",
    },
    petInfo_deleted = {
      isMapDel = true,
      targetFieldName = "petInfo",
    },
  },
  StarPPvpUserData = {
    cancelCount = {
    },
    lastCancelTime = {
    },
    penaltyTime = {
    },
    totalFailCount = {
    },
    totalMatchCount = {
    },
    totalSuccCount = {
    },
    weeklyFailCount = {
    },
    weeklyMatchCount = {
    },
    weeklySuccCount = {
    },
  },
  StarPQualifyingTypeInfo = {
    maxDedegreeType = {
    },
    maxDegreeID = {
    },
    maxIntegral = {
    },
    maxStar = {
    },
    qualifyType = {
    },
    shocksRewardMap = {
      isMap = true,
      keyName = "degreeType",
    },
    shocksRewardMap_deleted = {
      isMapDel = true,
      targetFieldName = "shocksRewardMap",
    },
  },
  StarPReceiveInfo = {
    receiveTime = {
    },
    roleId = {
    },
    roleName = {
    },
    starPId = {
    },
    uid = {
    },
  },
  StarPRecentlyCompletedAchievements = {
    finishTime = {
    },
    id = {
    },
  },
  StarPRecentTeammate = {
    teammates = {
      isMap = true,
      keyName = "uid",
    },
    teammates_deleted = {
      isMapDel = true,
      targetFieldName = "teammates",
    },
  },
  StarPRecentTeammateInfo = {
    pWorldId = {
    },
    time = {
    },
    uid = {
    },
  },
  StarPRecoveryWorldData = {
    curTimeSeconds = {
    },
    dayPass = {
    },
    lastSaveTimeStamp = {
    },
  },
  StarPResourceBalanceTable = {
    resourceTable = {
      isMap = true,
      keyName = "changeReason",
    },
    resourceTable_deleted = {
      isMapDel = true,
      targetFieldName = "resourceTable",
    },
    uid = {
    },
  },
  StarPResourceControl = {
    businessType = {
    },
    businessUid = {
    },
    controlData = {
      isMap = true,
      keyName = "businessSubType",
    },
    controlData_deleted = {
      isMapDel = true,
      targetFieldName = "controlData",
    },
  },
  StarPResourceData = {
    activeTime = {
    },
    businessSubType = {
    },
    itemMap = {
      isMap = true,
      keyName = "itemId",
    },
    itemMap_deleted = {
      isMapDel = true,
      targetFieldName = "itemMap",
    },
  },
  StarPResourceItem = {
    itemId = {
    },
    itemValue = {
    },
  },
  StarPResourceTableData = {
    changeReason = {
    },
    itemMap = {
      isMap = true,
      keyName = "itemId",
    },
    itemMap_deleted = {
      isMapDel = true,
      targetFieldName = "itemMap",
    },
    subReason = {
    },
  },
  StarPRLInfo = {
    buffIds = {
      isAry = true,
    },
    buffIds_is_cleared = {
      isAryClear = true,
      targetFieldName = "buffIds",
    },
    buffResetTimes = {
    },
    chooseBuffIds = {
      isAry = true,
    },
    chooseBuffIds_is_cleared = {
      isAryClear = true,
      targetFieldName = "chooseBuffIds",
    },
    chooseBuffResetTimes = {
    },
    lockStatus = {
    },
    receiveTowerIds = {
      isAry = true,
    },
    receiveTowerIds_is_cleared = {
      isAryClear = true,
      targetFieldName = "receiveTowerIds",
    },
    towerId = {
    },
    weekRewardStatus = {
    },
  },
  StarPRot = {
    rotX = {
    },
    rotY = {
    },
    rotZ = {
    },
  },
  StarPSeed = {
    croplandType = {
    },
    itemID = {
    },
    itemNum = {
    },
  },
  StarPSeedBox = {
    controlField = {
      isSet = true,
    },
    controlField_deleted = {
      isSetDel = true,
      targetFieldName = "controlField",
    },
    id = {
    },
  },
  StarPShocksDegreeIDInfo = {
    degreeID = {
    },
    status = {
    },
  },
  StarPShocksRewardInfo = {
    degreeRewardMap = {
      isMap = true,
      keyName = "degreeID",
    },
    degreeRewardMap_deleted = {
      isMapDel = true,
      targetFieldName = "degreeRewardMap",
    },
    degreeType = {
    },
  },
  StarPShop = {
    itemsRecords = {
      isMap = true,
      keyName = "configId",
    },
    itemsRecords_deleted = {
      isMapDel = true,
      targetFieldName = "itemsRecords",
    },
    shopType = {
    },
  },
  StarPShopItemRecord = {
    buyNum = {
    },
    buyTotalNum = {
    },
    configId = {
    },
    itemId = {
    },
    lastBuyTime = {
    },
    lastUpdateTime = {
    },
  },
  StarPSimpleItemUserData = {
    dump = {
    },
    foodUpdateTime = {
    },
  },
  StarPSkill = {
    skillId = {
    },
    slotId = {
    },
    unlockLevel = {
    },
  },
  StarPSOCDormancyData = {
    breedInfos = {
      isMap = true,
      keyName = "uid",
    },
    breedInfos_deleted = {
      isMapDel = true,
      targetFieldName = "breedInfos",
    },
    infos = {
      isMap = true,
      keyName = "uid",
    },
    infos_deleted = {
      isMapDel = true,
      targetFieldName = "infos",
    },
    offlineTimeMS = {
    },
    startDormancyTime = {
    },
    terminalId = {
    },
  },
  StarPSocInteractionData = {
    friendInteractInfo = {
      isMap = true,
      keyName = "friendUid",
    },
    friendInteractInfo_deleted = {
      isMapDel = true,
      targetFieldName = "friendInteractInfo",
    },
    interactCounts = {
    },
    interactPoints = {
      isMap = true,
      keyName = "mainTerminalId",
    },
    interactPoints_deleted = {
      isMapDel = true,
      targetFieldName = "interactPoints",
    },
  },
  StarPSocInteractRecord = {
    items = {
      isMap = true,
      keyName = "itemId",
    },
    items_deleted = {
      isMapDel = true,
      targetFieldName = "items",
    },
    socId = {
    },
    time = {
    },
    uid = {
    },
  },
  StarPStarInfo = {
    totalStarCount = {
    },
  },
  StarPStatisticsInfo = {
    cacheActiveTime = {
    },
    isRemoveFromCache = {
    },
    lastDayCapturedPetNum = {
    },
    lastDayJoinMemberNum = {
    },
    lastRemoveFromCacheCnt = {
    },
    onUpdateFailedCnt = {
    },
    petTypeId = {
      isAry = true,
    },
    petTypeId_is_cleared = {
      isAryClear = true,
      targetFieldName = "petTypeId",
    },
  },
  StarPStatusInfo = {
    startTime = {
    },
    statusId = {
    },
  },
  StarPStorylineRewardData = {
    rewardInfo = {
      isMap = true,
      keyName = "stepIndex",
    },
    rewardInfo_deleted = {
      isMapDel = true,
      targetFieldName = "rewardInfo",
    },
    storyID = {
    },
  },
  StarPStoryRewardInfo = {
    dropBagID = {
    },
    stepIndex = {
    },
  },
  StarPTaskSeq = {
    content = {
    },
    flag = {
    },
  },
  StarPTaskSeqInfo = {
    currentSeq = {
    },
  },
  StarPTaskSeqWhite = {
    whiteFlag = {
    },
  },
  StarpTeamDrop = {
    uids = {
      isAry = true,
    },
    uids_is_cleared = {
      isAryClear = true,
      targetFieldName = "uids",
    },
  },
  StarPTechStatus = {
    status = {
    },
    techId = {
    },
  },
  StarPTerminalLevel = {
    terminalLevel = {
    },
  },
  StarPThingKey = {
    itemId = {
    },
    itemInstId = {
    },
  },
  StarPTimeRefresh = {
    lastRefreshTm = {
    },
    refreshId = {
    },
  },
  StarPTowerAbilityInfo = {
    attrType = {
    },
    value = {
    },
  },
  StarPTradeOrder = {
    orderAccTime = {
    },
    orderFlushTime = {
    },
    orderId = {
    },
    orderType = {
    },
  },
  StarPTrader = {
    dump = {
    },
  },
  StarPTutorialEntryInfo = {
    bIsNew = {
    },
    dateTime = {
    },
    tutorialID = {
    },
  },
  StarPTutorialInfoList = {
    tutorialList = {
      isMap = true,
      keyName = "tutorialID",
    },
    tutorialList_deleted = {
      isMapDel = true,
      targetFieldName = "tutorialList",
    },
  },
  StarPUidMember = {
    roleId = {
    },
    state = {
    },
    uid = {
    },
  },
  StarPUidMemberInfo = {
    members = {
      isMap = true,
      keyName = "uid",
    },
    members_deleted = {
      isMapDel = true,
      targetFieldName = "members",
    },
  },
  StarPUnLockedEquip = {
    unLockedEquipInfoSet = {
      isMap = true,
      keyName = "equipId",
    },
    unLockedEquipInfoSet_deleted = {
      isMapDel = true,
      targetFieldName = "unLockedEquipInfoSet",
    },
    unLockedEquipSet = {
      isSet = true,
    },
    unLockedEquipSet_deleted = {
      isSetDel = true,
      targetFieldName = "unLockedEquipSet",
    },
  },
  StarPUser = {
    apply = {
      isMap = true,
      keyName = "starPWorldId",
    },
    apply_deleted = {
      isMapDel = true,
      targetFieldName = "apply",
    },
    blessCount = {
    },
    blessedUids = {
      isSet = true,
    },
    blessedUids_deleted = {
      isSetDel = true,
      targetFieldName = "blessedUids",
    },
    createTime = {
    },
    grey = {
    },
    initEquipStatus = {
    },
    isOpenBless = {
    },
    lastCrossTime = {
    },
    lastCrossWorld = {
    },
    lastEnterSPTime = {
    },
    lastLoginOutTime = {
    },
    lastLoginTime = {
    },
    lastSelfDelUserTimeMs = {
    },
    lastStarPWorldId = {
    },
    laststateOptTime = {
    },
    lastVisitorTime = {
    },
    level = {
    },
    mainStarPWorldId = {
    },
    name = {
    },
    professionID = {
    },
    recentStarPWorldId = {
    },
    roleId = {
    },
    roleSlotIndex = {
    },
    starPWorldIdSet = {
      isMap = true,
      keyName = "starPWorldId",
    },
    starPWorldIdSet_deleted = {
      isMapDel = true,
      targetFieldName = "starPWorldIdSet",
    },
    starPWorldInfoSet = {
      isMap = true,
      keyName = "starPWorldId",
    },
    starPWorldInfoSet_deleted = {
      isMapDel = true,
      targetFieldName = "starPWorldInfoSet",
    },
    state = {
    },
    userDressUpInfo = {
      isMsg = true,
    },
    userItemData = {
      isMap = true,
      keyName = "itemInstId",
    },
    userItemData_deleted = {
      isMapDel = true,
      targetFieldName = "userItemData",
    },
    visits = {
    },
  },
  StarPUserDressUpInfo = {
    dressUpCfgId = {
    },
    lastUpdateTime = {
    },
    openDressUpRoom = {
    },
  },
  StarPUserPet = {
    petAmulet = {
      isMap = true,
      keyName = "amuletId",
    },
    petAmulet_deleted = {
      isMapDel = true,
      targetFieldName = "petAmulet",
    },
    petAmuletCurrency = {
    },
    petDex = {
      isMap = true,
      keyName = "petTypeId",
    },
    petDex_deleted = {
      isMapDel = true,
      targetFieldName = "petDex",
    },
  },
  StarPUserWorldBaseInfo = {
    createTime = {
    },
    guildId = {
    },
    lastLoginOutTime = {
    },
    lastLoginTime = {
    },
    starPWorldId = {
    },
    starPWorldName = {
    },
  },
  StarPUserWorldInfo = {
    createTime = {
    },
    guildId = {
    },
    isUpdateLastEnterWorld = {
    },
    lastLoginOutTime = {
    },
    lastLoginTime = {
    },
    level = {
    },
    role = {
    },
    starPWorldId = {
    },
    starPWorldName = {
    },
    state = {
    },
  },
  StarPVisitorBehaviorRecord = {
    behaviorRecords = {
      isMap = true,
      keyName = "operateTime",
    },
    behaviorRecords_deleted = {
      isMapDel = true,
      targetFieldName = "behaviorRecords",
    },
  },
  StarPVisitorData = {
    blessStatueData = {
      isMsg = true,
    },
    giftData = {
      isMsg = true,
    },
    socInteractionData = {
      isMsg = true,
    },
  },
  StarPVisitorItemData = {
    itemCount = {
    },
    itemId = {
    },
  },
  StarPVisitorTempData = {
    visitorTempRecords = {
      isMap = true,
      keyName = "visitorUid",
    },
    visitorTempRecords_deleted = {
      isMapDel = true,
      targetFieldName = "visitorTempRecords",
    },
  },
  StarPVisitorTemporyRecord = {
    initTime = {
    },
    recordId = {
    },
    visitorBlessData = {
      isMap = true,
      keyName = "itemId",
    },
    visitorBlessData_deleted = {
      isMapDel = true,
      targetFieldName = "visitorBlessData",
    },
    visitorCropsData = {
      isMap = true,
      keyName = "itemId",
    },
    visitorCropsData_deleted = {
      isMapDel = true,
      targetFieldName = "visitorCropsData",
    },
    visitorUid = {
    },
  },
  StarPVisitRecord = {
    time = {
    },
    uid = {
    },
  },
  StarPWeaponUserData = {
    AmmoNum = {
    },
    SPVirtualAmmoNum = {
    },
  },
  StarPWishStatueData = {
    extraBuff = {
    },
    godPower = {
    },
    lastRefreshTime = {
    },
    luckBuff = {
    },
    todayGetPower = {
    },
    uid = {
    },
    visitUids = {
      isAry = true,
    },
    visitUids_is_cleared = {
      isAryClear = true,
      targetFieldName = "visitUids",
    },
  },
  StarPWorldChatGroup = {
    createTime = {
    },
    guildId = {
    },
    starPId = {
    },
    starPWorldName = {
    },
    worldChatGroupKey = {
      isMsg = true,
    },
  },
  StarPWorldDSLastCatchPet = {
    todayCatch = {
      isMsg = true,
    },
    yesterdayCatch = {
      isMsg = true,
    },
  },
  StarPWorldIndex = {
    getPage = {
    },
    pageIndex = {
    },
    starPId = {
    },
  },
  StarPWorldLastRole = {
    roleId = {
    },
    starPWorldId = {
    },
  },
  StarPWorldLevelBaseInfo = {
    curDonateTimes = {
    },
    curLevelRankListData = {
      isMsg = true,
    },
    level = {
    },
  },
  StarPWorldLevelCurGuildInfo = {
    donateTimes = {
    },
    guildId = {
    },
    latestDonateTime = {
    },
    memberList = {
      isMap = true,
      keyName = "uid",
    },
    memberList_deleted = {
      isMapDel = true,
      targetFieldName = "memberList",
    },
  },
  StarPWorldLevelCurGuildMemberInfo = {
    donateTimes = {
    },
    uid = {
    },
  },
  StarPWorldLevelCurRankList = {
    guildCurRankList = {
      isMap = true,
      keyName = "guildId",
    },
    guildCurRankList_deleted = {
      isMapDel = true,
      targetFieldName = "guildCurRankList",
    },
  },
  StarPWorldLevelData = {
    levelInfo = {
    },
  },
  StarPWorldLevelGuildInfo = {
    dropId = {
    },
    guildId = {
    },
    guildName = {
    },
    memberList = {
      isAry = true,
    },
    memberList_is_cleared = {
      isAryClear = true,
      targetFieldName = "memberList",
    },
    rank = {
    },
  },
  StarPWorldLevelRankList = {
    guildRankList = {
      isMap = true,
      keyName = "guildId",
    },
    guildRankList_deleted = {
      isMapDel = true,
      targetFieldName = "guildRankList",
    },
    level = {
    },
  },
  StarPWorldLevelUpRecord = {
    worldLevelInfo = {
      isMap = true,
      keyName = "level",
    },
    worldLevelInfo_deleted = {
      isMapDel = true,
      targetFieldName = "worldLevelInfo",
    },
  },
  StarPWorldOrnamentInfo = {
    curPage = {
    },
    getPage = {
    },
    starPId = {
    },
    totalCount = {
    },
    userData = {
      isMap = true,
      keyName = "itemInstId",
    },
    userData_deleted = {
      isMapDel = true,
      targetFieldName = "userData",
    },
  },
  StarPWorldPetInfo = {
    curPage = {
    },
    getPage = {
    },
    starPId = {
    },
    totalCount = {
    },
    userData = {
      isMap = true,
      keyName = "itemInstId",
    },
    userData_deleted = {
      isMapDel = true,
      targetFieldName = "userData",
    },
  },
  StarPWorldPlayerInfo = {
    backpackId = {
    },
    backpackType = {
    },
    grid = {
    },
    item = {
      isMsg = true,
    },
    itemId = {
    },
    itemInstId = {
    },
  },
  StarPWorldPveData = {
    commonBasePve = {
      isMap = true,
      keyName = "pWorldId",
    },
    commonBasePve_deleted = {
      isMapDel = true,
      targetFieldName = "commonBasePve",
    },
  },
  StarPWorldRoleInfo = {
    lastMailCheckId = {
    },
    pworldCatchBossInfo = {
      isMsg = true,
    },
    pworldEntranceId = {
    },
    pworldId = {
    },
    pworldInstId = {
    },
    pworldPos = {
      isMsg = true,
    },
    pworldRot = {
      isMsg = true,
    },
    pworldType = {
    },
    starPId = {
    },
    stateInfo = {
      isMap = true,
      keyName = "k",
    },
    stateInfo_deleted = {
      isMapDel = true,
      targetFieldName = "stateInfo",
    },
    worldPos = {
      isMsg = true,
    },
    worldRot = {
      isMsg = true,
    },
  },
  StarPWorldTimeRefresh = {
    lastRefreshTm = {
    },
    refreshId = {
    },
  },
  StarWorldDetailInfo = {
    backupMapIds = {
      isAry = true,
    },
    backupMapIds_is_cleared = {
      isAryClear = true,
      targetFieldName = "backupMapIds",
    },
    changeMapCount = {
    },
    curMapId = {
    },
    difficulty = {
    },
    failCount = {
    },
    info = {
      isMsg = true,
    },
    reward = {
    },
  },
  StarWorldStepInfo = {
    firstStarInfo = {
      isMsg = true,
    },
    isPass = {
    },
    lastStar = {
    },
    secondStarInfo = {
      isMsg = true,
    },
    stepId = {
    },
  },
  StatCluster = {
    dailyGraph = {
      isMap = true,
      keyName = "timeKey",
    },
    dailyGraph_deleted = {
      isMapDel = true,
      targetFieldName = "dailyGraph",
    },
    dailyKeyOrigin = {
    },
  },
  StatGraph = {
    onlineTimeMs = {
    },
    timeKey = {
    },
  },
  StealingRecord = {
    friendId = {
    },
    updateTime = {
    },
  },
  StickerChapterData = {
    bingoRewards = {
      isSet = true,
    },
    bingoRewards_deleted = {
      isSetDel = true,
      targetFieldName = "bingoRewards",
    },
    chapterId = {
    },
    chapterRewarded = {
    },
    drawCount = {
    },
    normalRewards = {
      isSet = true,
    },
    normalRewards_deleted = {
      isSetDel = true,
      targetFieldName = "normalRewards",
    },
  },
  StickerData = {
    chapters = {
      isMap = true,
      keyName = "chapterId",
    },
    chapters_deleted = {
      isMapDel = true,
      targetFieldName = "chapters",
    },
  },
  StickFriendInfo = {
    friends = {
      isAry = true,
    },
    friends_is_cleared = {
      isAryClear = true,
      targetFieldName = "friends",
    },
    scene = {
    },
  },
  StolenInfo = {
    id = {
    },
    itemIdList = {
      isAry = true,
    },
    itemIdList_is_cleared = {
      isAryClear = true,
      targetFieldName = "itemIdList",
    },
    itemNumList = {
      isAry = true,
    },
    itemNumList_is_cleared = {
      isAryClear = true,
      targetFieldName = "itemNumList",
    },
    loseFarmCoin = {
    },
    timeMs = {
    },
  },
  StolenStat = {
    cdExpireMs = {
    },
    currCompensation = {
    },
    incrId = {
    },
    newFlag = {
    },
    nextCalcMs = {
    },
    records = {
      isMap = true,
      keyName = "id",
    },
    records_deleted = {
      isMapDel = true,
      targetFieldName = "records",
    },
  },
  StreamSetting = {
    lastStreamTimestamp = {
    },
    onStream = {
    },
    playState = {
    },
    streamPlatType = {
    },
    streamPlayInfoId = {
    },
    streamRight = {
    },
    streamToken = {
    },
  },
  StrStrPair = {
    id = {
    },
    value = {
    },
  },
  SubReasonData = {
    changeReservedParams = {
      isAry = true,
    },
    changeReservedParams_is_cleared = {
      isAryClear = true,
      targetFieldName = "changeReservedParams",
    },
    subReason = {
    },
  },
  SubscribeQqRobt = {
    hasSubscribe = {
    },
    queryTimeMs = {
    },
  },
  SummerFlashMobData = {
    taskWeek = {
    },
  },
  SummerNavigationBaJiReward = {
    status = {
    },
    taskId = {
    },
  },
  SummerNavigationBarData = {
    baJiRewardInfo = {
      isMap = true,
      keyName = "taskId",
    },
    baJiRewardInfo_deleted = {
      isMapDel = true,
      targetFieldName = "baJiRewardInfo",
    },
    taskNewRedDot = {
      isSet = true,
    },
    taskNewRedDot_deleted = {
      isSetDel = true,
      targetFieldName = "taskNewRedDot",
    },
  },
  SummerVacationBPData = {
    exp = {
    },
    lastLevel = {
    },
    nowLevel = {
    },
    rewardedBuff = {
      isSet = true,
    },
    rewardedBuff_deleted = {
      isSetDel = true,
      targetFieldName = "rewardedBuff",
    },
    rewardedLevel = {
      isSet = true,
    },
    rewardedLevel_deleted = {
      isSetDel = true,
      targetFieldName = "rewardedLevel",
    },
  },
  SuperCoreRankActivityData = {
    initActiveDays = {
    },
    initTotalRmb = {
    },
    itemConsumes = {
      isMap = true,
      keyName = "id",
    },
    itemConsumes_deleted = {
      isMapDel = true,
      targetFieldName = "itemConsumes",
    },
    lastSuccReportRankScore = {
    },
  },
  SuperCoreRankActivityItemConsumeData = {
    id = {
    },
    num = {
    },
  },
  SuperLinearRedeem = {
    currentValue = {
    },
    rewardInfo = {
      isMap = true,
      keyName = "rewardId",
    },
    rewardInfo_deleted = {
      isMapDel = true,
      targetFieldName = "rewardInfo",
    },
  },
  TakeawayActivity = {
    ackReceivedDaily = {
    },
    ackRewardAtCount = {
      isSet = true,
    },
    ackRewardAtCount_deleted = {
      isSetDel = true,
      targetFieldName = "ackRewardAtCount",
    },
    ackRewardBoxIds = {
      isSet = true,
    },
    ackRewardBoxIds_deleted = {
      isSetDel = true,
      targetFieldName = "ackRewardBoxIds",
    },
    ackUpdateTs = {
    },
    invitationId = {
    },
    unlockingBoxId = {
    },
    unlockUntilTs = {
    },
  },
  TakeawayInviteeInfo = {
    supportedUids = {
      isSet = true,
    },
    supportedUids_deleted = {
      isSetDel = true,
      targetFieldName = "supportedUids",
    },
    ts = {
    },
  },
  TargetEquipInfo = {
    posItemInfo = {
      isMsg = true,
    },
    targetId = {
    },
  },
  Task = {
    conditionGroup = {
      isMsg = true,
    },
    id = {
    },
    lastCompleteTime = {
    },
    lastTriggerTime = {
    },
    repeatNum = {
    },
    rewardLock = {
    },
    status = {
    },
    timeInfo = {
      isMsg = true,
    },
  },
  TaskCompleteInfo = {
    completeTimeMs = {
      isSet = true,
    },
    completeTimeMs_deleted = {
      isSetDel = true,
      targetFieldName = "completeTimeMs",
    },
    taskId = {
    },
  },
  TaskExtraInfo = {
    creativeTaskInfo = {
      isMsg = true,
    },
    lobbyTaskInfo = {
      isMsg = true,
    },
    newbieTaskInfo = {
      isMsg = true,
    },
    returnTaskInfo = {
      isMsg = true,
    },
    ugcTaskInfo = {
      isMsg = true,
    },
  },
  TaskFinish = {
    completeTime = {
    },
    id = {
    },
  },
  TaskInfo = {
    extraInfo = {
      isMsg = true,
    },
    finishTask = {
      isMap = true,
      keyName = "id",
    },
    finishTask_deleted = {
      isMapDel = true,
      targetFieldName = "finishTask",
    },
    lastRefreshTime = {
      isMap = true,
      keyName = "taskType",
    },
    lastRefreshTime_deleted = {
      isMapDel = true,
      targetFieldName = "lastRefreshTime",
    },
    optionalReward = {
      isMap = true,
      keyName = "taskId",
    },
    optionalReward_deleted = {
      isMapDel = true,
      targetFieldName = "optionalReward",
    },
    runningTask = {
      isMap = true,
      keyName = "id",
    },
    runningTask_deleted = {
      isMapDel = true,
      targetFieldName = "runningTask",
    },
    version = {
    },
  },
  TaskLifeTime = {
    doBeginTime = {
    },
    doEndTime = {
    },
    showBeginTime = {
    },
    showEndTime = {
    },
  },
  TaskRefreshTime = {
    taskType = {
    },
    time = {
    },
  },
  TaskTimeInfo = {
    notUseAttrTime = {
    },
    taskLifeTime = {
      isMsg = true,
    },
  },
  TeamRankActivity = {
    teamRankTimes = {
      isMap = true,
      keyName = "id",
    },
    teamRankTimes_deleted = {
      isMapDel = true,
      targetFieldName = "teamRankTimes",
    },
  },
  TeamRankInfo = {
    id = {
    },
    updateTime = {
    },
    usedTimes = {
    },
  },
  TestActTeamData = {
    score = {
    },
    teamId = {
    },
  },
  ThemeAdventureDailyTaskData = {
    gameId = {
    },
    maxScore = {
    },
    rewardConfigIndex = {
      isSet = true,
    },
    rewardConfigIndex_deleted = {
      isSetDel = true,
      targetFieldName = "rewardConfigIndex",
    },
    score = {
    },
  },
  ThemeAdventureData = {
    dailyTaskData = {
      isMap = true,
      keyName = "gameId",
    },
    dailyTaskData_deleted = {
      isMapDel = true,
      targetFieldName = "dailyTaskData",
    },
    lastRefreshDailyTaskTimeMs = {
    },
    rewardUpgradeData = {
      isMap = true,
      keyName = "id",
    },
    rewardUpgradeData_deleted = {
      isMapDel = true,
      targetFieldName = "rewardUpgradeData",
    },
  },
  ThemeAdventureRewardUpgradeData = {
    finalItemId = {
    },
    id = {
    },
    itemId = {
      isAry = true,
    },
    itemId_is_cleared = {
      isAryClear = true,
      targetFieldName = "itemId",
    },
    orderNo = {
    },
    rewarded = {
    },
    sourceItemId = {
    },
  },
  ThemeMallInfo = {
    buyNum = {
    },
    discount = {
    },
    id = {
    },
  },
  TimeLimitedCheckInActivity = {
    checkInAttr = {
      isMap = true,
      keyName = "configIndex",
    },
    checkInAttr_deleted = {
      isMapDel = true,
      targetFieldName = "checkInAttr",
    },
    lastCheckInTimeMs = {
    },
    unlockTimeMs = {
    },
  },
  TimeLimitedCheckInAttr = {
    checkInCount = {
    },
    configIndex = {
    },
    rewardedCheckInCount = {
      isSet = true,
    },
    rewardedCheckInCount_deleted = {
      isSetDel = true,
      targetFieldName = "rewardedCheckInCount",
    },
  },
  Timeslot = {
    timeEnd = {
    },
    timeStart = {
    },
  },
  TradingCardBubbleSendInfo = {
    bubbleType = {
    },
    lastSendTime = {
    },
  },
  TradingCardClearRedDot = {
    redDotIds = {
      isSet = true,
    },
    redDotIds_deleted = {
      isSetDel = true,
      targetFieldName = "redDotIds",
    },
    redDotType = {
    },
    updateTimeMs = {
    },
  },
  TradingCardCollectionCardInfo = {
    id = {
    },
    tradingCardInfo = {
      isMap = true,
      keyName = "id",
    },
    tradingCardInfo_deleted = {
      isMapDel = true,
      targetFieldName = "tradingCardInfo",
    },
  },
  TradingCardCollectionCardInfos = {
    loaded = {
    },
    tradingCardCollectionInfo = {
      isMap = true,
      keyName = "id",
    },
    tradingCardCollectionInfo_deleted = {
      isMapDel = true,
      targetFieldName = "tradingCardCollectionInfo",
    },
  },
  TradingCardCollectionInfo = {
    id = {
    },
    star = {
    },
  },
  TradingCardCollectionInfos = {
    tradingCardCollectionInfo = {
      isMap = true,
      keyName = "id",
    },
    tradingCardCollectionInfo_deleted = {
      isMapDel = true,
      targetFieldName = "tradingCardCollectionInfo",
    },
  },
  TradingCardCollectionRedDot = {
    collectionId = {
    },
    tradingCardRedDot = {
      isMap = true,
      keyName = "redDotType",
    },
    tradingCardRedDot_deleted = {
      isMapDel = true,
      targetFieldName = "tradingCardRedDot",
    },
  },
  TradingCardCollectionTag = {
    id = {
    },
    tag = {
    },
  },
  TradingCardCycleCup = {
    id = {
    },
    progress = {
    },
    realProgress = {
    },
    realWeekAddProgress = {
    },
    sendMailReward = {
    },
    stageId = {
    },
    totalProgress = {
    },
    tradingCardCycleCupStageRecord = {
      isMap = true,
      keyName = "id",
    },
    tradingCardCycleCupStageRecord_deleted = {
      isMapDel = true,
      targetFieldName = "tradingCardCycleCupStageRecord",
    },
    updateTimeMS = {
    },
    weekAddProgress = {
    },
  },
  TradingCardCycleCupStageRecord = {
    drawCount = {
    },
    id = {
    },
  },
  TradingCardData = {
    cardList = {
      isMap = true,
      keyName = "id",
    },
    cardList_deleted = {
      isMapDel = true,
      targetFieldName = "cardList",
    },
    collectionRedDots = {
      isMap = true,
      keyName = "collectionId",
    },
    collectionRedDots_deleted = {
      isMapDel = true,
      targetFieldName = "collectionRedDots",
    },
    dailyGiveTimes = {
    },
    extCardBagOutPutData = {
      isMap = true,
      keyName = "Id",
    },
    extCardBagOutPutData_deleted = {
      isMapDel = true,
      targetFieldName = "extCardBagOutPutData",
    },
    frozenCardList = {
      isMap = true,
      keyName = "tradeId",
    },
    frozenCardList_deleted = {
      isMapDel = true,
      targetFieldName = "frozenCardList",
    },
    historyCollectionId = {
      isSet = true,
    },
    historyCollectionId_deleted = {
      isSetDel = true,
      targetFieldName = "historyCollectionId",
    },
    lastRequireTimeMs = {
    },
    lastShareChatTimeMap = {
      isMap = true,
      keyName = "tradeId",
    },
    lastShareChatTimeMap_deleted = {
      isMapDel = true,
      targetFieldName = "lastShareChatTimeMap",
    },
    newCardCollectionRedDot = {
      isMsg = true,
    },
    rewardFlag = {
      isMsg = true,
    },
    selfTradeInfoList = {
      isMap = true,
      keyName = "id",
    },
    selfTradeInfoList_deleted = {
      isMapDel = true,
      targetFieldName = "selfTradeInfoList",
    },
    tradingCardBubbleSendInfo = {
      isMap = true,
      keyName = "bubbleType",
    },
    tradingCardBubbleSendInfo_deleted = {
      isMapDel = true,
      targetFieldName = "tradingCardBubbleSendInfo",
    },
    tradingCardCollectionTag = {
      isMap = true,
      keyName = "id",
    },
    tradingCardCollectionTag_deleted = {
      isMapDel = true,
      targetFieldName = "tradingCardCollectionTag",
    },
    tradingCardExchange = {
      isMap = true,
      keyName = "id",
    },
    tradingCardExchange_deleted = {
      isMapDel = true,
      targetFieldName = "tradingCardExchange",
    },
    tradingCardNoviceReward = {
      isMap = true,
      keyName = "collectionId",
    },
    tradingCardNoviceReward_deleted = {
      isMapDel = true,
      targetFieldName = "tradingCardNoviceReward",
    },
    tradingCardRankData = {
      isMap = true,
      keyName = "collectionId",
    },
    tradingCardRankData_deleted = {
      isMapDel = true,
      targetFieldName = "tradingCardRankData",
    },
    tradingCardRedDot = {
      isMap = true,
      keyName = "redDotType",
    },
    tradingCardRedDot_deleted = {
      isMapDel = true,
      targetFieldName = "tradingCardRedDot",
    },
    tradingCycleCup = {
      isMap = true,
      keyName = "id",
    },
    tradingCycleCup_deleted = {
      isMapDel = true,
      targetFieldName = "tradingCycleCup",
    },
    wildCardList = {
      isMap = true,
      keyName = "uuid",
    },
    wildCardList_deleted = {
      isMapDel = true,
      targetFieldName = "wildCardList",
    },
  },
  TradingCardExchange = {
    id = {
    },
    tradingCardExchangeInfo = {
      isMap = true,
      keyName = "id",
    },
    tradingCardExchangeInfo_deleted = {
      isMapDel = true,
      targetFieldName = "tradingCardExchangeInfo",
    },
  },
  TradingCardExchangeInfo = {
    exchangeCount = {
    },
    id = {
    },
    lastExchangeTimeMs = {
    },
  },
  TradingCardInfo = {
    id = {
    },
    num = {
    },
  },
  TradingCardNoviceRewardInfo = {
    collectionId = {
    },
    draw = {
    },
    rewardId = {
    },
  },
  TradingCardRankData = {
    collectionId = {
    },
    star = {
    },
    tradCardNum = {
    },
  },
  TradingCardRedDot = {
    redDotIds = {
      isSet = true,
    },
    redDotIds_deleted = {
      isSetDel = true,
      targetFieldName = "redDotIds",
    },
    redDotType = {
    },
  },
  TradingCardRewardFlag = {
    rewardedCardCollectionId = {
      isSet = true,
    },
    rewardedCardCollectionId_deleted = {
      isSetDel = true,
      targetFieldName = "rewardedCardCollectionId",
    },
    rewardedCardDeckId = {
      isSet = true,
    },
    rewardedCardDeckId_deleted = {
      isSetDel = true,
      targetFieldName = "rewardedCardDeckId",
    },
  },
  TravelingDogData = {
    activityId = {
    },
    allCoinNum = {
    },
    dogData = {
      isMap = true,
      keyName = "date",
    },
    dogData_deleted = {
      isMapDel = true,
      targetFieldName = "dogData",
    },
    dogDate = {
    },
    dogStatus = {
    },
    goHomeTime = {
    },
    loginTime = {
    },
  },
  TravelingDogInfoData = {
    date = {
    },
    id = {
    },
  },
  TreasureHuntHistoryData = {
    excavateEpochSecs = {
    },
    itemCnt = {
    },
    itemId = {
    },
  },
  TreasureLevelUpData = {
    boxId = {
    },
    boxStatus = {
    },
    energy = {
    },
    lastRefreshTime = {
    },
  },
  TrophyTaskCompleteInfo = {
    completePlayerUid = {
    },
    completeTimestampMs = {
    },
    taskId = {
    },
  },
  TwoPeopleSquadMemberData = {
    battleTimes = {
    },
    lastBattleEpochMillis = {
    },
    lastOnlineEpochMillsRefreshEveryDay = {
    },
    loginEpochMillis = {
    },
    uid = {
    },
  },
  TwoPeopleSquadTaskCompleteData = {
    completeEpochMillis = {
    },
    completePlayerUid = {
    },
  },
  TwoPeopleSquadTaskData = {
    completeData = {
      isMap = true,
      keyName = "completePlayerUid",
    },
    completeData_deleted = {
      isMapDel = true,
      targetFieldName = "completeData",
    },
    taskId = {
    },
  },
  TYCFpsSettings = {
    aimBtnRotate = {
    },
    aimCameraSensitivity = {
    },
    aimGyroSensitivity = {
    },
    cameraSensitivity = {
    },
    gameMode = {
    },
    gyroSensitivity = {
    },
    isSetByPlayer = {
    },
    joyStickMode = {
    },
    leftAimBtn = {
    },
    openGyro = {
    },
    shootMode = {
    },
    showSniperScope = {
    },
    sniperFireMode = {
    },
    viewMode = {
    },
  },
  TycoonBuildingItem = {
    tycoonBuildingConfId = {
    },
    tycoonBuildingConfVersion = {
    },
    tycoonBuildingItemConfId = {
    },
    tycoonBuildingLevel = {
    },
  },
  TycoonUserBasicInfo = {
    tycoonLastLoginUnixSecs = {
    },
    tycoonLastLogoutUnixSecs = {
    },
    tycoonNormalLevel = {
    },
    tycoonPeakLevel = {
    },
    tycoonRegisterUnixSecs = {
    },
  },
  TycoonUserBuildingInfo = {
    tycoonBuildingMap = {
      isMap = true,
      keyName = "tycoonBuildingConfId",
    },
    tycoonBuildingMap_deleted = {
      isMapDel = true,
      targetFieldName = "tycoonBuildingMap",
    },
    tycoonBuildingTemplateId = {
    },
  },
  TycoonUserCurrencyInfo = {
    tycoonUserBankCurrency = {
    },
    tycoonUserCurCrystals = {
    },
    tycoonUserCurCurrency = {
    },
  },
  TycoonUserDBInfo = {
    tycoonUserBackpackInfo = {
      isMsg = true,
    },
    tycoonUserBasicInfo = {
      isMsg = true,
    },
    tycoonUserBuildingInfo = {
      isMsg = true,
    },
    tycoonUserCurrencyInfo = {
      isMsg = true,
    },
    tycoonUserMonsterInfo = {
      isMsg = true,
    },
    tycoonUserStatInfo = {
      isMsg = true,
    },
  },
  TycoonUserMonsterInfo = {
    curBuildingHp = {
    },
    guildSchedule = {
    },
    hasWaveReward = {
    },
    param1 = {
    },
    rebirthBossIdx = {
    },
    rebirthBossReward = {
    },
    rebirthMonsterIdx = {
    },
    rebirthMonsterPrice = {
    },
    rebirthMonsterReward = {
    },
    remainSecs4Quit = {
    },
    smallWaveId = {
    },
    waveRebirthTimes = {
    },
    waveSize = {
    },
    waveStatus4Quit = {
    },
  },
  TycoonUserStatInfo = {
    tycoonUserCollectNum = {
    },
    tycoonUserCurBuildingPercentInReincarnation = {
    },
    tycoonUserCurKillMonsterNumInReincarnation = {
    },
    tycoonUserCurWaveNumInReincarnation = {
    },
    tycoonUserKillMonsterNum = {
    },
    tycoonUserKillUserNum = {
    },
    tycoonUserMaxBuildingPercentInReincarnation = {
    },
    tycoonUserMaxKillMonsterNumInReincarnation = {
    },
    tycoonUserMaxWaveNumInReincarnation = {
    },
    tycoonUserReincarnationNum = {
    },
  },
  UgcAccountInfo = {
    draftCap = {
    },
    fansCount = {
    },
    publishedCap = {
    },
    publishGoodsProtoVersion = {
    },
    subCount = {
    },
  },
  UgcActivityInfo = {
    appreciateScore = {
    },
    scoredMapCount = {
    },
  },
  UgcBadgeInfo = {
    addTime = {
    },
    id = {
    },
    redDot = {
    },
  },
  UgcBuyGoodsDeliverFailOrder = {
    addTime = {
    },
    billNo = {
    },
    busBillNo = {
    },
    checkCode = {
    },
    costDiamonds = {
    },
    goodsInfo = {
      isMap = true,
      keyName = "goodsId",
    },
    goodsInfo_deleted = {
      isMapDel = true,
      targetFieldName = "goodsInfo",
    },
    mapId = {
    },
    reason = {
    },
    retryNum = {
    },
  },
  UgcBuyGoodsInfo = {
    failOrder = {
      isMap = true,
      keyName = "billNo",
    },
    failOrder_deleted = {
      isMapDel = true,
      targetFieldName = "failOrder",
    },
  },
  UgcCollection = {
    collected = {
      isSet = true,
    },
    collected_deleted = {
      isSetDel = true,
      targetFieldName = "collected",
    },
    collectedDigest = {
      isMap = true,
      keyName = "collectionId",
    },
    collectedDigest_deleted = {
      isMapDel = true,
      targetFieldName = "collectedDigest",
    },
    created = {
      isSet = true,
    },
    created_deleted = {
      isSetDel = true,
      targetFieldName = "created",
    },
    createdDigest = {
      isMap = true,
      keyName = "collectionId",
    },
    createdDigest_deleted = {
      isMapDel = true,
      targetFieldName = "createdDigest",
    },
    createIndex = {
    },
  },
  UgcCollectionDigest = {
    collectedTimes = {
    },
    collectionId = {
    },
    createMs = {
    },
    mapCount = {
    },
    name = {
    },
    playPv = {
    },
    playUv = {
    },
    updateMs = {
    },
  },
  UgcCommonInfo = {
    resIds = {
      isAry = true,
    },
    resIds_is_cleared = {
      isAryClear = true,
      targetFieldName = "resIds",
    },
  },
  UgcCoPlayInfo = {
    coPlayRecords = {
      isMap = true,
      keyName = "playKey",
    },
    coPlayRecords_deleted = {
      isMapDel = true,
      targetFieldName = "coPlayRecords",
    },
  },
  UgcCoPlayRecord = {
    playKey = {
    },
    playRoomId = {
    },
    playTime = {
    },
    playTimes = {
    },
    playUgcId = {
    },
    playUid = {
    },
  },
  UgcCreativeTaskInfo = {
    unlockStage = {
    },
  },
  UgcDailyStage = {
    changeMapCount = {
    },
    lastDayResetTime = {
    },
    resetCount = {
    },
    roundId = {
    },
    stepInfos = {
      isMap = true,
      keyName = "stepId",
    },
    stepInfos_deleted = {
      isMapDel = true,
      targetFieldName = "stepInfos",
    },
  },
  UgcDanMu = {
    danMuAble = {
    },
    danMuAlpha = {
    },
    danMuArea = {
    },
    danMuFont = {
    },
    danMuSpeed = {
    },
  },
  UgcDeliverGoodsInfo = {
    farmInfo = {
      isMsg = true,
    },
    goodsId = {
    },
    num = {
    },
  },
  UgcGroup = {
    md5 = {
    },
    name = {
    },
    size = {
    },
    uid = {
    },
  },
  UgcGroupActorList = {
    actorId = {
    },
    id = {
    },
    num = {
    },
  },
  UgcGroupIdList = {
    groupId = {
      isAry = true,
    },
    groupId_is_cleared = {
      isAryClear = true,
      targetFieldName = "groupId",
    },
    groupIdNum = {
      isMap = true,
      keyName = "id",
    },
    groupIdNum_deleted = {
      isMapDel = true,
      targetFieldName = "groupIdNum",
    },
  },
  UgcGrowUpInfo = {
    communityMaxLikeScore = {
    },
    lastRefreshTime = {
    },
    reissueBatchId = {
    },
    ugcBadgeInfoMap = {
      isMap = true,
      keyName = "id",
    },
    ugcBadgeInfoMap_deleted = {
      isMapDel = true,
      targetFieldName = "ugcBadgeInfoMap",
    },
    weeklyActivityDegree = {
    },
  },
  UgcIdMap = {
    id = {
    },
    idType = {
    },
  },
  UgcMapCreatorInfo = {
    avatar = {
    },
    creatorId = {
    },
    dressItemInfos = {
      isMap = true,
      keyName = "dressUpType",
    },
    dressItemInfos_deleted = {
      isMapDel = true,
      targetFieldName = "dressItemInfos",
    },
    editorType = {
    },
    nickName = {
    },
  },
  UgcMapInfo = {
    bucket = {
    },
    chunkGroupIdList = {
      isAry = true,
    },
    chunkGroupIdList_is_cleared = {
      isAryClear = true,
      targetFieldName = "chunkGroupIdList",
    },
    fileName = {
    },
    gameCamParam = {
      isAry = true,
    },
    gameCamParam_is_cleared = {
      isAryClear = true,
      targetFieldName = "gameCamParam",
    },
    loadingInfo = {
      isMsg = true,
    },
    mapKey = {
    },
    msg = {
    },
    preMsg = {
    },
    ugcId = {
    },
    version = {
    },
  },
  UgcMapLoadingInfo = {
    creatorInfo = {
      isMap = true,
      keyName = "creatorId",
    },
    creatorInfo_deleted = {
      isMapDel = true,
      targetFieldName = "creatorInfo",
    },
    loadingTemplateId = {
    },
    tags = {
    },
  },
  UgcMapSetInfo = {
    agreementStatus = {
    },
    aigcBucket = {
    },
    aigcCountLimit = {
      isMsg = true,
    },
    bucket = {
    },
    canUpdatePublish = {
    },
    commonBucket = {
    },
    composeObjectInfo = {
      isSet = true,
    },
    composeObjectInfo_deleted = {
      isSetDel = true,
      targetFieldName = "composeObjectInfo",
    },
    copyId = {
    },
    createChatGroupFlag = {
    },
    editAngle = {
    },
    editHomeId = {
    },
    groupFreeObjectId = {
      isSet = true,
    },
    groupFreeObjectId_deleted = {
      isSetDel = true,
      targetFieldName = "groupFreeObjectId",
    },
    groupObjectInfo = {
      isMap = true,
      keyName = "uid",
    },
    groupObjectInfo_deleted = {
      isMapDel = true,
      targetFieldName = "groupObjectInfo",
    },
    homeId = {
    },
    idMap = {
      isMap = true,
      keyName = "idType",
    },
    idMap_deleted = {
      isMapDel = true,
      targetFieldName = "idMap",
    },
    isSyncAllocInfo = {
    },
    logFlag = {
    },
    mapKey = {
    },
    musicSize = {
    },
    netDisplay = {
    },
    objectInfo = {
      isSet = true,
    },
    objectInfo_deleted = {
      isSetDel = true,
      targetFieldName = "objectInfo",
    },
    openAiAnicapFlag = {
    },
    openAiAnswerFlag = {
    },
    openAiFlag = {
    },
    openAiImageFlag = {
    },
    openAiVoiceFlag = {
    },
    openCustomSkeletonAnimFlag = {
    },
    openDialogueImageFlag = {
    },
    openSkillEditorFlag = {
    },
    openVisualProgramFlag = {
    },
    operateAid = {
    },
    operateMap = {
      isMap = true,
      keyName = "opType",
    },
    operateMap_deleted = {
      isMapDel = true,
      targetFieldName = "operateMap",
    },
    platWhiteSwitch = {
      isMap = true,
      keyName = "moduleType",
    },
    platWhiteSwitch_deleted = {
      isMapDel = true,
      targetFieldName = "platWhiteSwitch",
    },
    publishId = {
    },
    publishIdMap = {
      isSet = true,
    },
    publishIdMap_deleted = {
      isSetDel = true,
      targetFieldName = "publishIdMap",
    },
    saveIdMap = {
      isSet = true,
    },
    saveIdMap_deleted = {
      isSetDel = true,
      targetFieldName = "saveIdMap",
    },
    singleStageInfo = {
      isMsg = true,
    },
    soundSize = {
    },
    ugcDanMu = {
      isMsg = true,
    },
    ugcSave = {
      isMap = true,
      keyName = "keyName",
    },
    ugcSave_deleted = {
      isMapDel = true,
      targetFieldName = "ugcSave",
    },
    ugcTestPlayerCount = {
    },
    ugcTestPlayers = {
      isMap = true,
      keyName = "uid",
    },
    ugcTestPlayers_deleted = {
      isMapDel = true,
      targetFieldName = "ugcTestPlayers",
    },
    uniqueId = {
    },
  },
  UgcMatchInfo = {
    matchRecords = {
      isMap = true,
      keyName = "playId",
    },
    matchRecords_deleted = {
      isMapDel = true,
      targetFieldName = "matchRecords",
    },
  },
  UgcMatchRecord = {
    playId = {
    },
    playName = {
    },
    playTime = {
    },
    playUgcId = {
    },
    playUgcName = {
    },
  },
  UgcMiniGamePlayInfo = {
    playRecords = {
      isMap = true,
      keyName = "playId",
    },
    playRecords_deleted = {
      isMapDel = true,
      targetFieldName = "playRecords",
    },
  },
  UgcOperate = {
    onceUgcSet = {
      isSet = true,
    },
    onceUgcSet_deleted = {
      isSetDel = true,
      targetFieldName = "onceUgcSet",
    },
    opType = {
    },
    ugcSet = {
      isSet = true,
    },
    ugcSet_deleted = {
      isSetDel = true,
      targetFieldName = "ugcSet",
    },
  },
  UgcOpInfo = {
    getSubPlayerMapsTs = {
    },
    homeRecommendHotTags = {
      isAry = true,
    },
    homeRecommendHotTags_is_cleared = {
      isAryClear = true,
      targetFieldName = "homeRecommendHotTags",
    },
    lastGetCollectStarActivityMapsTs = {
    },
    lastGetSubPlayerMapsTs = {
    },
    lastHomePageTabId = {
    },
    lastHotPlayMaps = {
      isSet = true,
    },
    lastHotPlayMaps_deleted = {
      isSetDel = true,
      targetFieldName = "lastHotPlayMaps",
    },
    lastOpenFansTime = {
    },
    lastOpenStartWorldTimeMs = {
    },
    lastOpenSubsTime = {
    },
    lastRecommendedMaps = {
      isSet = true,
    },
    lastRecommendedMaps_deleted = {
      isSetDel = true,
      targetFieldName = "lastRecommendedMaps",
    },
    lastSendUgcBpCoinTipsTs = {
    },
    nextDailyReportTimeMs = {
    },
    nextHotMapsReportTimeMs = {
    },
    openFansTime = {
    },
    openSubsTime = {
    },
  },
  UgcSave = {
    keyName = {
    },
    value = {
    },
  },
  UgcSlotInfo = {
    slotCount = {
    },
    slotInfo = {
      isMap = true,
      keyName = "slotKey",
    },
    slotInfo_deleted = {
      isMapDel = true,
      targetFieldName = "slotInfo",
    },
  },
  UgcSlotKeyInfo = {
    info = {
    },
    slotKey = {
    },
  },
  UgcStarWorld = {
    difficultyGroupId = {
      isAry = true,
    },
    difficultyGroupId_is_cleared = {
      isAryClear = true,
      targetFieldName = "difficultyGroupId",
    },
    lastRefreshTime = {
    },
    redDot = {
    },
    stepId = {
    },
    stepInfo = {
      isMap = true,
      keyName = "stepId",
    },
    stepInfo_deleted = {
      isMapDel = true,
      targetFieldName = "stepInfo",
    },
    todayGoldCoinNum = {
    },
  },
  UgcTaskDeduplication = {
    taskId = {
    },
    values = {
      isSet = true,
    },
    values_deleted = {
      isSetDel = true,
      targetFieldName = "values",
    },
  },
  UgcTaskInfo = {
    appreciateScore = {
    },
    appreciateScoreTotal = {
    },
    lastEnterLobbyId = {
    },
    lastEnterLobbyMs = {
    },
    playBlueTopicTask = {
      isMap = true,
      keyName = "topicId",
    },
    playBlueTopicTask_deleted = {
      isMapDel = true,
      targetFieldName = "playBlueTopicTask",
    },
    playGoldTopicTask = {
      isMap = true,
      keyName = "topicId",
    },
    playGoldTopicTask_deleted = {
      isMapDel = true,
      targetFieldName = "playGoldTopicTask",
    },
    ugcMapPeakPlayUv = {
    },
    ugcMapTotalPlayUv = {
    },
    ugcNewYearMapPassed = {
      isSet = true,
    },
    ugcNewYearMapPassed_deleted = {
      isSetDel = true,
      targetFieldName = "ugcNewYearMapPassed",
    },
    ugcTaskDeduplication = {
      isMap = true,
      keyName = "taskId",
    },
    ugcTaskDeduplication_deleted = {
      isMapDel = true,
      targetFieldName = "ugcTaskDeduplication",
    },
  },
  UgcTestPlayerInfo = {
    lastTestTime = {
    },
    uid = {
    },
  },
  UID = {
    least = {
    },
    most = {
    },
  },
  UltramanThemeActivity = {
    hasAchievedReward = {
      isSet = true,
    },
    hasAchievedReward_deleted = {
      isSetDel = true,
      targetFieldName = "hasAchievedReward",
    },
    hasVisitedStory = {
      isSet = true,
    },
    hasVisitedStory_deleted = {
      isSetDel = true,
      targetFieldName = "hasVisitedStory",
    },
    isInTeam = {
    },
    selfCollectionProgress = {
    },
  },
  UltramanThemeTeam = {
    friendshipVal = {
    },
    friendshipValDayEd = {
    },
    friendshipValDaySt = {
    },
    friendshipValTeam = {
    },
    isCaptain = {
    },
    joinTeam = {
    },
    joinTeamSec = {
    },
    members = {
      isSet = true,
    },
    members_deleted = {
      isSetDel = true,
      targetFieldName = "members",
    },
    teamId = {
    },
    yesterdayEndMs = {
    },
  },
  UpgradeAwardInfo = {
    id = {
    },
    isGetRewards = {
    },
  },
  UpgradeCheckInInfo = {
    id = {
    },
    isGetBaseRewards = {
    },
    isGetHigherRewards = {
    },
  },
  UpgradeCheckInManualActivity = {
    checkInCount = {
    },
    checkInList = {
      isMap = true,
      keyName = "id",
    },
    checkInList_deleted = {
      isMapDel = true,
      targetFieldName = "checkInList",
    },
    isUpgrade = {
    },
    lastCheckInTimeMs = {
    },
  },
  UseItemShareActivity = {
    changeRecord = {
      isMap = true,
      keyName = "id",
    },
    changeRecord_deleted = {
      isMapDel = true,
      targetFieldName = "changeRecord",
    },
    shareCount = {
    },
    shareRecord = {
      isMap = true,
      keyName = "shareId",
    },
    shareRecord_deleted = {
      isMapDel = true,
      targetFieldName = "shareRecord",
    },
  },
  UseItemShareRecord = {
    shareId = {
    },
    shareTimeMs = {
    },
    shareUrl = {
    },
  },
  UserActivityAttr = {
    activity = {
      isMsg = true,
    },
    fiveClockRefreshTimeMs = {
    },
    lastMidnightRefreshTimeMs = {
    },
    lastWeekRefreshTimeMs = {
    },
    raffle = {
      isMsg = true,
    },
    rewardCompensate = {
      isMsg = true,
    },
    task = {
      isMsg = true,
    },
  },
  UserAllSeasonNoteBookAttr = {
    battleDataRelocateTimeMs = {
    },
    clearZeroBattleDataSeasonId = {
      isSet = true,
    },
    clearZeroBattleDataSeasonId_deleted = {
      isSetDel = true,
      targetFieldName = "clearZeroBattleDataSeasonId",
    },
    clearZeroBattleMetaDataSeasonId = {
      isSet = true,
    },
    clearZeroBattleMetaDataSeasonId_deleted = {
      isSetDel = true,
      targetFieldName = "clearZeroBattleMetaDataSeasonId",
    },
    removeOldBattleMetaDataTimeMs = {
    },
    seasonBattleData = {
      isMap = true,
      keyName = "seasonId",
    },
    seasonBattleData_deleted = {
      isMapDel = true,
      targetFieldName = "seasonBattleData",
    },
  },
  UserAttr = {
    abTestInfo = {
      isMap = true,
      keyName = "testType",
    },
    abTestInfo_deleted = {
      isMapDel = true,
      targetFieldName = "abTestInfo",
    },
    achievementInfo = {
      isMsg = true,
    },
    activityAddNewAidInfo = {
      isMsg = true,
    },
    activityCenter = {
      isMsg = true,
    },
    activitySquad = {
      isMsg = true,
    },
    activitySquadDetails = {
      isMap = true,
      keyName = "activityId",
    },
    activitySquadDetails_deleted = {
      isMapDel = true,
      targetFieldName = "activitySquadDetails",
    },
    aiChatInfo = {
      isMsg = true,
    },
    album = {
      isMsg = true,
    },
    algoRecommendMatchInfo = {
      isMsg = true,
    },
    allSeasonNoteBookAttr = {
      isMsg = true,
    },
    appearanceRoad = {
      isMsg = true,
    },
    arenaGameData = {
      isMsg = true,
    },
    arenaSettings = {
      isMsg = true,
    },
    arenaTipInfo = {
      isMsg = true,
    },
    bagInfo = {
      isMsg = true,
    },
    basicInfo = {
      isMsg = true,
    },
    battleInfo = {
      isMsg = true,
    },
    benefitCardInfo = {
      isMsg = true,
    },
    birthdayData = {
      isMsg = true,
    },
    biSeq = {
      isMsg = true,
    },
    bpInfo = {
      isMap = true,
      keyName = "type",
    },
    bpInfo_deleted = {
      isMapDel = true,
      targetFieldName = "bpInfo",
    },
    bubble = {
      isMsg = true,
    },
    chaseGameData = {
      isMsg = true,
    },
    chaseGameDataNoPublic = {
      isMsg = true,
    },
    clientCacheData = {
      isMap = true,
      keyName = "id",
    },
    clientCacheData_deleted = {
      isMapDel = true,
      targetFieldName = "clientCacheData",
    },
    clientLogColoring = {
      isMap = true,
      keyName = "stainId",
    },
    clientLogColoring_deleted = {
      isMapDel = true,
      targetFieldName = "clientLogColoring",
    },
    clientPakInfo = {
      isMsg = true,
    },
    clubInfo = {
      isMsg = true,
    },
    cocModeInfo = {
      isMsg = true,
    },
    collectionInfo = {
      isMap = true,
      keyName = "collectionType",
    },
    collectionInfo_deleted = {
      isMapDel = true,
      targetFieldName = "collectionInfo",
    },
    combinationSettings = {
      isMsg = true,
    },
    commonConditionAttr = {
      isMap = true,
      keyName = "contionGroupType",
    },
    commonConditionAttr_deleted = {
      isMapDel = true,
      targetFieldName = "commonConditionAttr",
    },
    commonGiftBuyInfo = {
      isMap = true,
      keyName = "id",
    },
    commonGiftBuyInfo_deleted = {
      isMapDel = true,
      targetFieldName = "commonGiftBuyInfo",
    },
    commonLimit = {
      isMap = true,
      keyName = "limitType",
    },
    commonLimit_deleted = {
      isMapDel = true,
      targetFieldName = "commonLimit",
    },
    concertData = {
      isMsg = true,
    },
    cookInfo = {
      isMsg = true,
    },
    cupsInfo = {
      isMsg = true,
    },
    dailyAwardSumBuyInfoMap = {
      isMap = true,
      keyName = "typeId",
    },
    dailyAwardSumBuyInfoMap_deleted = {
      isMapDel = true,
      targetFieldName = "dailyAwardSumBuyInfoMap",
    },
    dailyVictoryRecord = {
      isMap = true,
      keyName = "dateKey",
    },
    dailyVictoryRecord_deleted = {
      isMapDel = true,
      targetFieldName = "dailyVictoryRecord",
    },
    danceData = {
      isMsg = true,
    },
    displayBoard = {
      isMsg = true,
    },
    dsUserDBInfoMap = {
      isMap = true,
      keyName = "matchType",
    },
    dsUserDBInfoMap_deleted = {
      isMapDel = true,
      targetFieldName = "dsUserDBInfoMap",
    },
    entertainmentGuide = {
      isMsg = true,
    },
    exchangeCenter = {
      isMsg = true,
    },
    farmDailySumBuyTimes = {
    },
    farmInfo = {
      isMsg = true,
    },
    farmReturningInfo = {
      isMsg = true,
    },
    fashionSkilluse = {
      isSet = true,
    },
    fashionSkilluse_deleted = {
      isSetDel = true,
      targetFieldName = "fashionSkilluse",
    },
    fireworksInfo = {
      isMsg = true,
    },
    fittingSlots = {
      isMsg = true,
    },
    fixTag = {
      isMap = true,
      keyName = "k",
    },
    fixTag_deleted = {
      isMapDel = true,
      targetFieldName = "fixTag",
    },
    flashRaceCheerAi = {
      isMap = true,
      keyName = "k",
    },
    flashRaceCheerAi_deleted = {
      isMapDel = true,
      targetFieldName = "flashRaceCheerAi",
    },
    forceOpenMatchType = {
      isSet = true,
    },
    forceOpenMatchType_deleted = {
      isSetDel = true,
      targetFieldName = "forceOpenMatchType",
    },
    fpsSettings = {
      isMsg = true,
    },
    friendRecommendInfo = {
      isMsg = true,
    },
    gameReturnModeData = {
      isMsg = true,
    },
    gameTimesStatistics = {
      isMsg = true,
    },
    gameTvInfo = {
      isMsg = true,
    },
    generalRedDotMap = {
      isMap = true,
      keyName = "moduleType",
    },
    generalRedDotMap_deleted = {
      isMapDel = true,
      targetFieldName = "generalRedDotMap",
    },
    grayTagsInfo = {
      isMsg = true,
    },
    guidedDiscoverInfo = {
      isMsg = true,
    },
    guideStatistics = {
      isMsg = true,
    },
    guideTasks = {
      isSet = true,
    },
    guideTasks_deleted = {
      isSetDel = true,
      targetFieldName = "guideTasks",
    },
    hokAttrInfo = {
      isMsg = true,
    },
    hokSettings = {
      isMsg = true,
    },
    houseInfo = {
      isMsg = true,
    },
    iaaData = {
      isMsg = true,
    },
    iaaInfo = {
      isMap = true,
      keyName = "id",
    },
    iaaInfo_deleted = {
      isMapDel = true,
      targetFieldName = "iaaInfo",
    },
    idipInfo = {
      isMsg = true,
    },
    idipTaskInfo = {
      isMsg = true,
    },
    intellectualActivity = {
      isMsg = true,
    },
    interactions = {
      isMap = true,
      keyName = "pos",
    },
    interactions_deleted = {
      isMapDel = true,
      targetFieldName = "interactions",
    },
    itaBagData = {
      isMap = true,
      keyName = "itemUUID",
    },
    itaBagData_deleted = {
      isMapDel = true,
      targetFieldName = "itaBagData",
    },
    itemInfo = {
      isMsg = true,
    },
    itemPackageLimit = {
      isMap = true,
      keyName = "itemId",
    },
    itemPackageLimit_deleted = {
      isMapDel = true,
      targetFieldName = "itemPackageLimit",
    },
    lastDressOutLook = {
      isMap = true,
      keyName = "baseDressOutLook",
    },
    lastDressOutLook_deleted = {
      isMapDel = true,
      targetFieldName = "lastDressOutLook",
    },
    lastUpdateMainVersionTimeMs = {
    },
    levelIllustration = {
      isMsg = true,
    },
    levelRecord = {
      isMsg = true,
    },
    limitTimeExperienceItem = {
      isMsg = true,
    },
    lobbyInfo = {
      isMsg = true,
    },
    lobbyMatchInfo = {
      isMsg = true,
    },
    mailCache = {
      isMsg = true,
    },
    mallInfo = {
      isMsg = true,
    },
    mallWishList = {
      isMsg = true,
    },
    marqueeNoticeInfo = {
      isMsg = true,
    },
    masterPathData = {
      isMsg = true,
    },
    matchStatics = {
      isMsg = true,
    },
    matchTypeHistory = {
      isMsg = true,
    },
    matchUnlock = {
      isMap = true,
      keyName = "matchTypeId",
    },
    matchUnlock_deleted = {
      isMapDel = true,
      targetFieldName = "matchUnlock",
    },
    modNote = {
      isMsg = true,
    },
    modSettings = {
      isMsg = true,
    },
    money = {
      isMsg = true,
    },
    multiPlayerSquad = {
      isMsg = true,
    },
    newYearPilotInfo = {
      isMsg = true,
    },
    openedLevels = {
      isSet = true,
    },
    openedLevels_deleted = {
      isSetDel = true,
      targetFieldName = "openedLevels",
    },
    partyInfo = {
      isMsg = true,
    },
    passwordCodeDataList = {
      isMap = true,
      keyName = "taskId",
    },
    passwordCodeDataList_deleted = {
      isMapDel = true,
      targetFieldName = "passwordCodeDataList",
    },
    permitInfo = {
      isMsg = true,
    },
    pixuiRedDot = {
      isMap = true,
      keyName = "id",
    },
    pixuiRedDot_deleted = {
      isMapDel = true,
      targetFieldName = "pixuiRedDot",
    },
    playerBlessBagInfo = {
      isMsg = true,
    },
    playerEventData = {
      isMsg = true,
    },
    playerGameActionInfos = {
      isMsg = true,
    },
    playerLevelEstimation = {
      isMsg = true,
    },
    playerLoginPlat = {
      isSet = true,
    },
    playerLoginPlat_deleted = {
      isSetDel = true,
      targetFieldName = "playerLoginPlat",
    },
    playerPakPlayRecord = {
      isMap = true,
      keyName = "pakId",
    },
    playerPakPlayRecord_deleted = {
      isMapDel = true,
      targetFieldName = "playerPakPlayRecord",
    },
    playerProfileInfo = {
      isMsg = true,
    },
    playerPublicBasicInfo = {
      isMsg = true,
    },
    playerPublicEquipments = {
      isMsg = true,
    },
    playerPublicGameData = {
      isMsg = true,
    },
    playerPublicGameSettings = {
      isMsg = true,
    },
    playerPublicHistoryData = {
      isMsg = true,
    },
    playerPublicLiveStatus = {
      isMsg = true,
    },
    playerPublicProfileInfo = {
      isMsg = true,
    },
    playerPublicSceneData = {
      isMsg = true,
    },
    playerPublicSummaryInfo = {
      isMsg = true,
    },
    prayInfo = {
      isMsg = true,
    },
    publicChatInfo = {
      isMsg = true,
    },
    pushTopicInfo = {
      isMsg = true,
    },
    qaInvest = {
      isMap = true,
      keyName = "id",
    },
    qaInvest_deleted = {
      isMapDel = true,
      targetFieldName = "qaInvest",
    },
    qaInvestTag = {
      isMap = true,
      keyName = "id",
    },
    qaInvestTag_deleted = {
      isMapDel = true,
      targetFieldName = "qaInvestTag",
    },
    qqApplicationInfo = {
      isMsg = true,
    },
    qqbot = {
      isMsg = true,
    },
    raffleGroupInfo = {
      isMap = true,
      keyName = "id",
    },
    raffleGroupInfo_deleted = {
      isMapDel = true,
      targetFieldName = "raffleGroupInfo",
    },
    raffleInfo = {
      isMap = true,
      keyName = "id",
    },
    raffleInfo_deleted = {
      isMapDel = true,
      targetFieldName = "raffleInfo",
    },
    raffleStash = {
      isMap = true,
      keyName = "stashId",
    },
    raffleStash_deleted = {
      isMapDel = true,
      targetFieldName = "raffleStash",
    },
    rankData = {
      isMsg = true,
    },
    recentActivity = {
      isMsg = true,
    },
    rechargeInfo = {
      isMsg = true,
    },
    recommendMatchType = {
      isMsg = true,
    },
    redEnvelopeRainActs = {
      isMsg = true,
    },
    relationMapInfo = {
      isMsg = true,
    },
    returningInfo = {
      isMsg = true,
    },
    rewardCompensate = {
      isMsg = true,
    },
    rewardNtf = {
      isMap = true,
      keyName = "id",
    },
    rewardNtf_deleted = {
      isMapDel = true,
      targetFieldName = "rewardNtf",
    },
    rewardRetrieval = {
      isMsg = true,
    },
    richInfo = {
      isMsg = true,
    },
    roguelikeExtraInfo = {
      isMsg = true,
    },
    roguelikeInfo = {
      isMsg = true,
    },
    roguelikeTalentData = {
      isMsg = true,
    },
    roguelikeTaskData = {
      isMsg = true,
    },
    RoomInfo = {
      isMsg = true,
    },
    safetyCheck = {
      isMsg = true,
    },
    sceneInfo = {
      isMsg = true,
    },
    seasonInfo = {
      isMsg = true,
    },
    seasonReview = {
      isMsg = true,
    },
    secondaryPassword = {
      isMsg = true,
    },
    selfMessageSlip = {
      isMsg = true,
    },
    sendHokExperienceCard = {
    },
    shareGiftInfo = {
      isMsg = true,
    },
    snsInvitationAccepted = {
      isMap = true,
      keyName = "cfgId",
    },
    snsInvitationAccepted_deleted = {
      isMapDel = true,
      targetFieldName = "snsInvitationAccepted",
    },
    snsInvitationInfo = {
      isMap = true,
      keyName = "uuid",
    },
    snsInvitationInfo_deleted = {
      isMapDel = true,
      targetFieldName = "snsInvitationInfo",
    },
    specialFaceAttr = {
      isMap = true,
      keyName = "faceId",
    },
    specialFaceAttr_deleted = {
      isMapDel = true,
      targetFieldName = "specialFaceAttr",
    },
    specReward = {
      isMsg = true,
    },
    starPChatInfo = {
      isMsg = true,
    },
    starPInfo = {
      isMsg = true,
    },
    stickFriends = {
      isMap = true,
      keyName = "scene",
    },
    stickFriends_deleted = {
      isMapDel = true,
      targetFieldName = "stickFriends",
    },
    streamSetting = {
      isMsg = true,
    },
    streamSettingList = {
      isMap = true,
      keyName = "streamPlatType",
    },
    streamSettingList_deleted = {
      isMapDel = true,
      targetFieldName = "streamSettingList",
    },
    targetEquipInfo = {
      isMap = true,
      keyName = "targetId",
    },
    targetEquipInfo_deleted = {
      isMapDel = true,
      targetFieldName = "targetEquipInfo",
    },
    taskInfo = {
      isMsg = true,
    },
    tradingCardData = {
      isMsg = true,
    },
    tycFpsSettings = {
      isMap = true,
      keyName = "gameMode",
    },
    tycFpsSettings_deleted = {
      isMapDel = true,
      targetFieldName = "tycFpsSettings",
    },
    ugcAccountInfo = {
      isMsg = true,
    },
    ugcActivityInfo = {
      isMsg = true,
    },
    ugcBuyGoodsInfo = {
      isMsg = true,
    },
    ugcCollection = {
      isMsg = true,
    },
    ugcCoPlayInfo = {
      isMsg = true,
    },
    ugcDailyStage = {
      isMsg = true,
    },
    ugcGrowUpInfo = {
      isMsg = true,
    },
    ugcMapSetInfo = {
      isMsg = true,
    },
    ugcMatchInfo = {
      isMsg = true,
    },
    ugcMiniGamePlayInfo = {
      isMsg = true,
    },
    ugcOpInfo = {
      isMsg = true,
    },
    ugcSlotKeyInfo = {
      isMsg = true,
    },
    ugcStarWorld = {
      isMsg = true,
    },
    uid = {
    },
    unLockGameModeHistorySet = {
      isSet = true,
    },
    unLockGameModeHistorySet_deleted = {
      isSetDel = true,
      targetFieldName = "unLockGameModeHistorySet",
    },
    unLockGameModeSet = {
      isSet = true,
    },
    unLockGameModeSet_deleted = {
      isSetDel = true,
      targetFieldName = "unLockGameModeSet",
    },
    unLockLabels = {
      isMap = true,
      keyName = "id",
    },
    unLockLabels_deleted = {
      isMapDel = true,
      targetFieldName = "unLockLabels",
    },
    unLockPreparationWidgetSet = {
      isSet = true,
    },
    unLockPreparationWidgetSet_deleted = {
      isSetDel = true,
      targetFieldName = "unLockPreparationWidgetSet",
    },
    upgradeCheckHistoryBuyTimes = {
    },
    upgradeCheckLastDayBuyTime = {
    },
    userLabels = {
      isMap = true,
      keyName = "id",
    },
    userLabels_deleted = {
      isMapDel = true,
      targetFieldName = "userLabels",
    },
    waitSettlementBattle = {
      isMap = true,
      keyName = "battleId",
    },
    waitSettlementBattle_deleted = {
      isMapDel = true,
      targetFieldName = "waitSettlementBattle",
    },
    welfareData = {
      isMsg = true,
    },
    whiteListInfo = {
      isMap = true,
      keyName = "type",
    },
    whiteListInfo_deleted = {
      isMapDel = true,
      targetFieldName = "whiteListInfo",
    },
    wolfKillInfo = {
      isMsg = true,
    },
    wolfKillInteractions = {
      isMap = true,
      keyName = "pos",
    },
    wolfKillInteractions_deleted = {
      isMapDel = true,
      targetFieldName = "wolfKillInteractions",
    },
    wolfKillNewUserTips = {
      isMap = true,
      keyName = "eventId",
    },
    wolfKillNewUserTips_deleted = {
      isMapDel = true,
      targetFieldName = "wolfKillNewUserTips",
    },
    wolfKillSeasonRewardList = {
      isMap = true,
      keyName = "rewardTime",
    },
    wolfKillSeasonRewardList_deleted = {
      isMapDel = true,
      targetFieldName = "wolfKillSeasonRewardList",
    },
    xiaoWoInfo = {
      isMsg = true,
    },
    xlsWhiteList = {
      isMap = true,
      keyName = "moduleId",
    },
    xlsWhiteList_deleted = {
      isMapDel = true,
      targetFieldName = "xlsWhiteList",
    },
  },
  UserConcertData = {
    concertTicket = {
      isMap = true,
      keyName = "concertId",
    },
    concertTicket_deleted = {
      isMapDel = true,
      targetFieldName = "concertTicket",
    },
  },
  UserConcertTicketInfo = {
    concertId = {
    },
    expressionId = {
    },
    lightBoardId = {
    },
    starId = {
    },
    ticketId = {
    },
  },
  UserLabel = {
    expiredTime = {
    },
    id = {
    },
    numVals = {
      isAry = true,
    },
    numVals_is_cleared = {
      isAryClear = true,
      targetFieldName = "numVals",
    },
    strVals = {
      isAry = true,
    },
    strVals_is_cleared = {
      isAryClear = true,
      targetFieldName = "strVals",
    },
  },
  UserSeasonNoteBookAttr = {
    battleMetaDataDetail = {
      isMap = true,
      keyName = "dataId",
    },
    battleMetaDataDetail_deleted = {
      isMapDel = true,
      targetFieldName = "battleMetaDataDetail",
    },
    seasonId = {
    },
  },
  VariableData = {
    addCount = {
    },
    addTimes = {
    },
    recoverStopped = {
    },
    recoverTime = {
    },
    used = {
    },
  },
  VileplumeInfo = {
    eatCoinNumTotal = {
    },
    eatCoinNumUnconverted = {
    },
    eatenFlag = {
    },
    eatenItems = {
      isMap = true,
      keyName = "itemId",
    },
    eatenItems_deleted = {
      isMapDel = true,
      targetFieldName = "eatenItems",
    },
    eatenStartTimeMs = {
    },
    eatExpTotal = {
    },
    exp = {
    },
    level = {
    },
    protectCanceled = {
    },
    protectEndTime = {
    },
  },
  VillageAttr = {
    farmId = {
    },
    villageBasicInfo = {
      isMsg = true,
    },
    villagerHouses = {
      isMap = true,
      keyName = "buildingId",
    },
    villagerHouses_deleted = {
      isMapDel = true,
      targetFieldName = "villagerHouses",
    },
  },
  VillageBasicInfo = {
    createTime = {
    },
    lastRefreshTimeMs = {
    },
  },
  VillagePublicInfo = {
    villagerHouseVisitorInfos = {
      isMap = true,
      keyName = "buildingId",
    },
    villagerHouseVisitorInfos_deleted = {
      isMapDel = true,
      targetFieldName = "villagerHouseVisitorInfos",
    },
  },
  VillagerAcceptGiftInfo = {
    lastGiftTime = {
    },
    todayGiftCount = {
    },
  },
  VillagerClientCache = {
    k = {
    },
    v = {
    },
  },
  VillagerDailyFavorInfo = {
    interActAdd = {
      isMap = true,
      keyName = "actId",
    },
    interActAdd_deleted = {
      isMapDel = true,
      targetFieldName = "interActAdd",
    },
  },
  VillagerFestivalGiftInfo = {
    historyFestivalGift = {
      isMap = true,
      keyName = "giftId",
    },
    historyFestivalGift_deleted = {
      isMapDel = true,
      targetFieldName = "historyFestivalGift",
    },
  },
  VillagerGiftInfo = {
    farmWallet = {
    },
    lastNormalGiftRefreshTime = {
    },
    lastNormalGiftTrigTime = {
    },
    lastOnDemandGiftPresentTime = {
    },
    lastOnDemandGiftRefreshTime = {
    },
    lastOnDemandGiftTrigTime = {
    },
    onDemandGiftCDHour = {
    },
    shennongGiftTrigedLv = {
    },
    todayAcceptGiftCount = {
    },
    todayPresentGiftCount = {
    },
    walletLastSettleTime = {
    },
  },
  VillagerGiftItem = {
    itemId = {
    },
    itemNum = {
    },
  },
  VillagerHouse = {
    buildingId = {
    },
    houseAttr = {
      isMsg = true,
    },
  },
  VillagerHouseVisitorInfo = {
    buildingId = {
    },
    totalVisitCount = {
    },
  },
  VillagerPresentGiftInfo = {
    giftId = {
    },
    giftType = {
    },
    items = {
      isMap = true,
      keyName = "itemId",
    },
    items_deleted = {
      isMapDel = true,
      targetFieldName = "items",
    },
    lastGiftTime = {
    },
    prepared = {
    },
  },
  VillagerTriggeredGift = {
    giftId = {
    },
    giftType = {
    },
    items = {
      isMap = true,
      keyName = "itemId",
    },
    items_deleted = {
      isMapDel = true,
      targetFieldName = "items",
    },
    trigTime = {
    },
  },
  VisitCookInfo = {
    currentCookId = {
    },
    enterTimeMs = {
    },
    tlogInfo = {
      isMsg = true,
    },
  },
  VisitFarmInfo = {
    currentFarmId = {
    },
    enterTimeMs = {
    },
    tlogInfo = {
      isMsg = true,
    },
  },
  VisitFarmTlogInfo = {
    ABTestInfo = {
    },
    channel = {
    },
    enterSource = {
    },
    RcmdInfo = {
    },
    relationType = {
    },
    tabName = {
    },
  },
  VisitHouseInfo = {
    currentBuildingId = {
    },
    currentHouseId = {
    },
    currentRoomId = {
    },
    enterTimeMs = {
    },
    tlogInfo = {
      isMsg = true,
    },
  },
  VisitHouseTlogInfo = {
    ABTestInfo = {
    },
    AutoReconnect = {
    },
    enterSource = {
    },
    RcmdInfo = {
    },
    relationType = {
    },
    tabName = {
    },
  },
  VisitorFarmPartyInfo = {
    enterSource = {
    },
    farmPartyEnterTime = {
    },
  },
  VisitRichBoardInfo = {
    currentBoardId = {
    },
    enterTimeMs = {
    },
    tlogInfo = {
      isMsg = true,
    },
  },
  VisitRichTlogInfo = {
    enterSource = {
    },
  },
  VisitXiaoWoInfo = {
    currentXiaoWoId = {
    },
    currentXiaoWoType = {
    },
    sampleRoomConfId = {
    },
  },
  WaitBattleInfo = {
    battleId = {
    },
    createTimeMs = {
    },
  },
  WarmRoundBattleResultDb = {
    millTs = {
    },
    result = {
    },
  },
  WarmRoundInfoDb = {
    returningInfo = {
      isMsg = true,
    },
    scores = {
      isMap = true,
      keyName = "id",
    },
    scores_deleted = {
      isMapDel = true,
      targetFieldName = "scores",
    },
  },
  WarmRoundReturningInfoDb = {
    mainGamePlayedCnt = {
    },
    returningDays = {
    },
    warmRoundTimes = {
    },
  },
  WarmRoundScoreChangeDb = {
    changedScore = {
    },
    millTs = {
    },
  },
  WarmRoundScoreDb = {
    changes = {
      isMap = true,
      keyName = "millTs",
    },
    changes_deleted = {
      isMapDel = true,
      targetFieldName = "changes",
    },
    consecutiveNoChampionCnt = {
    },
    dayTriggered = {
      isMsg = true,
    },
    id = {
    },
    recentEnteredLevel = {
      isAry = true,
    },
    recentEnteredLevel_is_cleared = {
      isAryClear = true,
      targetFieldName = "recentEnteredLevel",
    },
  },
  WarmRoundTriggeredDb = {
    cnt = {
    },
    id = {
    },
  },
  WaterReward = {
    idx = {
    },
    items = {
      isMap = true,
      keyName = "itemID",
    },
    items_deleted = {
      isMapDel = true,
      targetFieldName = "items",
    },
  },
  WaterRewardItem = {
    itemID = {
    },
    itemNum = {
    },
  },
  WealthBankActivity = {
    checkInDays = {
      isMap = true,
      keyName = "weekDay",
    },
    checkInDays_deleted = {
      isMapDel = true,
      targetFieldName = "checkInDays",
    },
    deposit = {
    },
    depositReceivedNum = {
    },
    makeUpTimes = {
    },
  },
  WeekenIceBrokenData = {
    buyTimeMs = {
    },
    rewardItem = {
    },
    rewardKey = {
    },
    rewardState = {
    },
  },
  WeeklyTaskExtraInfo = {
    value = {
    },
  },
  WeekPakPlayRecord = {
    pakPlayTimes = {
    },
    weekStartTime = {
    },
  },
  WelfareAerospaceTechEdData = {
    bills = {
      isMap = true,
      keyName = "transactionNo",
    },
    bills_deleted = {
      isMapDel = true,
      targetFieldName = "bills",
    },
  },
  WelfareData = {
    aerospaceTechEdData = {
      isMsg = true,
    },
  },
  WelfareHistoryBill = {
    billNum = {
    },
    billType = {
    },
    recordTimeMs = {
    },
    retryTimes = {
    },
    transactionNo = {
    },
  },
  WerewolfFullReducedCartDataAttr = {
    commodityId = {
    },
    itemNums = {
    },
  },
  WerewolfFullReducedConf = {
    accumulatedAmount = {
    },
    couponID = {
    },
    extraGiftNum = {
    },
    limitNum = {
    },
    receiveFinalist = {
    },
    useTime = {
    },
  },
  WerewolfFullReducedConfData = {
    cartDataAttr = {
      isMap = true,
      keyName = "commodityId",
    },
    cartDataAttr_deleted = {
      isMapDel = true,
      targetFieldName = "cartDataAttr",
    },
    werewolfFullReducedConf = {
      isMap = true,
      keyName = "couponID",
    },
    werewolfFullReducedConf_deleted = {
      isMapDel = true,
      targetFieldName = "werewolfFullReducedConf",
    },
  },
  WhiteInfo = {
    beginTime = {
    },
    enable = {
    },
    endTime = {
    },
    moduleType = {
    },
  },
  WhiteListInfo = {
    closeTime = {
    },
    openTime = {
    },
    type = {
    },
  },
  WildCardInfo = {
    cardId = {
    },
    expireTimeMs = {
    },
    uuid = {
    },
  },
  WildCatRefreshInfo = {
    lastWildCatLeaveTime = {
    },
    nextRefreshCD = {
    },
  },
  WishActivityData = {
    code = {
    },
    giftIds = {
      isSet = true,
    },
    giftIds_deleted = {
      isSetDel = true,
      targetFieldName = "giftIds",
    },
    giftState = {
    },
    helpAddVal = {
    },
    helpedOpenIds = {
      isSet = true,
    },
    helpedOpenIds_deleted = {
      isSetDel = true,
      targetFieldName = "helpedOpenIds",
    },
    helpState = {
    },
    helpUid = {
    },
    helpUidList = {
      isSet = true,
    },
    helpUidList_deleted = {
      isSetDel = true,
      targetFieldName = "helpUidList",
    },
    lastResetTime = {
    },
    raffleTicketGetCnt = {
    },
    recordData = {
      isMap = true,
      keyName = "createTime",
    },
    recordData_deleted = {
      isMapDel = true,
      targetFieldName = "recordData",
    },
    recordDataByAward = {
      isMap = true,
      keyName = "createTime",
    },
    recordDataByAward_deleted = {
      isMapDel = true,
      targetFieldName = "recordDataByAward",
    },
    selfOpenId = {
    },
    todayBeHelpCnt = {
    },
    todayHelpCnt = {
    },
    wishVal = {
    },
  },
  WishAwardData = {
    id = {
    },
    itemCnt = {
    },
    itemId = {
    },
  },
  WishAwardRecordData = {
    awardInfo = {
      isMap = true,
      keyName = "id",
    },
    awardInfo_deleted = {
      isMapDel = true,
      targetFieldName = "awardInfo",
    },
    createTime = {
    },
    type = {
    },
  },
  WishingRecord = {
    amsPackageGroupId = {
    },
    createTime = {
    },
    wishDay = {
    },
  },
  WishingTreeActivityData = {
    reservationRewardState = {
    },
    treeId = {
    },
    wishingRecord = {
      isMap = true,
      keyName = "wishDay",
    },
    wishingRecord_deleted = {
      isMapDel = true,
      targetFieldName = "wishingRecord",
    },
    wishRewardState = {
    },
  },
  WishRecordData = {
    addWishVal = {
    },
    createTime = {
    },
    helperUid = {
    },
    name = {
    },
    type = {
    },
  },
  WishTaskData = {
    compCnt = {
    },
    taskId = {
    },
  },
  WolfHistoryData = {
    conditionId = {
    },
    id = {
    },
    itemRecord = {
      isMap = true,
      keyName = "k",
    },
    itemRecord_deleted = {
      isMapDel = true,
      targetFieldName = "itemRecord",
    },
    times = {
    },
    uid = {
    },
  },
  WolfKillInfo = {
    brawlLastSelected = {
    },
    hyperCoreClearTime = {
    },
    hyperCoreScore = {
    },
    initTreasureFlag = {
    },
    lv = {
    },
    lvLast = {
    },
    rentAni = {
      isMap = true,
      keyName = "id",
    },
    rentAni_deleted = {
      isMapDel = true,
      targetFieldName = "rentAni",
    },
    rentClearTime = {
    },
    rentFromAni = {
      isMsg = true,
    },
    rentFromVocation = {
      isMsg = true,
    },
    rentHighVersionItemList = {
      isMap = true,
      keyName = "itemId",
    },
    rentHighVersionItemList_deleted = {
      isMapDel = true,
      targetFieldName = "rentHighVersionItemList",
    },
    rentVocation = {
      isMap = true,
      keyName = "id",
    },
    rentVocation_deleted = {
      isMapDel = true,
      targetFieldName = "rentVocation",
    },
    treasureEquipInfo = {
      isMap = true,
      keyName = "id",
    },
    treasureEquipInfo_deleted = {
      isMapDel = true,
      targetFieldName = "treasureEquipInfo",
    },
    treasureNum = {
    },
    treasureNumAdd = {
    },
    treasureNumAll = {
    },
    treasureNumAllLast = {
    },
    treasureNumLast = {
    },
  },
  WolfKillNewUserTipsInfo = {
    eventId = {
    },
    num = {
    },
  },
  WolfKillRewardItem = {
    itemId = {
    },
    num = {
    },
    rewardTime = {
    },
  },
  WolfKillTreasureEquipInfo = {
    id = {
    },
    inUse = {
    },
    isNew = {
    },
    rewarded = {
    },
  },
  WolfKillTreasureRentFromInfo = {
    rentFromTime = {
    },
    rentFromUid = {
    },
    retFromFlag = {
    },
  },
  WolfKillTreasureRentHighVersionItemInfo = {
    expiredTime = {
    },
    itemId = {
    },
    itemNum = {
    },
    relation = {
    },
    rentType = {
    },
    sharePlayerUid = {
    },
  },
  WolfKillTreasureRentInfo = {
    id = {
    },
    rentTime = {
    },
    rentUid = {
    },
  },
  WolfReturnData = {
    coinGetRecord = {
      isMap = true,
      keyName = "k",
    },
    coinGetRecord_deleted = {
      isMapDel = true,
      targetFieldName = "coinGetRecord",
    },
    conditionGroup = {
      isMsg = true,
    },
    eventRecord = {
      isMap = true,
      keyName = "k",
    },
    eventRecord_deleted = {
      isMapDel = true,
      targetFieldName = "eventRecord",
    },
    newWolf = {
    },
    receiveChannel = {
    },
    receiveList = {
      isSet = true,
    },
    receiveList_deleted = {
      isSetDel = true,
      targetFieldName = "receiveList",
    },
    redClickOpen = {
    },
    sendUid = {
    },
    wolfHistory = {
      isMap = true,
      keyName = "id",
    },
    wolfHistory_deleted = {
      isMapDel = true,
      targetFieldName = "wolfHistory",
    },
    wolfReturnState = {
    },
    wolfRewardRecord = {
      isMap = true,
      keyName = "k",
    },
    wolfRewardRecord_deleted = {
      isMapDel = true,
      targetFieldName = "wolfRewardRecord",
    },
  },
  WolfTeamAwardRecordData = {
    createTime = {
    },
    itemCnt = {
    },
    itemId = {
    },
    type = {
    },
  },
  WolfTeamChestData = {
    itemCount = {
      isMap = true,
      keyName = "k",
    },
    itemCount_deleted = {
      isMapDel = true,
      targetFieldName = "itemCount",
    },
    lotteryDrawsCount = {
    },
    residueCount = {
    },
    teamId = {
    },
    todayCount = {
    },
    wolfTeamAwardRecordMap = {
      isMap = true,
      keyName = "createTime",
    },
    wolfTeamAwardRecordMap_deleted = {
      isMapDel = true,
      targetFieldName = "wolfTeamAwardRecordMap",
    },
  },
  WorldStarPBackPackId = {
    backInfo = {
      isMap = true,
      keyName = "type",
    },
    backInfo_deleted = {
      isMapDel = true,
      targetFieldName = "backInfo",
    },
    starpId = {
    },
  },
  XiaowoAttr = {
    liuYanMessageInfo = {
      isMsg = true,
    },
    redPacketInfo = {
      isMsg = true,
    },
    ugcCommonInfo = {
      isMsg = true,
    },
    ugcGroupIdList = {
      isMsg = true,
    },
    xiaoWoBasicInfo = {
      isMsg = true,
    },
    XiaoWoClientCloud = {
      isMap = true,
      keyName = "K",
    },
    XiaoWoClientCloud_deleted = {
      isMapDel = true,
      targetFieldName = "XiaoWoClientCloud",
    },
    xiaoWoDsInfo = {
      isMsg = true,
    },
    xiaoWoFarmingInfo = {
      isMsg = true,
    },
    xiaoWoHotInfo = {
      isMsg = true,
    },
    xiaoWoId = {
    },
    xiaoWoInteractInfo = {
      isMsg = true,
    },
    xiaoWoLayoutPublishRecordIndexInfo = {
      isMap = true,
      keyName = "recordId",
    },
    xiaoWoLayoutPublishRecordIndexInfo_deleted = {
      isMapDel = true,
      targetFieldName = "xiaoWoLayoutPublishRecordIndexInfo",
    },
    xiaoWoLevelInfo = {
      isMsg = true,
    },
    xiaoWoLikeInfo = {
      isMsg = true,
    },
    xiaoWoLockInfo = {
      isMsg = true,
    },
    xiaoWoMoneyTreeInfo = {
      isMsg = true,
    },
    xiaoWoOwnerInfo = {
      isMsg = true,
    },
    xiaoWoPunishSaveInfo = {
      isMsg = true,
    },
    xiaoWoSafeInfo = {
      isMsg = true,
    },
    xiaoWoSampleRoomInfo = {
      isMsg = true,
    },
    xiaoWoShareInfo = {
      isMsg = true,
    },
    xiaoWoStarInfo = {
      isMsg = true,
    },
    xiaoWoVisitorInfo = {
      isMsg = true,
    },
    xiaoWoWelcomeInfo = {
      isMsg = true,
    },
  },
  XiaoWoBasicInfo = {
    bucket = {
    },
    createTime = {
    },
    editID = {
    },
    editUgcMapMetaInfo = {
      isMap = true,
      keyName = "index",
    },
    editUgcMapMetaInfo_deleted = {
      isMapDel = true,
      targetFieldName = "editUgcMapMetaInfo",
    },
    image = {
      isMsg = true,
    },
    instruction = {
    },
    name = {
    },
    pubID = {
    },
    region = {
    },
    saveTime = {
    },
    svrId = {
    },
    templateId = {
    },
    ugcMapMetaInfo = {
      isMap = true,
      keyName = "index",
    },
    ugcMapMetaInfo_deleted = {
      isMapDel = true,
      targetFieldName = "ugcMapMetaInfo",
    },
    version = {
    },
    visitorListTag = {
    },
    xiaoWoId = {
    },
    xiaoWoType = {
    },
  },
  XiaoWoClientCache = {
    K = {
    },
    V = {
    },
  },
  XiaoWoComponent = {
    itemId = {
    },
    num = {
    },
  },
  XiaoWoDsAddr = {
    aliasId = {
    },
    compatID = {
    },
    createTime = {
    },
    desModInfo = {
    },
    dsAddr = {
    },
    dsaServiceId = {
    },
    dsaServiceName = {
    },
    dsAuthToken = {
    },
    fleetId = {
    },
    gamesessID = {
    },
    isLoal = {
    },
    logFile = {
    },
    pakVer = {
    },
    queryFail = {
    },
    state = {
    },
    visitorCount = {
    },
  },
  XiaoWoDsInfo = {
    dsAddr = {
      isMap = true,
      keyName = "compatID",
    },
    dsAddr_deleted = {
      isMapDel = true,
      targetFieldName = "dsAddr",
    },
  },
  XiaoWoFarmingInfo = {
    crops = {
      isMap = true,
      keyName = "objID",
    },
    crops_deleted = {
      isMapDel = true,
      targetFieldName = "crops",
    },
    lastMailWaterRewardTime = {
    },
    waterRewardForOwner = {
      isMap = true,
      keyName = "idx",
    },
    waterRewardForOwner_deleted = {
      isMapDel = true,
      targetFieldName = "waterRewardForOwner",
    },
  },
  XiaoWoHandbookInfo = {
    farmHandbook = {
      isMap = true,
      keyName = "confId",
    },
    farmHandbook_deleted = {
      isMapDel = true,
      targetFieldName = "farmHandbook",
    },
    farmingLevel = {
    },
  },
  XiaoWoHandbookItem = {
    confId = {
    },
    gotReward = {
      isSet = true,
    },
    gotReward_deleted = {
      isSetDel = true,
      targetFieldName = "gotReward",
    },
    totalCount = {
    },
  },
  XiaoWoHotInfo = {
    hotInfo = {
      isMap = true,
      keyName = "timeKey",
    },
    hotInfo_deleted = {
      isMapDel = true,
      targetFieldName = "hotInfo",
    },
  },
  XiaoWoInfo = {
    farmingInfo = {
      isMsg = true,
    },
    initFurnitureVersion = {
    },
    initFurnitureZeroFlag = {
    },
    layoutIDInfo = {
      isMap = true,
      keyName = "layoutID",
    },
    layoutIDInfo_deleted = {
      isMapDel = true,
      targetFieldName = "layoutIDInfo",
    },
    liuYanMessageInfo = {
      isMsg = true,
    },
    myXiaoWoInfo = {
      isMsg = true,
    },
    starXiaoWo = {
      isSet = true,
    },
    starXiaoWo_deleted = {
      isSetDel = true,
      targetFieldName = "starXiaoWo",
    },
    starXiaoWoHistory = {
      isSet = true,
    },
    starXiaoWoHistory_deleted = {
      isSetDel = true,
      targetFieldName = "starXiaoWoHistory",
    },
    todayLikeXiaoWo = {
      isSet = true,
    },
    todayLikeXiaoWo_deleted = {
      isSetDel = true,
      targetFieldName = "todayLikeXiaoWo",
    },
    todayShareXiaoWo = {
      isSet = true,
    },
    todayShareXiaoWo_deleted = {
      isSetDel = true,
      targetFieldName = "todayShareXiaoWo",
    },
    unlockedFurniture = {
      isSet = true,
    },
    unlockedFurniture_deleted = {
      isSetDel = true,
      targetFieldName = "unlockedFurniture",
    },
    visitXiaoWoInfo = {
      isMsg = true,
    },
    xiaoWoHandbookInfo = {
      isMsg = true,
    },
  },
  XiaoWoInteractInfo = {
    itemInteracts = {
      isMap = true,
      keyName = "itemUid",
    },
    itemInteracts_deleted = {
      isMapDel = true,
      targetFieldName = "itemInteracts",
    },
  },
  XiaoWoLayoutInfo = {
    bucket = {
    },
    clientVersion = {
    },
    component = {
      isMap = true,
      keyName = "itemId",
    },
    component_deleted = {
      isMapDel = true,
      targetFieldName = "component",
    },
    image = {
      isMsg = true,
    },
    layoutDesc = {
    },
    layoutID = {
    },
    layoutItemId = {
      isAry = true,
    },
    layoutItemId_is_cleared = {
      isAryClear = true,
      targetFieldName = "layoutItemId",
    },
    layoutItemNum = {
      isAry = true,
    },
    layoutItemNum_is_cleared = {
      isAryClear = true,
      targetFieldName = "layoutItemNum",
    },
    layoutName = {
    },
    region = {
    },
    resIds = {
      isAry = true,
    },
    resIds_is_cleared = {
      isAryClear = true,
      targetFieldName = "resIds",
    },
    saveTime = {
    },
    ugcGroupIdList = {
      isMsg = true,
    },
    ugcMapMetaInfo = {
      isMap = true,
      keyName = "index",
    },
    ugcMapMetaInfo_deleted = {
      isMapDel = true,
      targetFieldName = "ugcMapMetaInfo",
    },
  },
  XiaoWoLayoutPublishRecord = {
    clientVersion = {
    },
    componentId = {
      isAry = true,
    },
    componentId_is_cleared = {
      isAryClear = true,
      targetFieldName = "componentId",
    },
    componentNum = {
      isAry = true,
    },
    componentNum_is_cleared = {
      isAryClear = true,
      targetFieldName = "componentNum",
    },
    editUgcMapMetaInfo = {
      isMap = true,
      keyName = "index",
    },
    editUgcMapMetaInfo_deleted = {
      isMapDel = true,
      targetFieldName = "editUgcMapMetaInfo",
    },
    image = {
      isMsg = true,
    },
    recordId = {
    },
    resIds = {
      isAry = true,
    },
    resIds_is_cleared = {
      isAryClear = true,
      targetFieldName = "resIds",
    },
    saveTime = {
    },
    ugcGroupIdList = {
      isMsg = true,
    },
    ugcMapMetaInfo = {
      isMap = true,
      keyName = "index",
    },
    ugcMapMetaInfo_deleted = {
      isMapDel = true,
      targetFieldName = "ugcMapMetaInfo",
    },
  },
  XiaoWoLayoutPublishRecordIndexInfo = {
    recordId = {
    },
    saveTime = {
    },
  },
  XiaoWoLevelInfo = {
    beauty = {
    },
    level = {
    },
  },
  XiaoWoLikeInfo = {
    totalLikeCount = {
    },
  },
  XiaoWoLiuYanMessageInfo = {
    lastPullId = {
    },
    newNumber = {
    },
    permission = {
    },
    totalNumber = {
    },
  },
  XiaoWoLockInfo = {
    lockInfo = {
      isMap = true,
      keyName = "itemUid",
    },
    lockInfo_deleted = {
      isMapDel = true,
      targetFieldName = "lockInfo",
    },
  },
  XiaoWoMinuteHotInfo = {
    action = {
    },
    chat = {
    },
    like = {
    },
    star = {
    },
    timeKey = {
    },
    voice = {
    },
  },
  XiaoWoMoneyTreeInfo = {
    experience = {
    },
    lastPayTime = {
    },
    payTimesToday = {
    },
    plantLevel = {
    },
    shakeDropItems = {
      isMap = true,
      keyName = "treeUid",
    },
    shakeDropItems_deleted = {
      isMapDel = true,
      targetFieldName = "shakeDropItems",
    },
    trees = {
      isMap = true,
      keyName = "objID",
    },
    trees_deleted = {
      isMapDel = true,
      targetFieldName = "trees",
    },
    waterDropItems = {
      isMap = true,
      keyName = "treeUid",
    },
    waterDropItems_deleted = {
      isMapDel = true,
      targetFieldName = "waterDropItems",
    },
    waterDropItemsForOwner = {
      isMap = true,
      keyName = "dropKey",
    },
    waterDropItemsForOwner_deleted = {
      isMapDel = true,
      targetFieldName = "waterDropItemsForOwner",
    },
  },
  XiaoWoOwnerInfo = {
    atHome = {
    },
    online = {
    },
    partyOpen = {
    },
  },
  XiaoWoPlayerLiuYanMessageInfo = {
    bannedTime = {
    },
    dayRecord = {
      isMap = true,
      keyName = "xiaoWoId",
    },
    dayRecord_deleted = {
      isMapDel = true,
      targetFieldName = "dayRecord",
    },
  },
  XiaowoPublicInfo = {
    bucket = {
    },
    canWaterTime = {
    },
    createTime = {
    },
    cropCanWaterIntervals = {
      isMap = true,
      keyName = "dryTime",
    },
    cropCanWaterIntervals_deleted = {
      isMapDel = true,
      targetFieldName = "cropCanWaterIntervals",
    },
    image = {
      isMsg = true,
    },
    liuYanPermission = {
    },
    region = {
    },
    saveTime = {
    },
    totalLiuYanMessageNumber = {
    },
    UgcMapMetaInfo = {
      isMap = true,
      keyName = "index",
    },
    UgcMapMetaInfo_deleted = {
      isMapDel = true,
      targetFieldName = "UgcMapMetaInfo",
    },
    version = {
    },
    xiaoWoAtHome = {
    },
    xiaoWoBeauty = {
    },
    xiaoWoEditID = {
    },
    xiaoWoInstruction = {
    },
    xiaoWoLevel = {
    },
    xiaoWoLike = {
    },
    xiaoWoName = {
    },
    xiaoWoPubID = {
    },
    xiaoWoSafeInfo = {
      isMsg = true,
    },
    xiaoWoShareCount = {
    },
    xiaoWoStar = {
    },
    xiaoWoTemplateId = {
    },
    xiaoWoVisitorCount = {
    },
    xiaoWoVisitTotalCount = {
    },
  },
  XiaoWoPunish = {
    enable = {
    },
    endTime = {
    },
    reason = {
    },
  },
  XiaoWoPunishSaveInfo = {
    publishRecords = {
      isMap = true,
      keyName = "recordId",
    },
    publishRecords_deleted = {
      isMapDel = true,
      targetFieldName = "publishRecords",
    },
  },
  XiaoWoRedPacketInfo = {
    redPackets = {
      isMap = true,
      keyName = "uuid",
    },
    redPackets_deleted = {
      isMapDel = true,
      targetFieldName = "redPackets",
    },
  },
  XiaoWoSafeInfo = {
    block = {
      isMsg = true,
    },
    clearInstructionAndImage = {
      isMsg = true,
    },
    hotRatio = {
    },
    hotRatioEndTime = {
    },
    lastVerifyStart = {
    },
    mapReset = {
    },
    mapResetToVersion = {
    },
    putDown = {
      isMsg = true,
    },
    verified = {
    },
    verifiedResult = {
    },
  },
  XiaoWoSampleRoomInfo = {
    confId = {
    },
    groupId = {
    },
    roomType = {
    },
  },
  XiaoWoShareInfo = {
    totalShareCount = {
    },
  },
  XiaoWoStarInfo = {
    totalStarCount = {
    },
  },
  XiaowoUgcMapMetaInfo = {
    index = {
    },
    isCoverCheckPass = {
    },
    layerId = {
    },
    msg = {
    },
    msgType = {
    },
    preMsg = {
    },
    processType = {
    },
    size = {
    },
    version = {
    },
  },
  XiaoWoVisitor = {
    blackUids = {
      isAry = true,
    },
    blackUids_is_cleared = {
      isAryClear = true,
      targetFieldName = "blackUids",
    },
    dsCheckID = {
    },
    dsGroupID = {
    },
    dsState = {
    },
    enterTimeStamp = {
    },
    farmTlogRequiredFields = {
      isMsg = true,
    },
    lastHeartBeatTimeStamp = {
    },
    playerPublicEquipments = {
      isMsg = true,
    },
    playerPublicProfileInfo = {
      isMsg = true,
    },
    startRaining = {
    },
    uid = {
    },
    version = {
    },
    visitorFarmPartyInfo = {
      isMsg = true,
    },
  },
  XiaoWoVisitorInfo = {
    totalVisitCount = {
    },
    visitCountAddHistory = {
      isMap = true,
      keyName = "uid",
    },
    visitCountAddHistory_deleted = {
      isMapDel = true,
      targetFieldName = "visitCountAddHistory",
    },
    visitors = {
      isMap = true,
      keyName = "uid",
    },
    visitors_deleted = {
      isMapDel = true,
      targetFieldName = "visitors",
    },
  },
  XiaoWoWelcomeInfo = {
    bannedTime = {
    },
    content = {
    },
  },
  XlsWhiteList = {
    moduleId = {
    },
    status = {
    },
  },
}
return desc
