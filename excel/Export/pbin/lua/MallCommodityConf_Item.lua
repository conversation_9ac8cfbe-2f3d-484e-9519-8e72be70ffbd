--com.tencent.wea.xlsRes.table_MallCommodityConf => excel/xls/S_商城_商品.xlsx: 商城-道具

local v0 = 1

local v1 = 208

local v2 = "解锁"

local v3 = "前往"

local v4 = 9

local data = {
[100002] = {
commodityId = 100002,
commodityName = "潮流服装染色膏",
coinType = 6,
price = 20,
beginTime = {
seconds = 1676390400
},
itemIds = {
200006
},
canGift = true,
addIntimacy = 3,
giftCoinType = 1,
giftPrice = 20
},
[100003] = {
commodityId = 100003,
commodityName = "时尚装饰调色盘",
coinType = 6,
price = 20,
beginTime = {
seconds = 1676390400
},
shopSort = 2,
itemIds = {
200008
},
canGift = true,
addIntimacy = 2,
giftCoinType = 1,
giftPrice = 20
},
[100004] = {
commodityId = 100004,
commodityName = "昵称修改卡",
coinType = 1,
price = 120,
beginTime = {
seconds = 1676390400
},
shopSort = 1,
itemIds = {
200010
},
canGift = true,
addIntimacy = 15,
giftCoinType = 1,
giftPrice = 120
},
[100005] = {
commodityId = 100005,
commodityName = "甜蜜初心",
coinType = 6,
price = 5,
beginTime = {
seconds = 1676390400
},
shopSort = 1,
itemIds = {
200014
}
},
[100006] = {
commodityId = 100006,
commodityName = "心心糖果",
coinType = 6,
price = 25,
beginTime = {
seconds = 1676390400
},
itemIds = {
200015
}
},
[100007] = {
commodityId = 100007,
commodityName = "心心宝瓶",
coinType = 6,
price = 50,
beginTime = {
seconds = 1676390400
},
itemIds = {
200016
}
},
[100008] = {
commodityId = 100008,
commodityName = "信心蜜罐",
coinType = 6,
price = 100,
beginTime = {
seconds = 1676390400
},
itemIds = {
200017
}
},
[100009] = {
commodityId = 100009,
commodityName = "星光烟花",
coinType = 6,
price = 30,
beginTime = {
seconds = 1734170400
},
endTime = {
seconds = 1736092799
},
itemIds = {
1010
}
},
[100010] = {
commodityId = 100010,
commodityName = "迎春红包",
coinType = 1,
price = 10,
beginTime = {
seconds = 1707235200
},
endTime = {
seconds = 1708790399
},
itemIds = {
320034
},
canGift = true,
addIntimacy = 1,
giftCoinType = 1,
giftPrice = 10
},
[100016] = {
commodityId = 100016,
commodityName = "迎春红包",
coinType = 6,
price = 20,
beginTime = {
seconds = 1676390400
},
endTime = {
seconds = 1708790399
},
itemIds = {
320017
},
canGift = true,
addIntimacy = 5,
giftCoinType = 1,
giftPrice = 50
},
[100017] = {
commodityId = 100017,
commodityName = "载具喷涂罐",
coinType = 6,
price = 20,
beginTime = {
seconds = 1714665600
},
shopSort = 2,
minVersion = "1.2.100.1",
itemIds = {
200005
},
canGift = true,
addIntimacy = 2,
giftCoinType = 1,
giftPrice = 20
},
[102001] = {
commodityId = 102001,
commodityName = "星宝会员卡(30天)",
canDirectBuy = true,
itemIds = {
210000
},
canGift = true,
addIntimacy = 12,
giftPrice = 12,
NoShowInMall = 1,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
hideGiftCoinUI = 1
},
[102002] = {
commodityId = 102002,
commodityName = "山海赛季高级通行证",
coinType = 1,
price = 300,
beginTime = {
seconds = 1707926400
},
endTime = {
seconds = 1710431999
},
shopSort = 1,
jumpId = 208,
itemIds = {
201001
},
canGift = true,
addIntimacy = 30,
giftCoinType = 1,
giftPrice = 300,
noOwnedJumpText = v2,
ownedJumpText = v3,
giftChecker = {
checkType = "MGCT_TargetPermitType",
checkParam = {
1
}
},
NoShowInMall = 1,
giftJumpId = 9,
unlockJumpId = 208,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
hideGiftCoinUI = 1,
unlockNotOwnedJumpId = 208
},
[102003] = {
commodityId = 102003,
commodityName = "山海赛季高级通行证",
coinType = 1,
price = 600,
beginTime = {
seconds = 1707926400
},
endTime = {
seconds = 1710431999
},
shopSort = 1,
jumpId = 208,
itemIds = {
201002
},
canGift = true,
addIntimacy = 60,
giftCoinType = 1,
giftPrice = 600,
noOwnedJumpText = v2,
ownedJumpText = v3,
giftChecker = {
checkType = "MGCT_TargetPermitType",
checkParam = {
1
}
},
NoShowInMall = 1,
giftJumpId = 9,
unlockJumpId = 208,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
hideGiftCoinUI = 1,
unlockNotOwnedJumpId = 208
},
[102004] = {
commodityId = 102004,
commodityName = "昆仑赛季高级通行证",
coinType = 1,
price = 300,
beginTime = {
seconds = 1750953600
},
endTime = {
seconds = 1755791999
},
shopSort = 1,
jumpId = 208,
minVersion = "1.3.99.1",
itemIds = {
201003
},
canGift = true,
addIntimacy = 30,
giftCoinType = 1,
giftPrice = 300,
noOwnedJumpText = v2,
ownedJumpText = v3,
giftChecker = {
checkType = "MGCT_TargetBattlePassType",
checkParam = {
1,
1
}
},
giftJumpId = 208,
unlockJumpId = 208,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
hideGiftCoinUI = 1,
unlockNotOwnedJumpId = 208
},
[102005] = {
commodityId = 102005,
commodityName = "昆仑赛季豪华通行证",
coinType = 1,
price = 600,
beginTime = {
seconds = 1750953600
},
endTime = {
seconds = 1755791999
},
shopSort = 1,
jumpId = 208,
minVersion = "1.3.99.1",
itemIds = {
201004
},
canGift = true,
addIntimacy = 60,
giftCoinType = 1,
giftPrice = 600,
noOwnedJumpText = v2,
ownedJumpText = v3,
giftChecker = {
checkType = "MGCT_TargetBattlePassType",
checkParam = {
1,
2
}
},
giftJumpId = 208,
unlockJumpId = 208,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
hideGiftCoinUI = 1,
unlockNotOwnedJumpId = 208
},
[102007] = {
commodityId = 102007,
commodityName = "超能赛季高级通行证",
coinType = 1,
price = 300,
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
shopSort = 1,
jumpId = 208,
minVersion = "1.3.88.1",
itemIds = {
201005
},
canGift = true,
addIntimacy = 30,
giftCoinType = 1,
giftPrice = 300,
noOwnedJumpText = v2,
ownedJumpText = v3,
giftChecker = {
checkType = "MGCT_TargetPermitType",
checkParam = {
1
}
},
giftJumpId = 9,
unlockJumpId = 208,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
hideGiftCoinUI = 1,
unlockNotOwnedJumpId = 208
},
[102008] = {
commodityId = 102008,
commodityName = "超能赛季豪华通行证",
coinType = 1,
price = 600,
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
shopSort = 1,
jumpId = 208,
minVersion = "1.3.88.1",
itemIds = {
201006
},
canGift = true,
addIntimacy = 60,
giftCoinType = 1,
giftPrice = 600,
noOwnedJumpText = v2,
ownedJumpText = v3,
giftChecker = {
checkType = "MGCT_TargetPermitType",
checkParam = {
1
}
},
giftJumpId = 9,
unlockJumpId = 208,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
hideGiftCoinUI = 1,
unlockNotOwnedJumpId = 208
},
[102009] = {
commodityId = 102009,
commodityName = "星宝农场月卡",
coinType = 1,
price = 298,
beginTime = {
seconds = 1718726400
},
endTime = {
seconds = 1718812800
},
shopSort = 1,
itemIds = {
200103
}
},
[102014] = {
commodityId = 102014,
commodityName = "狼人赛季高级通行证",
coinType = 1,
price = 198,
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
shopSort = 1,
jumpId = 208,
minVersion = "1.3.88.1",
itemIds = {
201007
},
canGift = true,
addIntimacy = 20,
giftCoinType = 1,
giftPrice = 198,
noOwnedJumpText = v2,
ownedJumpText = v3,
giftChecker = {
checkType = "MGCT_TargetBattlePassType",
checkParam = {
2,
1
}
},
NoShowInMall = 1,
giftJumpId = 9,
unlockJumpId = 208,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
hideGiftCoinUI = 1,
unlockNotOwnedJumpId = 208
},
[102015] = {
commodityId = 102015,
commodityName = "狼人赛季豪华通行证",
coinType = 1,
price = 596,
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
shopSort = 1,
jumpId = 208,
minVersion = "1.3.88.1",
itemIds = {
201008
},
canGift = true,
addIntimacy = 60,
giftCoinType = 1,
giftPrice = 596,
noOwnedJumpText = v2,
ownedJumpText = v3,
giftChecker = {
checkType = "MGCT_TargetBattlePassType",
checkParam = {
2,
2
}
},
NoShowInMall = 1,
giftJumpId = 9,
unlockJumpId = 208,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
hideGiftCoinUI = 1,
unlockNotOwnedJumpId = 208
},
[102016] = {
commodityId = 102016,
commodityName = "节日特惠峡谷通行证",
coinType = 1,
price = 198,
beginTime = {
seconds = 1725984000
},
endTime = {
seconds = 1729180799
},
shopSort = 1,
jumpId = 208,
minVersion = "1.3.18.1",
itemIds = {
201009
},
canGift = true,
addIntimacy = 20,
giftCoinType = 1,
giftPrice = 198,
noOwnedJumpText = v2,
ownedJumpText = v3,
giftChecker = {
checkType = "MGCT_TargetBattlePassType",
checkParam = {
3,
1
}
},
NoShowInMall = 1,
giftJumpId = 9,
unlockJumpId = 208,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
hideGiftCoinUI = 1,
unlockNotOwnedJumpId = 208
},
[102017] = {
commodityId = 102017,
commodityName = "节日豪华峡谷通行证",
coinType = 1,
price = 396,
beginTime = {
seconds = 1725984000
},
endTime = {
seconds = 1729180799
},
shopSort = 1,
jumpId = 208,
minVersion = "1.3.18.1",
itemIds = {
201010
},
canGift = true,
addIntimacy = 40,
giftCoinType = 1,
giftPrice = 396,
noOwnedJumpText = v2,
ownedJumpText = v3,
giftChecker = {
checkType = "MGCT_TargetBattlePassType",
checkParam = {
3,
2
}
},
NoShowInMall = 1,
giftJumpId = 9,
unlockJumpId = 208,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
hideGiftCoinUI = 1,
unlockNotOwnedJumpId = 208
},
[102018] = {
commodityId = 102018,
commodityName = "狼人赛季高级通行证",
coinType = 1,
price = 198,
beginTime = {
seconds = 1750953600
},
endTime = {
seconds = 1755791999
},
shopSort = 1,
jumpId = 208,
minVersion = "1.3.99.1",
itemIds = {
201011
},
canGift = true,
addIntimacy = 20,
giftCoinType = 1,
giftPrice = 198,
noOwnedJumpText = v2,
ownedJumpText = v3,
giftChecker = {
checkType = "MGCT_TargetBattlePassType",
checkParam = {
2,
1
}
},
NoShowInMall = 1,
giftJumpId = 9,
unlockJumpId = 208,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
hideGiftCoinUI = 1,
unlockNotOwnedJumpId = 208
},
[102019] = {
commodityId = 102019,
commodityName = "狼人赛季豪华通行证",
coinType = 1,
price = 596,
beginTime = {
seconds = 1750953600
},
endTime = {
seconds = 1755791999
},
shopSort = 1,
jumpId = 208,
minVersion = "1.3.99.1",
itemIds = {
201012
},
canGift = true,
addIntimacy = 60,
giftCoinType = 1,
giftPrice = 596,
noOwnedJumpText = v2,
ownedJumpText = v3,
giftChecker = {
checkType = "MGCT_TargetBattlePassType",
checkParam = {
2,
2
}
},
NoShowInMall = 1,
giftJumpId = 9,
unlockJumpId = 208,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
hideGiftCoinUI = 1,
unlockNotOwnedJumpId = 208
},
[102020] = {
commodityId = 102020,
commodityName = "星钻赠送",
coinType = 1,
price = 1,
beginTime = {
seconds = 1723132800
},
shopSort = 1,
itemIds = {
1
},
canGift = true,
giftCoinType = 1,
giftPrice = 1,
NoShowInMall = 1,
onlyIosMiniGameCanDemand = true
},
[102021] = {
commodityId = 102021,
commodityName = "星愿币*1",
coinType = 1,
price = 10,
beginTime = {
seconds = 1676390400
},
shopSort = 1,
itemIds = {
2
},
NoShowInMall = 1
},
[102022] = {
commodityId = 102022,
commodityName = "星钻赠送",
coinType = 1,
price = 1,
beginTime = {
seconds = 1723132800
},
shopSort = 1,
itemIds = {
1
},
canGift = true,
giftCoinType = 1,
giftPrice = 1,
NoShowInMall = 1,
onlyIosMiniGameCanDemand = true
},
[102023] = {
commodityId = 102023,
commodityName = "峡谷相逢高级通行证",
coinType = 1,
price = 198,
beginTime = {
seconds = 1727020800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 208,
minVersion = "1.3.26.26",
itemIds = {
201013
},
canGift = true,
addIntimacy = 20,
giftCoinType = 1,
giftPrice = 198,
noOwnedJumpText = v2,
ownedJumpText = v3,
giftChecker = {
checkType = "MGCT_TargetBattlePassType",
checkParam = {
3,
1
}
},
NoShowInMall = 1,
giftJumpId = 9,
unlockJumpId = 208,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
hideGiftCoinUI = 1,
unlockNotOwnedJumpId = 208
},
[102024] = {
commodityId = 102024,
commodityName = "峡谷相逢豪华通行证",
coinType = 1,
price = 396,
beginTime = {
seconds = 1727020800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 208,
minVersion = "1.3.26.26",
itemIds = {
201014
},
canGift = true,
addIntimacy = 40,
giftCoinType = 1,
giftPrice = 396,
noOwnedJumpText = v2,
ownedJumpText = v3,
giftChecker = {
checkType = "MGCT_TargetBattlePassType",
checkParam = {
3,
2
}
},
NoShowInMall = 1,
giftJumpId = 9,
unlockJumpId = 208,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
hideGiftCoinUI = 1,
unlockNotOwnedJumpId = 208
},
[102025] = {
commodityId = 102025,
commodityName = "峡谷相逢S9高级通行证",
coinType = 1,
price = 198,
beginTime = {
seconds = 1731513600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 208,
minVersion = "1.3.36.1",
itemIds = {
201015
},
canGift = true,
addIntimacy = 20,
giftCoinType = 1,
giftPrice = 198,
noOwnedJumpText = v2,
ownedJumpText = v3,
giftChecker = {
checkType = "MGCT_TargetBattlePassType",
checkParam = {
3,
1
}
},
NoShowInMall = 1,
giftJumpId = 9,
unlockJumpId = 208,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
hideGiftCoinUI = 1,
unlockNotOwnedJumpId = 208
},
[102026] = {
commodityId = 102026,
commodityName = "峡谷相逢S9豪华通行证",
coinType = 1,
price = 398,
beginTime = {
seconds = 1731513600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 208,
minVersion = "1.3.36.1",
itemIds = {
201016
},
canGift = true,
addIntimacy = 40,
giftCoinType = 1,
giftPrice = 398,
noOwnedJumpText = v2,
ownedJumpText = v3,
giftChecker = {
checkType = "MGCT_TargetBattlePassType",
checkParam = {
3,
2
}
},
NoShowInMall = 1,
giftJumpId = 9,
unlockJumpId = 208,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
hideGiftCoinUI = 1,
unlockNotOwnedJumpId = 208
},
[102027] = {
commodityId = 102027,
commodityName = "战神颂歌礼盒",
coinType = 213,
price = 8,
beginTime = {
seconds = 1734062400
},
shopSort = 1,
minVersion = "1.3.37.37",
itemIds = {
310282
},
canGift = true,
addIntimacy = 1500,
giftCoinType = 213,
giftPrice = 8,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
noBuySelf = true,
cumuRecvNumMax = 1,
canPreviewPackage = true
},
[102028] = {
commodityId = 102028,
commodityName = "峡谷相逢S10高级通行证",
coinType = 1,
price = 198,
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 208,
minVersion = "1.3.68.1",
itemIds = {
201017
},
canGift = true,
addIntimacy = 20,
giftCoinType = 1,
giftPrice = 198,
noOwnedJumpText = v2,
ownedJumpText = v3,
giftChecker = {
checkType = "MGCT_TargetBattlePassType",
checkParam = {
3,
1
}
},
NoShowInMall = 1,
giftJumpId = 9,
unlockJumpId = 208,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
hideGiftCoinUI = 1,
unlockNotOwnedJumpId = 208
},
[102029] = {
commodityId = 102029,
commodityName = "峡谷相逢S10豪华通行证",
coinType = 1,
price = 596,
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 208,
minVersion = "1.3.68.1",
itemIds = {
201018
},
canGift = true,
addIntimacy = 60,
giftCoinType = 1,
giftPrice = 596,
noOwnedJumpText = v2,
ownedJumpText = v3,
giftChecker = {
checkType = "MGCT_TargetBattlePassType",
checkParam = {
3,
2
}
},
NoShowInMall = 1,
giftJumpId = 9,
unlockJumpId = 208,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
hideGiftCoinUI = 1,
unlockNotOwnedJumpId = 208
},
[102030] = {
commodityId = 102030,
commodityName = "云梦绮旅礼盒",
coinType = 225,
price = 33,
beginTime = {
seconds = 1735660800
},
shopSort = 1,
minVersion = "1.3.37.87",
itemIds = {
310289
},
canGift = true,
addIntimacy = 1500,
giftCoinType = 225,
giftPrice = 33,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
noBuySelf = true,
cumuRecvNumMax = 1,
canPreviewPackage = true
},
[102031] = {
commodityId = 102031,
commodityName = "天启圣谕礼盒",
coinType = 213,
price = 13,
beginTime = {
seconds = 1737993600
},
shopSort = 1,
minVersion = "1.3.68.52",
itemIds = {
310303
},
canGift = true,
addIntimacy = 2700,
giftCoinType = 213,
giftPrice = 13,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
noBuySelf = true,
cumuRecvNumMax = 1,
canPreviewPackage = true
},
[102032] = {
commodityId = 102032,
commodityName = "S12赛季升阶豪华道具",
coinType = 1,
price = 400,
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
shopSort = 1,
jumpId = 208,
minVersion = "1.3.88.1",
itemIds = {
201084
},
canGift = true,
addIntimacy = 40,
giftCoinType = 1,
giftPrice = 400,
noOwnedJumpText = v2,
ownedJumpText = v3,
giftChecker = {
checkType = "MGCT_TargetBattlePassType",
checkParam = {
1,
2
}
},
NoShowInMall = 0,
giftJumpId = 208,
unlockJumpId = 208,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
hideGiftCoinUI = 1,
unlockNotOwnedJumpId = 208
},
[102033] = {
commodityId = 102033,
commodityName = "S13赛季升阶豪华道具",
coinType = 1,
price = 400,
beginTime = {
seconds = 1750953600
},
endTime = {
seconds = 1755791999
},
shopSort = 1,
jumpId = 208,
minVersion = "1.3.99.1",
itemIds = {
201085
},
canGift = true,
addIntimacy = 40,
giftCoinType = 1,
giftPrice = 400,
noOwnedJumpText = v2,
ownedJumpText = v3,
giftChecker = {
checkType = "MGCT_TargetBattlePassType",
checkParam = {
1,
2
}
},
giftJumpId = 208,
unlockJumpId = 208,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
hideGiftCoinUI = 1,
unlockNotOwnedJumpId = 208
},
[102034] = {
commodityId = 102034,
commodityName = "峡谷相逢S13高级通行证",
coinType = 1,
price = 198,
beginTime = {
seconds = 1750953600
},
endTime = {
seconds = 1755791999
},
shopSort = 1,
jumpId = 208,
minVersion = "1.3.99.1",
itemIds = {
201019
},
canGift = true,
addIntimacy = 20,
giftCoinType = 1,
giftPrice = 198,
noOwnedJumpText = v2,
ownedJumpText = v3,
giftChecker = {
checkType = "MGCT_TargetBattlePassType",
checkParam = {
3,
1
}
},
NoShowInMall = 1,
giftJumpId = 9,
unlockJumpId = 208,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
hideGiftCoinUI = 1,
unlockNotOwnedJumpId = 208
},
[102035] = {
commodityId = 102035,
commodityName = "峡谷相逢S13豪华通行证",
coinType = 1,
price = 596,
beginTime = {
seconds = 1750953600
},
endTime = {
seconds = 1755791999
},
shopSort = 1,
jumpId = 208,
minVersion = "1.3.99.1",
itemIds = {
201020
},
canGift = true,
addIntimacy = 60,
giftCoinType = 1,
giftPrice = 596,
noOwnedJumpText = v2,
ownedJumpText = v3,
giftChecker = {
checkType = "MGCT_TargetBattlePassType",
checkParam = {
3,
2
}
},
NoShowInMall = 1,
giftJumpId = 9,
unlockJumpId = 208,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
hideGiftCoinUI = 1,
unlockNotOwnedJumpId = 208
},
[102036] = {
commodityId = 102036,
commodityName = "峡谷相逢S12高级通行证",
coinType = 1,
price = 198,
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
shopSort = 1,
jumpId = 208,
minVersion = "1.3.88.1",
itemIds = {
201021
},
canGift = true,
addIntimacy = 20,
giftCoinType = 1,
giftPrice = 198,
noOwnedJumpText = v2,
ownedJumpText = v3,
giftChecker = {
checkType = "MGCT_TargetBattlePassType",
checkParam = {
3,
1
}
},
NoShowInMall = 1,
giftJumpId = 9,
unlockJumpId = 208,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
hideGiftCoinUI = 1,
unlockNotOwnedJumpId = 208
},
[102037] = {
commodityId = 102037,
commodityName = "峡谷相逢S12豪华通行证",
coinType = 1,
price = 596,
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
shopSort = 1,
jumpId = 208,
minVersion = "1.3.88.1",
itemIds = {
201022
},
canGift = true,
addIntimacy = 60,
giftCoinType = 1,
giftPrice = 596,
noOwnedJumpText = v2,
ownedJumpText = v3,
giftChecker = {
checkType = "MGCT_TargetBattlePassType",
checkParam = {
3,
2
}
},
NoShowInMall = 1,
giftJumpId = 9,
unlockJumpId = 208,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
hideGiftCoinUI = 1,
unlockNotOwnedJumpId = 208
},
[102038] = {
commodityId = 102038,
commodityName = "峡谷通行证升阶豪华道具",
coinType = 1,
price = 398,
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
shopSort = 1,
jumpId = 208,
minVersion = "1.3.88.1",
itemIds = {
201086
},
canGift = true,
addIntimacy = 40,
giftCoinType = 1,
giftPrice = 400,
noOwnedJumpText = v2,
ownedJumpText = v3,
giftChecker = {
checkType = "MGCT_TargetBattlePassType",
checkParam = {
3,
2
}
},
NoShowInMall = 1,
giftJumpId = 208,
unlockJumpId = 208,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
hideGiftCoinUI = 1,
unlockNotOwnedJumpId = 208
},
[102039] = {
commodityId = 102039,
commodityName = "狼人赛季升阶豪华道具",
coinType = 1,
price = 398,
beginTime = {
seconds = 1750953600
},
endTime = {
seconds = 1755791999
},
shopSort = 1,
jumpId = 208,
minVersion = "1.3.99.1",
itemIds = {
201087
},
canGift = true,
addIntimacy = 40,
giftCoinType = 1,
giftPrice = 398,
noOwnedJumpText = v2,
ownedJumpText = v3,
giftChecker = {
checkType = "MGCT_TargetBattlePassType",
checkParam = {
2,
2
}
},
NoShowInMall = 0,
giftJumpId = 9,
unlockJumpId = 208,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
hideGiftCoinUI = 1,
unlockNotOwnedJumpId = 208
},
[102040] = {
commodityId = 102040,
commodityName = "狼人赛季升阶豪华道具",
coinType = 1,
price = 398,
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
shopSort = 1,
jumpId = 208,
minVersion = "1.3.88.1",
itemIds = {
201088
},
canGift = true,
addIntimacy = 40,
giftCoinType = 1,
giftPrice = 398,
noOwnedJumpText = v2,
ownedJumpText = v3,
giftChecker = {
checkType = "MGCT_TargetBattlePassType",
checkParam = {
2,
2
}
},
NoShowInMall = 0,
giftJumpId = 9,
unlockJumpId = 208,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
hideGiftCoinUI = 1,
unlockNotOwnedJumpId = 208
},
[102041] = {
commodityId = 102041,
commodityName = "天天领权益卡",
beginTime = {
seconds = 1748534400
},
itemIds = {
200814
},
NoShowInMall = 1
},
[102042] = {
commodityId = 102042,
commodityName = "狼人月卡",
coinType = 1,
price = 268,
shopSort = 1,
jumpId = 689,
minVersion = "1.3.88.113",
itemIds = {
200178
},
canGift = true,
addIntimacy = 40,
giftCoinType = 1,
giftPrice = 268,
noOwnedJumpText = v2,
ownedJumpText = v3,
NoShowInMall = 1,
giftJumpId = 689,
unlockJumpId = 689,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
hideGiftCoinUI = 1,
unlockNotOwnedJumpId = 689
},
[102043] = {
commodityId = 102043,
commodityName = "峡谷天天领权益卡",
beginTime = {
seconds = 1750953600
},
itemIds = {
200828
},
NoShowInMall = 1
},
[102044] = {
commodityId = 102044,
commodityName = "天穹登录礼",
coinType = 1,
price = 120,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1748707200
},
canDirectBuy = true,
itemIds = {
200830
},
NoShowInMall = 1
},
[102045] = {
commodityId = 102045,
commodityName = "缤纷市集炫彩包",
coinType = 6,
price = 1000,
limitType = "MCL_WeeklyLimit",
limitNum = 5,
beginTime = {
seconds = 1676390400
},
itemIds = {
290021
},
NoShowInMall = 1
},
[102046] = {
commodityId = 102046,
commodityName = "缤纷市集稀有包",
coinType = 6,
price = 1500,
limitType = "MCL_WeeklyLimit",
limitNum = 4,
beginTime = {
seconds = 1676390400
},
itemIds = {
290022
},
NoShowInMall = 1
},
[102047] = {
commodityId = 102047,
commodityName = "缤纷市集非凡包",
coinType = 6,
price = 2000,
limitType = "MCL_WeeklyLimit",
limitNum = 3,
beginTime = {
seconds = 1676390400
},
itemIds = {
290023
},
NoShowInMall = 1
},
[102048] = {
commodityId = 102048,
commodityName = "缤纷市集臻藏包",
coinType = 6,
price = 3000,
limitType = "MCL_WeeklyLimit",
limitNum = 2,
beginTime = {
seconds = 1676390400
},
itemIds = {
290024
},
NoShowInMall = 1
},
[102049] = {
commodityId = 102049,
commodityName = "缤纷市集超凡包",
coinType = 6,
price = 5000,
limitType = "MCL_WeeklyLimit",
limitNum = 1,
beginTime = {
seconds = 1676390400
},
itemIds = {
290025
},
NoShowInMall = 1
}
}

local mt = {
mallId = 15,
shopTag = {
4
},
gender = 0,
itemNums = {
1
},
canGift = false,
canDirectBuy = false,
onlyIosMiniGameCanDemand = false,
noBuySelf = false,
canPreviewPackage = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data